import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'dart:async';

import '../../../core/services/geocoding_service.dart';

class LocationSearchBar extends ConsumerStatefulWidget {
  final Function(double, double) onLocationSelected;
  final Function() onAddLocationPressed;
  final Function() onCurrentLocationPressed;

  const LocationSearchBar({
    super.key,
    required this.onLocationSelected,
    required this.onAddLocationPressed,
    required this.onCurrentLocationPressed,
  });

  @override
  ConsumerState<LocationSearchBar> createState() => _LocationSearchBarState();
}

class _LocationSearchBarState extends ConsumerState<LocationSearchBar> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  Timer? _debounce;

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Search for addresses using the search query
  Future<List<dynamic>> _searchAddresses(String query) async {
    if (query.length < 3) return [];
    
    final geocodingService = ref.read(geocodingServiceProvider);
    return await geocodingService.searchAddress(query);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Address search field with typeahead
          TypeAheadField(
            builder: (context, controller, focusNode) {
              return TextField(
                controller: controller,
                focusNode: focusNode,
                decoration: InputDecoration(
                  hintText: 'Search for a location...',
                  prefixIcon: const Icon(LucideIcons.search, size: 20),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
                  contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                  suffixIcon: controller.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, size: 20),
                          onPressed: () {
                            controller.clear();
                            setState(() {});
                          },
                        )
                      : null,
                ),
                onChanged: (query) {
                  _debounce?.cancel();
                  if (query.isNotEmpty) {
                    setState(() => _isSearching = true);
                    _debounce = Timer(const Duration(milliseconds: 500), () async {
                      if (mounted) {
                        setState(() => _isSearching = false);
                      }
                    });
                  }
                },
              );
            },
            suggestionsCallback: (pattern) async {
              if (pattern.isNotEmpty) {
                return await _searchAddresses(pattern);
              }
              return [];
            },
            itemBuilder: (context, suggestion) {
              return ListTile(
                leading: const Icon(LucideIcons.mapPin, size: 20),
                title: Text(suggestion.displayName),
              );
            },
            onSelected: (suggestion) {
              _searchController.text = suggestion.displayName;
              widget.onLocationSelected(
                suggestion.latitude,
                suggestion.longitude,
              );
              // Hide keyboard
              FocusScope.of(context).unfocus();
            },
          ),
            
          const SizedBox(height: 8),
          // Action buttons
          Row(
            children: [
              // Add Location button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.onAddLocationPressed,
                  icon: const Icon(LucideIcons.plus, size: 16),
                  label: const Text('Add Location'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Current Location button
              SizedBox(
                width: 48,
                child: IconButton(
                  onPressed: widget.onCurrentLocationPressed,
                  icon: const Icon(LucideIcons.locate, size: 20),
                  style: IconButton.styleFrom(
                    backgroundColor: theme.colorScheme.surfaceContainerHighest,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.all(12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
