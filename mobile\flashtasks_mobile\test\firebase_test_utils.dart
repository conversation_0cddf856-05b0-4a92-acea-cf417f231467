import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

// Mock Firebase Core Platform Implementation
class MockFirebasePlatform extends Mock
    with MockPlatformInterfaceMixin
    implements FirebasePlatform {
  @override
  FirebaseAppPlatform app([String name = defaultFirebaseAppName]) {
    return MockFirebaseAppPlatform();
  }
  
  @override
  List<FirebaseAppPlatform> get apps => <FirebaseAppPlatform>[];
  
  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    return MockFirebaseAppPlatform();
  }
}

// Mock Firebase App
class MockFirebaseAppPlatform extends Mock implements FirebaseAppPlatform {
  @override
  String get name => 'test-app';
  
  @override
  FirebaseOptions get options => const FirebaseOptions(
    apiKey: 'mock-api-key',
    appId: 'mock-app-id',
    messagingSenderId: 'mock-sender-id',
    projectId: 'mock-project-id',
  );
}

// Setup Firebase Mocks
Future<void> setupFirebaseCoreMocks() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  // Setup mock implementation
  FirebasePlatform.instance = MockFirebasePlatform();
}

// Mock Firebase Initialization for Testing
class FakeFirebaseApp extends Fake implements FirebaseApp {
  @override
  String get name => 'test-app';
  
  @override
  FirebaseOptions get options => const FirebaseOptions(
    apiKey: 'mock-api-key',
    appId: 'mock-app-id',
    messagingSenderId: 'mock-sender-id',
    projectId: 'mock-project-id',
  );
} 