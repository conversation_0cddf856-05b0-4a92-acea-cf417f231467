import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../models/task.dart';

class TaskListItemWidget extends StatelessWidget {
  final TaskListItem task;
  final VoidCallback? onTap;
  final VoidCallback onToggleCompletion;
  
  const TaskListItemWidget({
    super.key,
    required this.task,
    this.onTap,
    required this.onToggleCompletion,
  });
  
  String? _formatDueDate(DateTime? date) {
    if (date == null) return null;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dueDate = DateTime(date.year, date.month, date.day);
    
    if (dueDate.isAtSameMomentAs(today)) {
      return 'Today';
    } else if (dueDate.isAtSameMomentAs(tomorrow)) {
      return 'Tomorrow';
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }
  
  Color _getPriorityColor() {
    if (task.priority == null) return Colors.transparent;
    
    switch (task.priority) {
      case 'High':
        return Colors.red;
      case 'Medium':
        return Colors.orange;
      case 'Low':
        return Colors.yellow;
      default:
        return Colors.transparent;
    }
  }
  
  String _getPriorityText() {
    return task.priority ?? '';
  }
  
  Color _parseColorFromString(String colorStr) {
    try {
      if (colorStr.startsWith('#')) {
        // Handle hex color with # prefix
        if (colorStr.length == 7) { // #RRGGBB format
          return Color(int.parse('0xFF${colorStr.substring(1)}'));
        } else if (colorStr.length == 9) { // #AARRGGBB format
          return Color(int.parse('0x${colorStr.substring(1)}'));
        }
      } else if (colorStr.startsWith('0x') || colorStr.startsWith('0X')) {
        // Handle hex color with 0x prefix
        return Color(int.parse(colorStr));
      } else if (colorStr.length == 6) {
        // Handle hex color without # prefix (RRGGBB)
        return Color(int.parse('0xFF$colorStr'));
      }
    } catch (e) {
      print('Error parsing color: $colorStr - $e');
    }
    return Colors.blue[100]!; // Default color
  }
  
  @override
  Widget build(BuildContext context) {
    final dueDate = _formatDueDate(task.dueDate);
    final priorityColor = _getPriorityColor();
    final priorityText = _getPriorityText();
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      clipBehavior: Clip.antiAlias,
      elevation: 0, // Reduce elevation for flatter look like web app
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.white.withOpacity(0.1), // Add subtle border like web app's border-white/10
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Checkbox
            Transform.scale(
              scale: 1.2,
              child: Checkbox(
                value: task.completed,
                onChanged: (_) => onToggleCompletion(),
                shape: const CircleBorder(),
                activeColor: Colors.green,
              ),
            ),
            
            const Gap(12),
            
            // Task content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title with AI indicators
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          task.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            decoration: task.completed ? TextDecoration.lineThrough : null,
                            color: task.completed ? Colors.grey : Colors.black,
                          ),
                        ),
                      ),
                      // AI Autocorrection indicator
                      if (task.metadata.aiWasAutoCorrected == true)
                        const Padding(
                          padding: EdgeInsets.only(left: 4),
                          child: Icon(
                            LucideIcons.edit3,
                            size: 12,
                            color: Colors.blueAccent,
                          ),
                        ),
                      // AI Confidence indicator - low confidence
                      if (task.metadata.aiConfidence?.toLowerCase() == 'low')
                        const Padding(
                          padding: EdgeInsets.only(left: 4),
                          child: Icon(
                            LucideIcons.alertCircle,
                            size: 12,
                            color: Colors.orangeAccent,
                          ),
                        ),
                      // AI Clarification needed indicator
                      if (task.metadata.aiClarificationNeeded != null)
                        const Padding(
                          padding: EdgeInsets.only(left: 4),
                          child: Icon(
                            LucideIcons.helpCircle,
                            size: 12,
                            color: Colors.purpleAccent,
                          ),
                        ),
                    ],
                  ),
                  
                  // AI metadata details (if any)
                  if (task.metadata.aiWasAutoCorrected == true || 
                      task.metadata.aiConfidence?.toLowerCase() == 'low' ||
                      task.metadata.aiClarificationNeeded != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        children: [
                          Icon(
                            LucideIcons.brainCircuit,
                            size: 10,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'AI processed',
                            style: TextStyle(
                              fontSize: 10,
                              fontStyle: FontStyle.italic,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  // Task metadata (categories, due date, reminders)
                  if (task.categories.isNotEmpty || dueDate != null || priorityText.isNotEmpty || task.hasReminder)
                    const Gap(8),

                  // Categories section
                  if (task.categories.isNotEmpty)
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: task.categories.map((category) => 
                        Container(
                          margin: const EdgeInsets.only(right: 4, bottom: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: category.color != null ? 
                                  _parseColorFromString(category.color!).withOpacity(0.2) : 
                                  Colors.blue[100],
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: category.color != null ? 
                                    _parseColorFromString(category.color!) : 
                                    Colors.blue,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            category.name,
                            style: TextStyle(
                              fontSize: 11,
                              color: category.color != null ?
                                    _parseColorFromString(category.color!) :
                                    Colors.blue,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ).toList(),
                    ),
                  
                  if ((task.categories.isNotEmpty) && (dueDate != null || priorityText.isNotEmpty || task.hasReminder))
                    const Gap(8),
                  
                  // Additional metadata row (priority, due date, reminder)
                  Row(
                    children: [
                      // Priority
                      if (priorityText.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: priorityColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: priorityColor, width: 1),
                          ),
                          child: Text(
                            priorityText,
                            style: TextStyle(
                              fontSize: 12,
                              color: priorityColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      
                      if (priorityText.isNotEmpty && (dueDate != null || task.hasReminder))
                        const Gap(8),
                      
                      // Due date
                      if (dueDate != null)
                        Row(
                          children: [
                            const Icon(
                              Icons.event_outlined,
                              size: 14,
                              color: Colors.grey,
                            ),
                            const Gap(4),
                            Text(
                              dueDate,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),

                      // Reminder indicator
                      if (task.hasReminder)
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: Icon(
                            Icons.notifications_active_outlined,
                            size: 14,
                            color: Colors.amber[700],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
