#!/bin/bash

# Exit on any error
set -e

# Print current working directory and PATH for debugging
echo "Current directory: $(pwd)"
echo "PATH: $PATH"

# Install dependencies
yarn

# Explicitly install copyfiles globally and locally
npm install -g copyfiles
yarn add copyfiles --dev

# Export node bin to PATH
export PATH="$PATH:$(yarn bin)"
echo "Updated PATH: $PATH"

# Build the project
tsc
npx copyfiles -u 1 "src/**/*.yaml" dist

echo "Build completed successfully!" 