'use client';

import { useState, useEffect } from 'react';
import { format, startOfToday, isToday, isSameDay, addDays, isWithinInterval } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { TaskModel, TaskFilters } from '@/lib/types/task.model';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CalendarDays, Check, ExternalLink } from 'lucide-react';

// Define priority types since we can't use TaskPriority as a value
type TaskPriorityType = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | string;

const PRIORITIES: TaskPriorityType[] = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];

// Helper function to safely format date
const safeFormatDate = (date: Date | string | undefined | null, formatStr: string): string => {
  if (!date) return '';
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, formatStr);
  } catch (e) {
    console.error('Error formatting date:', e);
    return '';
  }
};

interface CalendarSidebarProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  tasks: TaskModel[];
  categories: { id: string; name: string; color: string }[];
  selectedCategories: string[];
  onCategoryToggle: (categoryId: string) => void;
  onTaskClick: (task: TaskModel) => void;
  onFilterChange?: (filters: TaskFilters) => void;
}

export function CalendarSidebar({
  selectedDate,
  onDateSelect,
  tasks,
  categories,
  selectedCategories,
  onCategoryToggle,
  onTaskClick,
  onFilterChange,
}: CalendarSidebarProps) {
  const [date, setDate] = useState<Date>(startOfToday());
  const [upcomingTasks, setUpcomingTasks] = useState<Array<TaskModel & { deadline?: string | Date | null }>>([]);
  const [showCompleted, setShowCompleted] = useState<boolean>(false);
  const [selectedPriorities, setSelectedPriorities] = useState<TaskPriorityType[]>([]);
  const [isFiltersApplied, setIsFiltersApplied] = useState<boolean>(false);

  // State for calendar sync dialog
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [calendarSyncStatus, setCalendarSyncStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [connectedCalendars, setConnectedCalendars] = useState<Array<{provider: string, email: string, lastSync?: Date}>>([]);

  // Function to handle connecting to Google Calendar
  const handleConnectGoogleCalendar = () => {
    // In a real implementation, this would redirect to Google OAuth flow
    // For now, we'll simulate a successful connection
    setCalendarSyncStatus('connecting');
    
    // Simulate API call delay
    setTimeout(() => {
      setCalendarSyncStatus('connected');
      setConnectedCalendars([{
        provider: 'google',
        email: '<EMAIL>',
        lastSync: new Date()
      }]);
      setIsSyncDialogOpen(false);
    }, 1500);
  };

  // Filter tasks for the next 7 days
  useEffect(() => {
    const today = startOfToday();
    const nextWeek = addDays(today, 7);
    
    const filtered = tasks.filter(task => {
      if (!task.deadline) return false;
      try {
        const taskDate = typeof task.deadline === 'string' ? new Date(task.deadline) : task.deadline;
        if (isNaN(taskDate.getTime())) return false;
        return isWithinInterval(taskDate, {
          start: today,
          end: nextWeek
        }) && (showCompleted ? true : !task.completed);
      } catch (e) {
        console.error('Error processing task date:', e);
        return false;
      }
    });
    
    setUpcomingTasks(filtered);
  }, [tasks, showCompleted]);

  const handleDateSelect = (newDate: Date | undefined) => {
    if (!newDate) return;
    if (newDate) {
      setDate(newDate);
      onDateSelect(newDate);
    }
  };
  
  // Handle priority toggle
  const handlePriorityToggle = (priority: TaskPriorityType) => {
    setSelectedPriorities(prev => {
      const newPriorities = prev.includes(priority) 
        ? prev.filter(p => p !== priority)
        : [...prev, priority];
      
      // Apply filters if onFilterChange is provided
      if (onFilterChange) {
        onFilterChange({
          priorities: newPriorities.length > 0 ? newPriorities as string[] : undefined,
          status: showCompleted ? 'all' : 'active'
        });
      }
      
      setIsFiltersApplied(newPriorities.length > 0 || showCompleted);
      return newPriorities;
    });
  };
  
  // Handle show completed toggle
  const handleShowCompletedToggle = (checked: boolean) => {
    setShowCompleted(checked);
    
    // Apply filters if onFilterChange is provided
    if (onFilterChange) {
      onFilterChange({
        priorities: selectedPriorities.length > 0 ? selectedPriorities as string[] : undefined,
        status: checked ? 'all' : 'active'
      });
    }
    
    setIsFiltersApplied(selectedPriorities.length > 0 || checked);
  };
  
  // Clear all filters
  const clearFilters = () => {
    setSelectedPriorities([]);
    setShowCompleted(false);
    
    if (onFilterChange) {
      onFilterChange({
        status: 'active'
      });
    }
    
    setIsFiltersApplied(false);
  };

  return (
    <div className="w-80 border-r p-4 flex flex-col h-full">
      {/* Mini Calendar - Temporarily disabled due to issues
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Calendar</h3>
          <CalendarIcon className="h-5 w-5 text-muted-foreground" />
        </div>
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          className="rounded-md border"
        />
      </div>
      */}

      {/* Calendar Sync */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">Calendar Sync</h3>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsSyncDialogOpen(true)}
            className="h-8 px-2"
          >
            <CalendarDays className="h-4 w-4 mr-1" />
            {calendarSyncStatus === 'connected' ? 'Manage' : 'Connect'}
          </Button>
        </div>
        
        {calendarSyncStatus === 'connected' && connectedCalendars.length > 0 && (
          <div className="text-sm text-gray-600 mb-2">
            {connectedCalendars.map((calendar, index) => (
              <div key={index} className="flex items-center mb-1">
                <Check className="h-3 w-3 text-green-500 mr-1" />
                <span>Connected to {calendar.provider} ({calendar.email})</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Filters</h3>
        
        <div className="space-y-3">
          <div>
            <h4 className="text-sm font-medium mb-2">Status</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="show-completed" 
                  checked={showCompleted} 
                  onCheckedChange={(checked) => handleShowCompletedToggle(checked === true)}
                />
                <Label htmlFor="show-completed">Show Completed</Label>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Priority</h4>
            <div className="space-y-2">
              {PRIORITIES.map((priority) => (
                <div key={priority} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`priority-${priority}`} 
                    checked={selectedPriorities.includes(priority)}
                    onCheckedChange={(checked) => {
                      if (checked) handlePriorityToggle(priority);
                      else handlePriorityToggle(priority);
                    }}
                  />
                  <Label 
                    htmlFor={`priority-${priority}`} 
                    className={cn(
                      "capitalize",
                      {
                        'text-blue-600 font-medium': priority === 'LOW',
                        'text-green-600 font-medium': priority === 'MEDIUM',
                        'text-yellow-600 font-medium': priority === 'HIGH',
                        'text-red-600 font-medium': priority === 'CRITICAL',
                      }
                    )}
                  >
                    {priority.toLowerCase()}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {categories.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Categories</h4>
              <div className="space-y-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      onCheckedChange={() => onCategoryToggle(category.id)}
                    />
                    <Label
                      htmlFor={`category-${category.id}`}
                      className="flex items-center"
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: category.color || '#888888' }}
                      />
                      {category.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Upcoming Tasks */}
      <div className="flex-1 flex flex-col">
        <h3 className="text-lg font-semibold mb-3">Upcoming</h3>
        <ScrollArea className="flex-1 pr-2">
          {upcomingTasks.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No upcoming tasks
            </p>
          ) : (
            <div className="space-y-2">
              {upcomingTasks.map((task) => (
                <div
                  key={task._id}
                  onClick={() => onTaskClick(task)}
                  className={cn(
                    'p-3 rounded-md border cursor-pointer hover:bg-accent transition-colors',
                    {
                      'border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800': task.deadline && isToday(new Date(task.deadline)),
                      'ring-2 ring-primary': task.deadline && isSameDay(new Date(task.deadline), selectedDate)
                    }
                  )}
                >
                  <div className="flex justify-between items-start">
                    <h4 className="font-medium">{task.title}</h4>
                    {task.deadline && (
                      <span className="text-xs text-muted-foreground">
                        {safeFormatDate(task.deadline, 'h:mm a')}
                      </span>
                    )}
                  </div>
                  {task.priority && (
                    <span className={cn(
                      'inline-block px-2 py-0.5 text-xs rounded-full mt-1',
                      {
                        'bg-blue-100 text-blue-800': task.priority?.toUpperCase() === 'LOW',
                        'bg-green-100 text-green-800': task.priority?.toUpperCase() === 'MEDIUM',
                        'bg-yellow-100 text-yellow-800': task.priority?.toUpperCase() === 'HIGH',
                        'bg-red-100 text-red-800': task.priority?.toUpperCase() === 'CRITICAL',
                        'bg-gray-100 text-gray-800': !task.priority
                      }
                    )}>
                      {task.priority?.toLowerCase?.() || 'none'}
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>
      {/* Calendar Sync Dialog */}
      <Dialog open={isSyncDialogOpen} onOpenChange={setIsSyncDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Calendar Integration</DialogTitle>
            <DialogDescription>
              Connect your external calendars to sync your tasks
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            {calendarSyncStatus === 'connected' ? (
              <div className="space-y-4">
                <div className="flex flex-col space-y-2 border rounded-md p-3 bg-gray-50">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <CalendarDays className="h-5 w-5 text-blue-500 mr-2" />
                      <div>
                        <p className="font-medium">Google Calendar</p>
                        <p className="text-sm text-gray-500">{connectedCalendars[0]?.email}</p>
                      </div>
                    </div>
                    <Button variant="destructive" size="sm">
                      Disconnect
                    </Button>
                  </div>
                  {connectedCalendars[0]?.lastSync && (
                    <p className="text-xs text-gray-500 mt-1">
                      Last synced: {connectedCalendars[0].lastSync.toLocaleString()}
                    </p>
                  )}
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="text-sm font-medium mb-2">Sync Settings</h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sync-new-tasks" defaultChecked />
                    <Label htmlFor="sync-new-tasks">Automatically sync new tasks with deadline</Label>
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <Checkbox id="sync-updates" defaultChecked />
                    <Label htmlFor="sync-updates">Sync task updates and deletions</Label>
                  </div>
                </div>
              </div>
            ) : calendarSyncStatus === 'connecting' ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                <p>Connecting to Google Calendar...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <Button 
                  onClick={handleConnectGoogleCalendar} 
                  className="w-full flex items-center justify-center py-6"
                >
                  <CalendarDays className="h-5 w-5 mr-2" />
                  Connect Google Calendar
                </Button>
                
                <div className="text-sm text-gray-500 space-y-2">
                  <p>Connecting your calendar allows you to:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Sync tasks with deadlines to your calendar</li>
                    <li>Keep task updates in sync across platforms</li>
                    <li>Receive calendar notifications for task deadlines</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
