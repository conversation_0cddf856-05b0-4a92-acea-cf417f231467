// mobile/flashtasks_mobile/web/firebase-messaging-sw.js
importScripts('https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js');

// Configuration will be passed from the main app thread
let firebaseConfigInternal;
let firebaseInitialized = false;

self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'FIREBASE_CONFIG' && event.data.config) {
    firebaseConfigInternal = event.data.config;
    console.log('[SW] Received Firebase config:', firebaseConfigInternal);
    if (!firebaseInitialized && firebaseConfigInternal) {
      try {
        firebase.initializeApp(firebaseConfigInternal);
        firebaseInitialized = true;
        console.log('[SW] Firebase initialized in Service Worker.');

        const messaging = firebase.messaging();
        messaging.onBackgroundMessage(function(payload) {
          console.log('[SW] Received background message: ', payload);
          const notificationTitle = payload.notification?.title || 'FlashTasks Notification';
          const notificationOptions = {
            body: payload.notification?.body || 'You have a new message.',
            icon: payload.notification?.icon || '/icons/Icon-192.png', // Ensure this path is correct relative to your web root
            data: payload.data || {},
          };
          return self.registration.showNotification(notificationTitle, notificationOptions);
        });
        console.log('[SW] Background message handler set up.');
      } catch (e) {
        console.error('[SW] Error initializing Firebase in SW:', e);
      }
    }
  }
});

self.addEventListener('install', (event) => {
  console.log('[SW] Install event');
});

self.addEventListener('activate', (event) => {
  console.log('[SW] Activate event');
  return self.clients.claim();
});

self.addEventListener('notificationclick', function(event) {
  console.log('[SW] Notification click Received.');

  event.notification.close();

  // This looks up the client instance opened at the time of the notification.
  event.waitUntil(
    clients.matchAll({
      type: 'window'
    }).then(function(clientList) {
      for (var i = 0; i < clientList.length; i++) {
        var client = clientList[i];
        if (client.url == '/' && 'focus' in client)
          return client.focus();
      }
      if (clients.openWindow) {
        return clients.openWindow('/');
      }
    })
  );
});
