import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:latlong2/latlong.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../../core/services/geocoding_service.dart';
import '../../../core/services/geolocation_service.dart';
import '../models/location.dart';

class MapWithSearch extends ConsumerStatefulWidget {
  final List<Location> locations;
  final Function(Location) onLocationSelected;
  final Function(double, double, String?) onAddLocation;

  const MapWithSearch({
    super.key,
    required this.locations,
    required this.onLocationSelected,
    required this.onAddLocation,
  });

  @override
  ConsumerState<MapWithSearch> createState() => _MapWithSearchState();
}

class _MapWithSearchState extends ConsumerState<MapWithSearch> {
  final MapController _mapController = MapController();
  final TextEditingController _searchController = TextEditingController();
  
  bool _isSearching = false;
  Timer? _debounce;
  String? _currentAddress;
  bool _isReverseGeocoding = false;

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Get user's current location and center map on it
  Future<void> _centerOnUserLocation() async {
    try {
      final coordinates = await ref.read(geolocationServiceProvider).getCurrentCoordinates();
      if (coordinates.latitude != null && coordinates.longitude != null) {
        _mapController.move(
          LatLng(coordinates.latitude!, coordinates.longitude!),
          15.0, // Zoom level
        );
        
        // Perform reverse geocoding to get the address
        _reverseGeocode(coordinates.latitude!, coordinates.longitude!);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not get current location')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    }
  }

  // Search for addresses using the search query
  Future<List<dynamic>> _searchAddresses(String query) async {
    if (query.length < 3) return [];
    
    final geocodingService = ref.read(geocodingServiceProvider);
    return await geocodingService.searchAddress(query);
  }

  // Reverse geocode to get address from coordinates
  Future<void> _reverseGeocode(double latitude, double longitude) async {
    if (_isReverseGeocoding) return;
    
    setState(() {
      _isReverseGeocoding = true;
    });
    
    try {
      final geocodingService = ref.read(geocodingServiceProvider);
      final results = await geocodingService.searchAddress('${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}');
      
      if (results.isNotEmpty) {
        setState(() {
          _currentAddress = results.first.displayName;
        });
      }
    } catch (e) {
      debugPrint('Error reverse geocoding: $e');
    } finally {
      setState(() {
        _isReverseGeocoding = false;
      });
    }
  }

  // Handle map position changed
  void _onPositionChanged(MapPosition position, bool hasGesture) {
    if (!hasGesture || position.center == null) return;
    
    // Debounce to avoid too many reverse geocoding requests
    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      final center = position.center!;
      _reverseGeocode(center.latitude, center.longitude);
    });
  }

  // Add location at current center
  void _addCurrentLocation() {
    final center = _mapController.center;
    widget.onAddLocation(center.latitude, center.longitude, _currentAddress);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: TypeAheadField(
            builder: (context, controller, focusNode) {
              return TextField(
                controller: controller,
                focusNode: focusNode,
                decoration: InputDecoration(
                  hintText: 'Search for a location...',
                  prefixIcon: const Icon(LucideIcons.search, size: 20),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                  contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                  suffixIcon: controller.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, size: 20),
                          onPressed: () {
                            controller.clear();
                            setState(() {});
                          },
                        )
                      : null,
                ),
                onChanged: (query) {
                  _debounce?.cancel();
                  if (query.isNotEmpty) {
                    setState(() => _isSearching = true);
                    _debounce = Timer(const Duration(milliseconds: 500), () async {
                      if (mounted) {
                        setState(() => _isSearching = false);
                      }
                    });
                  }
                },
              );
            },
            suggestionsCallback: (pattern) async {
              if (pattern.isNotEmpty) {
                return await _searchAddresses(pattern);
              }
              return [];
            },
            itemBuilder: (context, suggestion) {
              return ListTile(
                leading: const Icon(LucideIcons.mapPin, size: 20),
                title: Text(suggestion.displayName),
              );
            },
            onSelected: (suggestion) {
              _searchController.text = suggestion.displayName;
              
              // Center map on selected location
              _mapController.move(
                LatLng(suggestion.latitude, suggestion.longitude),
                15.0,
              );
              
              // Update current address
              setState(() {
                _currentAddress = suggestion.displayName;
              });
              
              // Hide keyboard
              FocusScope.of(context).unfocus();
            },
          ),
        ),
        
        // Map with central marker
        Expanded(
          child: Stack(
            children: [
              // Map
              FlutterMap(
                mapController: _mapController,
                options: MapOptions(
                  initialCenter: const LatLng(37.7749, -122.4194), // Default center
                  initialZoom: 13.0,
                  onMapReady: () {
                    // Center on user's location when map is ready
                    _centerOnUserLocation();
                  },
                  onPositionChanged: _onPositionChanged,
                ),
                children: [
                  // Base map layer
                  TileLayer(
                    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.flashtasks.app',
                  ),
                  
                  // Location markers
                  MarkerLayer(
                    markers: widget.locations.map((location) => Marker(
                      width: 40.0,
                      height: 40.0,
                      point: LatLng(location.latitude, location.longitude),
                      child: GestureDetector(
                        onTap: () => widget.onLocationSelected(location),
                        child: Icon(
                          LocationStyling.getIconForLocation(location),
                          color: LocationStyling.getColorForLocation(location, context),
                          size: 30,
                        ),
                      ),
                    )).toList(),
                  ),
                ],
              ),
              
              // Central marker (always in the center)
              const Center(
                child: Icon(
                  LucideIcons.mapPin,
                  color: Colors.red,
                  size: 36,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 2),
                      blurRadius: 3.0,
                      color: Color.fromARGB(128, 0, 0, 0),
                    ),
                  ],
                ),
              ),
              
              // Current address display
              if (_currentAddress != null)
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      _currentAddress!,
                      style: theme.textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              
              // Action buttons
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Add location button
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _addCurrentLocation,
                        icon: const Icon(LucideIcons.mapPin, size: 16),
                        label: const Text('Add Here'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Current location button
                    SizedBox(
                      width: 48,
                      child: IconButton(
                        onPressed: _centerOnUserLocation,
                        icon: const Icon(LucideIcons.locate, size: 20),
                        style: IconButton.styleFrom(
                          backgroundColor: theme.colorScheme.surfaceVariant,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Loading indicator for reverse geocoding
              if (_isReverseGeocoding)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      shape: BoxShape.circle,
                    ),
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
