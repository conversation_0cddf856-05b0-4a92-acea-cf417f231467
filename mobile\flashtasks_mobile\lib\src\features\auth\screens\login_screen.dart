import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:go_router/go_router.dart';
import '../providers/auth_provider.dart';

import '../../../core/storage/secure_storage.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscurePassword = true;
  bool _rememberMe = false; // Add remember me state

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  Future<void> _loadSavedCredentials() async {
    try {
      final secureStorage = SecureStorageService();
      final savedEmail = await secureStorage.read(key: 'saved_email') ?? '';
      final savedPassword = await secureStorage.read(key: 'saved_password') ?? '';
      final rememberMeString = await secureStorage.read(key: 'remember_me') ?? 'false';
      final rememberMe = rememberMeString == 'true';

      setState(() {
        _emailController.text = savedEmail;
        _passwordController.text = savedPassword;
        _rememberMe = rememberMe;
      });
    } catch (e) {
      // If there's an error loading credentials, just continue with empty fields
      print('Error loading saved credentials: $e');
    }
  }

  Future<void> _saveCredentials() async {
    try {
      final secureStorage = SecureStorageService();
      if (_rememberMe) {
        // Store credentials securely using encrypted storage
        await secureStorage.write(key: 'saved_email', value: _emailController.text);
        await secureStorage.write(key: 'saved_password', value: _passwordController.text);
        await secureStorage.write(key: 'remember_me', value: 'true');
      } else {
        // Clear stored credentials
        await secureStorage.delete(key: 'saved_email');
        await secureStorage.delete(key: 'saved_password');
        await secureStorage.write(key: 'remember_me', value: 'false');
      }
    } catch (e) {
      // If there's an error saving credentials, just continue
      print('Error saving credentials: $e');
    }
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return 'Enter a valid email address';
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    // Note: For login, we only check minimum length
    // The backend will validate the full password requirements
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }

    return null;
  }

  Future<void> _login() async {
    ref.read(authProvider.notifier).clearError();
    if (_formKey.currentState?.validate() != true) {
      return;
    }
    await _saveCredentials(); // Save credentials if needed
    final success = await ref.read(authProvider.notifier).login(
      email: _emailController.text.trim(),
      password: _passwordController.text.trim(),
    );

    // Navigation is handled by the router based on authState changes
    // If login failed, the error will be in authState.error
  }

  Future<void> _signInWithGoogle() async {
    // Clear any previous errors
    ref.read(authProvider.notifier).clearError();

    try {
      // Call the provider's Google Sign-In method
      await ref.read(authProvider.notifier).signInWithGoogle();

      // Navigation is handled by the router based on authState changes
      // If login failed, the error will be in authState.error
    } catch (e) {
      // This should be handled by the provider, but just in case
      print('Google Sign-In error: $e');
    }
  }

  void _navigateToRegister() {
    context.go('/register');
  }

  void _navigateToServerSettings() {
    // Direct navigation to server settings, bypassing auth
    context.push('/dashboard/settings/server');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);

    // Listen for auth errors to show snackbar
    ref.listen<AuthState>(authProvider, (previous, current) {
      if (previous?.error == null && current.error != null && mounted) {
        // Clear previous snackbars before showing a new one
        ScaffoldMessenger.of(context).removeCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(current.error!),
            backgroundColor: theme.colorScheme.error,
          ),
        );

        // Optional: Clear error in provider after showing snackbar
        // Future.delayed(Duration(seconds: 3), () => ref.read(authProvider.notifier).clearError());
      }
    });

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Logo or app title
                  Text(
                    'FlashTasks AI',
                    style: theme.textTheme.displaySmall,
                    textAlign: TextAlign.center,
                  ),

                  const Gap(48),

                  // Login form container with subtle card styling
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.onSurface.withOpacity(0.1),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Email field
                        TextFormField(
                          key: const Key('login_email_field'),
                          controller: _emailController,
                          decoration: const InputDecoration(
                            labelText: 'Email',
                            hintText: '<EMAIL>',
                            prefixIcon: Icon(LucideIcons.mail),
                          ),
                          keyboardType: TextInputType.emailAddress,
                          validator: _validateEmail,
                          textInputAction: TextInputAction.next,
                          enabled: !authState.isLoading,
                        ),

                        const Gap(20),

                        // Remember me checkbox
                        Row(
                          children: [
                            Checkbox(
                              value: _rememberMe,
                              onChanged: authState.isLoading
                                  ? null
                                  : (value) {
                                      setState(() {
                                        _rememberMe = value ?? false;
                                      });
                                    },
                            ),
                            const Text('Remember me'),
                          ],
                        ),

                        // Password field
                        TextFormField(
                          key: const Key('login_password_field'),
                          controller: _passwordController,
                          decoration: InputDecoration(
                            labelText: 'Password',
                            hintText: 'Enter your password', // Changed from masked dots to plain text
                            prefixIcon: const Icon(LucideIcons.lock),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword
                                    ? LucideIcons.eye
                                    : LucideIcons.eyeOff,
                                size: 20,
                              ),
                              onPressed: _togglePasswordVisibility,
                              splashRadius: 20,
                            ),
                          ),
                          obscureText: _obscurePassword,
                          validator: _validatePassword,
                          textInputAction: TextInputAction.done,
                          enabled: !authState.isLoading,
                          onFieldSubmitted: (_) => _login(),
                        ),

                        // Error message
                        if (authState.error != null) ...[
                          const Gap(16),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.error.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: theme.colorScheme.error.withOpacity(0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  LucideIcons.alertCircle,
                                  size: 18,
                                  color: theme.colorScheme.error,
                                ),
                                const Gap(8),
                                Expanded(
                                  child: Text(
                                    authState.error!,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.error,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        const Gap(32),

                        // Login button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: authState.isLoading ? null : _login,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: authState.isLoading
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Text('Login', style: TextStyle(fontSize: 16)),
                          ),
                        ),

                        const Gap(16),

                        // Divider with "or" text
                        Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: theme.colorScheme.onSurface.withOpacity(0.2),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              child: Text(
                                'or',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ),
                            Expanded(
                              child: Divider(
                                color: theme.colorScheme.onSurface.withOpacity(0.2),
                              ),
                            ),
                          ],
                        ),

                        const Gap(16),

                        // Google Sign-In button
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            key: const Key('google_signin_button'),
                            onPressed: authState.isLoading ? null : _signInWithGoogle,
                            icon: const Icon(LucideIcons.chrome, size: 24, color: Colors.blue),
                            label: const Text('Sign in with Google'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              minimumSize: const Size(double.infinity, 48),
                              side: BorderSide(
                                color: theme.colorScheme.onSurface.withOpacity(0.2),
                              ),
                            ),
                          ),
                        ),

                        const Gap(16),

                        Align(
                          alignment: Alignment.center,
                          child: TextButton(
                            onPressed: null, // Stubbed
                            child: Text(
                              'Forgot password?',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Gap(32),

                  // Sign Up Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Don't have an account?",
                        style: theme.textTheme.bodyMedium,
                      ),
                      TextButton(
                        onPressed: authState.isLoading ? null : _navigateToRegister,
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                        ),
                        child: const Text('Create an account', style: TextStyle(fontSize: 16)),
                      ),
                    ],
                  ),

                  // Server Settings Button (commented out for production)
                  /*
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: OutlinedButton.icon(
                      onPressed: _navigateToServerSettings,
                      icon: const Icon(LucideIcons.server, size: 16),
                      label: const Text('Having connection issues? Configure Server Settings'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: theme.colorScheme.primary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  */
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}