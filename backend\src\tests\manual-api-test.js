// Manual API test script
const axios = require('axios');

const API_URL = 'http://localhost:3001';
let authToken = null;

// Test user credentials - replace with valid credentials for your system
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Login to get auth token
async function login() {
  try {
    const response = await axios.post(`${API_URL}/api/auth/login`, testUser);
    console.log('Login response:', response.data);
    authToken = response.data.token;
    console.log('Auth token obtained:', authToken ? 'Yes' : 'No');
    return authToken;
  } catch (error) {
    console.error('Login failed:', error.response ? error.response.data : error.message);
    throw error;
  }
}

// Test category endpoints
async function testCategoryEndpoints() {
  try {
    console.log('\n--- Testing Category Endpoints ---');
    
    // Get categories
    console.log('\nGetting categories:');
    const getResponse = await axios.get(`${API_URL}/api/categories`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('Response status:', getResponse.status);
    console.log('Response structure:', JSON.stringify(getResponse.data, null, 2).substring(0, 300) + '...');
    
    // Create a category
    console.log('\nCreating a category:');
    const createResponse = await axios.post(
      `${API_URL}/api/categories`,
      { name: 'Test Category ' + Date.now(), color: '#FF5733' },
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    console.log('Response status:', createResponse.status);
    console.log('Response structure:', JSON.stringify(createResponse.data, null, 2));
    
    const categoryId = createResponse.data.data._id;
    
    // Update the category
    console.log('\nUpdating the category:');
    const updateResponse = await axios.put(
      `${API_URL}/api/categories/${categoryId}`,
      { name: 'Updated Test Category', color: '#33FF57' },
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    console.log('Response status:', updateResponse.status);
    console.log('Response structure:', JSON.stringify(updateResponse.data, null, 2));
    
    // Delete the category
    console.log('\nDeleting the category:');
    const deleteResponse = await axios.delete(
      `${API_URL}/api/categories/${categoryId}`,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    console.log('Response status:', deleteResponse.status);
    console.log('Response data:', deleteResponse.data || 'No content (expected for 204)');
    
  } catch (error) {
    console.error('Category test failed:', error.response ? error.response.data : error.message);
  }
}

// Test grocery endpoints
async function testGroceryEndpoints() {
  try {
    console.log('\n--- Testing Grocery Endpoints ---');
    
    // Get grocery items
    console.log('\nGetting grocery items:');
    const getResponse = await axios.get(`${API_URL}/api/grocery`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('Response status:', getResponse.status);
    console.log('Response structure:', JSON.stringify(getResponse.data, null, 2).substring(0, 300) + '...');
    
    // Add grocery items
    console.log('\nAdding grocery items:');
    const createResponse = await axios.post(
      `${API_URL}/api/grocery`,
      { items: [{ name: 'Test Grocery ' + Date.now() }] },
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    console.log('Response status:', createResponse.status);
    console.log('Response structure:', JSON.stringify(createResponse.data, null, 2));
    
    if (createResponse.data.data && createResponse.data.data.items && createResponse.data.data.items.length > 0) {
      const itemId = createResponse.data.data.items[0]._id;
      
      // Update the grocery item
      console.log('\nUpdating the grocery item:');
      const updateResponse = await axios.put(
        `${API_URL}/api/grocery/${itemId}`,
        { name: 'Updated Test Grocery', isChecked: true },
        { headers: { Authorization: `Bearer ${authToken}` } }
      );
      console.log('Response status:', updateResponse.status);
      console.log('Response structure:', JSON.stringify(updateResponse.data, null, 2));
      
      // Delete the grocery item
      console.log('\nDeleting the grocery item:');
      const deleteResponse = await axios.delete(
        `${API_URL}/api/grocery/${itemId}`,
        { headers: { Authorization: `Bearer ${authToken}` } }
      );
      console.log('Response status:', deleteResponse.status);
      console.log('Response data:', deleteResponse.data || 'No content (expected for 204)');
    }
    
  } catch (error) {
    console.error('Grocery test failed:', error.response ? error.response.data : error.message);
  }
}

// Run the tests
async function runTests() {
  try {
    await login();
    if (authToken) {
      await testCategoryEndpoints();
      await testGroceryEndpoints();
    }
  } catch (error) {
    console.error('Test suite failed:', error);
  }
}

runTests();
