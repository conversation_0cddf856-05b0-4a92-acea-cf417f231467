import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

void main() {
  runApp(const TestNavApp());
}

class TestNavApp extends StatelessWidget {
  const TestNavApp({super.key});

  @override
  Widget build(BuildContext context) {
    final router = GoRouter(
      initialLocation: '/login',
      routes: [
        GoRoute(
          path: '/login',
          builder: (context, state) => const TestLoginScreen(),
        ),
        GoRoute(
          path: '/register',
          builder: (context, state) => const TestRegisterScreen(),
        ),
      ],
    );

    return MaterialApp.router(
      routerConfig: router,
      title: 'Navigation Test',
    );
  }
}

class TestLoginScreen extends StatelessWidget {
  const TestLoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login Screen')),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            print('Attempting to navigate to register');
            context.push('/register');
          },
          child: const Text('Go to Register'),
        ),
      ),
    );
  }
}

class TestRegisterScreen extends StatelessWidget {
  const TestRegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register Screen')),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            context.go('/login');
          },
          child: const Text('Back to Login'),
        ),
      ),
    );
  }
}
