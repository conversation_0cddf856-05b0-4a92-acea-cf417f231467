# 📱 Mobile Shared Groceries Implementation Scope

## **🎯 Core Philosophy: FlashTasks Simplicity**

The mobile shared groceries implementation must maintain FlashTasks' core principle: **simple, fast input for voice and text**. Shared lists are a powerful secondary feature that enhances collaboration without compromising the primary quick-add experience.

### **Key Principles**
- ✅ **Quick Add Remains Simple**: Voice and text input stay as the primary, fastest interaction
- ✅ **Shared Lists via Popups**: No new bottom navigation items - accessed through contextual popups
- ✅ **Context-Aware but Unobtrusive**: Smart targeting without cluttering the interface
- ✅ **Familiar Patterns**: Follows existing mobile app UI patterns

---

## **📋 Phase 1: Data Models & Types (Foundation)**

### **1.1 Enhanced Grocery Models**

**File**: `lib/src/features/groceries/models/grocery_item.dart`
- **🔄 Enhance**: Add collaboration tracking fields

```dart
class GroceryItem {
  // ... existing fields

  // NEW: Collaboration tracking
  final CollaboratorInfo? lastModifiedBy;
  final CollaboratorInfo? checkedBy;
  final DateTime? checkedAt;
  final CollaboratorInfo? addedBy;
}

class CollaboratorInfo {
  final String id;
  final String name;
  final String email;
}
```

### **1.2 New Collaboration Models**

**Files to Create**:
- `lib/src/features/groceries/models/grocery_list.dart`
- `lib/src/features/groceries/models/grocery_invitation.dart`

**Key Models**:
```dart
class GroceryList {
  final String id;
  final String userId;
  final String? name;
  final bool isShared;
  final List<GroceryCollaborator> collaborators;
  final GroceryShareSettings shareSettings;
  final UserRole? userRole;
}

enum CollaboratorRole { editor, viewer }
enum UserRole { owner, editor, viewer }
```

---

## **📋 Phase 2: Enhanced Services (API Layer)**

### **2.1 Enhanced Grocery Service**

**File**: `lib/src/core/services/grocery_service.dart`
- **🔄 Enhance**: Add `listOwnerId` parameter to existing methods
- **➕ Add**: Collaboration methods

```dart
class GroceryService {
  // Enhanced existing methods
  Future<List<GroceryItem>> getGroceryItems({String? listOwnerId}) async;
  Future<List<GroceryItem>> addGroceryItems(List<Map<String, dynamic>> items, {String? listOwnerId}) async;

  // NEW: Collaboration methods
  Future<GroceryList> getGroceryList() async;
  Future<List<GroceryList>> getSharedLists() async;
  Future<List<GroceryListInvitation>> getPendingInvitations() async;
  Future<void> acceptInvitation(String token) async;
}
```

---

## **📋 Phase 3: Enhanced State Management (Providers)**

### **3.1 Enhanced Grocery Provider**

**File**: `lib/src/features/groceries/providers/grocery_provider.dart`
- **🔄 Enhance**: Add collaboration state

```dart
class GroceryState {
  // Existing fields
  final List<GroceryItem> items;
  final bool isLoading;
  final String? error;

  // NEW: Collaboration state
  final GroceryList? groceryList;
  final List<GroceryList> sharedLists;
  final List<GroceryListInvitation> pendingInvitations;
  final String? currentListOwnerId; // Track which list we're viewing
}
```

---

## **📋 Phase 4: UI Components (Maintaining Simplicity)**

### **4.1 Enhanced Quick Add (KEEP SIMPLE)**

**File**: `lib/src/features/dashboard/widgets/quick_add_sheet.dart`
- **🔄 Minimal Enhancement**: Only add subtle context indicator

```dart
// BEFORE: Simple input
[Add grocery item...        ] [🎤] [+]

// AFTER: Simple input + subtle context (when on shared list)
👥 [Add grocery item...        ] [🎤] [+]
```

**Key Requirements**:
- ✅ **Voice and text input remain primary and unchanged**
- ✅ **Only subtle visual indicator when on shared list**
- ✅ **No additional complexity in the input flow**
- ✅ **Context-aware targeting happens automatically**

### **4.2 Enhanced Grocery List Item**

**File**: `lib/src/features/groceries/widgets/grocery_list_item.dart`
- **🔄 Enhance**: Add subtle collaboration indicators

```dart
// Subtle collaboration info (only when relevant)
☐ Milk                            [⋮]
  Added by John • 2 hours ago          ← Small, subtle text
```

### **4.3 New Collaboration Widgets (Popup-Based)**

**Files to Create**:
- `lib/src/features/groceries/widgets/list_switcher_popup.dart`
- `lib/src/features/groceries/widgets/collaboration_bottom_sheet.dart`
- `lib/src/features/groceries/widgets/invitation_card.dart`

---

## **📋 Phase 5: Enhanced Screens (Minimal Changes)**

### **5.1 Enhanced Groceries Screen**

**File**: `lib/src/features/groceries/screens/groceries_screen.dart`
- **🔄 Minimal Enhancement**: Add subtle header and popup access

```dart
// NEW: Subtle list context header (only when on shared list)
┌─────────────────────────────────────────┐
│ 👥 Smith Family List        [⚙️] [👥]   │  ← Only shows for shared lists
└─────────────────────────────────────────┘

// UNCHANGED: Simple add item bar
┌─────────────────────────────────────────┐
│ [Add grocery item...        ] [🎤] [+]  │  ← Stays exactly the same
└─────────────────────────────────────────┘
```

**Key Requirements**:
- ✅ **Personal lists show no collaboration UI**
- ✅ **Shared lists show minimal context header**
- ✅ **Add item bar remains unchanged**
- ✅ **All collaboration features accessed via popups**

### **5.2 New Popup-Based Screens**

**Files to Create**:
- `lib/src/features/groceries/screens/shared_lists_popup.dart`
- `lib/src/features/groceries/screens/collaboration_settings_popup.dart`
- `lib/src/features/groceries/screens/invitation_management_popup.dart`

---

## **📱 Updated User Experience & Interface Design**

### **🏠 Enhanced Main Groceries Screen (Minimal Changes)**

#### **Personal List (NO CHANGES)**
```
┌─────────────────────────────────────────┐
│ ☰ Groceries                        [👤] │  ← Unchanged
├─────────────────────────────────────────┤
│ [Add grocery item...        ] [🎤] [+]  │  ← Unchanged
├─────────────────────────────────────────┤
│ ☐ Milk                            [⋮]  │  ← Unchanged
│ ☐ Bread                           [⋮]  │  ← Unchanged
│ ☑ Eggs                            [⋮]  │  ← Unchanged
└─────────────────────────────────────────┘
```

#### **Shared List (Minimal Context)**
```
┌─────────────────────────────────────────┐
│ ☰ Groceries                        [👤] │
├─────────────────────────────────────────┤
│ 👥 Smith Family List        [⚙️] [👥]   │  ← NEW: Subtle context header
├─────────────────────────────────────────┤
│ [Add grocery item...        ] [🎤] [+]  │  ← Unchanged input
├─────────────────────────────────────────┤
│ ☐ Milk                            [⋮]  │
│   Added by John • 2 hours ago          │  ← NEW: Subtle collaboration info
│ ☑ Bread                           [⋮]  │
│   ✓ Checked by Sarah • 1 hour ago      │  ← NEW: Check info
└─────────────────────────────────────────┘
```

### **🔄 Popup-Based Navigation (No Bottom Nav Changes)**

#### **1. List Switcher Popup** *(Access: Tap [👥] button)*
```
┌─────────────────────────────────────────┐
│ ●●● Switch List                         │
├─────────────────────────────────────────┤
│ ● 👤 My Grocery List                    │  ← Currently selected
│   Personal list • 8 items              │
│                                         │
│ ○ 👥 Smith Family Groceries             │
│   3 collaborators • 12 items           │
│                                         │
│ ────────────────────────────────────────│
│ [📧 View Invitations (2)]               │
│ [⚙️ Manage Sharing]                     │
└─────────────────────────────────────────┘
```

#### **2. Enhanced Quick Add (Context-Aware but Simple)**
```
┌─────────────────────────────────────────┐
│ ●●● Quick Add                           │
├─────────────────────────────────────────┤
│ 👥 Adding to: Smith Family List         │  ← Only shows when on shared list
│                                         │
│ What do you need?                       │
│ [Milk, bread, eggs...          ] [🎤]  │  ← UNCHANGED: Simple input
│                                         │
│ [Add Items]                             │  ← UNCHANGED: Simple action
└─────────────────────────────────────────┘
```

---

## **🎯 Key UX Principles (Updated)**

### **1. FlashTasks Core Maintained**
- ✅ **Quick Add Unchanged**: Voice and text input remain the fastest, primary interaction
- ✅ **No Navigation Bloat**: Shared lists don't add bottom navigation complexity
- ✅ **Simple First**: Personal lists show zero collaboration UI
- ✅ **Fast Input**: Adding items is always one tap + voice/text

### **2. Collaboration as Enhancement**
- ✅ **Popup-Based**: All collaboration features accessed through contextual popups
- ✅ **Subtle Indicators**: Shared list context shown minimally
- ✅ **Progressive Disclosure**: Advanced features don't clutter basic interface
- ✅ **Context-Aware**: Smart targeting without user complexity

### **3. Familiar Patterns**
- ✅ **Existing UI**: Follows current mobile app design patterns
- ✅ **Standard Interactions**: Uses familiar mobile interaction patterns
- ✅ **Consistent Experience**: Shared lists feel like natural extension

---

## **🚀 Implementation Priority**

### **Phase 1: Foundation** (Week 1-2)
- Enhanced data models
- Enhanced grocery service with `listOwnerId` support
- Enhanced grocery provider state

### **Phase 2: Minimal UI** (Week 3)
- Subtle collaboration indicators on grocery items
- Simple list context header (shared lists only)
- Enhanced quick add context awareness

### **Phase 3: Popup Features** (Week 4-5)
- List switcher popup
- Collaboration settings popup
- Invitation management popup

### **Phase 4: Polish** (Week 6)
- Real-time updates
- Push notifications for invitations
- Performance optimization

---

## **✅ Success Criteria**

1. **FlashTasks Simplicity Maintained**: Quick add remains as fast and simple as before
2. **Zero Navigation Bloat**: No new bottom navigation items added
3. **Context-Aware Intelligence**: Items automatically target correct list
4. **Seamless Collaboration**: Family members can collaborate without complexity
5. **Progressive Enhancement**: Personal lists show no collaboration UI

This scope ensures shared groceries enhance FlashTasks without compromising its core speed and simplicity! 🚀✨

---

## **📋 Detailed Implementation Plan**

### **Phase 1: Data Models & Types (Foundation)**

#### **1.1 Enhanced Grocery Models**

**File**: `lib/src/features/groceries/models/grocery_item.dart`
```dart
// Add collaboration tracking fields to existing GroceryItem class
class GroceryItem {
  // ... existing fields (id, name, isChecked, category, notes, quantity, createdAt, updatedAt)

  // NEW: Collaboration tracking
  final CollaboratorInfo? lastModifiedBy;
  final CollaboratorInfo? checkedBy;
  final DateTime? checkedAt;
  final CollaboratorInfo? addedBy;

  // Enhanced constructor and fromJson to handle new fields
}

class CollaboratorInfo {
  final String id;
  final String name;
  final String email;

  CollaboratorInfo({required this.id, required this.name, required this.email});

  factory CollaboratorInfo.fromJson(Map<String, dynamic> json) => CollaboratorInfo(
    id: json['_id'] ?? json['id'],
    name: json['name'],
    email: json['email'],
  );
}
```

#### **1.2 New Collaboration Models**

**File**: `lib/src/features/groceries/models/grocery_list.dart` *(NEW)*
```dart
class GroceryList {
  final String id;
  final String userId;
  final String? name;
  final bool isShared;
  final List<GroceryCollaborator> collaborators;
  final GroceryShareSettings shareSettings;
  final DateTime lastCollaborativeActivity;
  final int? collaboratorCount;
  final UserRole? userRole;
  final ItemCounts? itemCounts;
  final DateTime createdAt;
  final DateTime updatedAt;

  factory GroceryList.fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson();
}

class GroceryCollaborator {
  final CollaboratorUser userId;
  final CollaboratorRole role;
  final DateTime joinedAt;
  final CollaboratorUser invitedBy;
}

class GroceryShareSettings {
  final bool allowCollaboratorInvites;
  final bool requireApprovalForEdits;
  final bool notifyOnChanges;
}

enum CollaboratorRole { editor, viewer }
enum UserRole { owner, editor, viewer }

class ItemCounts {
  final int total;
  final int checked;
  final int unchecked;
}
```

**File**: `lib/src/features/groceries/models/grocery_invitation.dart` *(NEW)*
```dart
class GroceryListInvitation {
  final String id;
  final String groceryListId;
  final CollaboratorUser inviterUserId;
  final String inviteeEmail;
  final CollaboratorRole role;
  final InvitationStatus status;
  final String token;
  final DateTime expiresAt;
  final DateTime createdAt;
  final GroceryListInfo? groceryList;

  factory GroceryListInvitation.fromJson(Map<String, dynamic> json);
}

enum InvitationStatus { pending, accepted, declined, expired }

class GroceryListInfo {
  final String id;
  final String? name;
  final String userId;
}
```

### **Phase 2: Enhanced Services (API Layer)**

#### **2.1 Enhanced Grocery Service**

**File**: `lib/src/core/services/grocery_service.dart`
```dart
class GroceryService {
  final ApiClient _apiClient;

  // ENHANCED: Add listOwnerId parameter to existing methods
  Future<List<GroceryItem>> getGroceryItems({String? listOwnerId}) async {
    final params = listOwnerId != null ? {'listOwnerId': listOwnerId} : <String, dynamic>{};
    final response = await _apiClient.get(ApiConfig.groceriesEndpoint, queryParameters: params);
    // ... existing parsing logic
  }

  Future<List<GroceryItem>> addGroceryItems(List<Map<String, dynamic>> items, {String? listOwnerId}) async {
    final params = listOwnerId != null ? {'listOwnerId': listOwnerId} : <String, dynamic>{};
    final response = await _apiClient.post(
      ApiConfig.groceriesEndpoint,
      data: {'items': items},
      queryParameters: params,
    );
    // ... existing parsing logic
  }

  // NEW: Collaboration methods
  Future<GroceryList> getGroceryList() async {
    final response = await _apiClient.get(ApiConfig.groceryListEndpoint);
    return GroceryList.fromJson(response.data['data']);
  }

  Future<GroceryList> enableSharing(GroceryShareSettings shareSettings) async {
    final response = await _apiClient.post(
      ApiConfig.groceryListSharingEndpoint,
      data: shareSettings.toJson(),
    );
    return GroceryList.fromJson(response.data['data']);
  }

  Future<List<GroceryList>> getSharedLists() async {
    final response = await _apiClient.get(ApiConfig.sharedGroceryListsEndpoint);
    return (response.data['data'] as List)
        .map((item) => GroceryList.fromJson(item))
        .toList();
  }

  Future<List<GroceryListInvitation>> getPendingInvitations() async {
    final response = await _apiClient.get(ApiConfig.groceryInvitationsEndpoint);
    return (response.data['data'] as List)
        .map((item) => GroceryListInvitation.fromJson(item))
        .toList();
  }

  Future<void> acceptInvitation(String token) async {
    await _apiClient.post('${ApiConfig.groceryInvitationsEndpoint}/$token/accept');
  }

  Future<void> declineInvitation(String token) async {
    await _apiClient.post('${ApiConfig.groceryInvitationsEndpoint}/$token/decline');
  }

  Future<GroceryList> addCollaborator(String email, CollaboratorRole role) async {
    final response = await _apiClient.post(
      ApiConfig.groceryCollaboratorsEndpoint,
      data: {'email': email, 'role': role.name},
    );
    return GroceryList.fromJson(response.data['data']['list']);
  }
}
```

#### **2.2 Enhanced API Config**

**File**: `lib/src/core/config/api_config.dart`
```dart
class ApiConfig {
  // Existing endpoints...

  // NEW: Collaboration endpoints
  static const String groceryListEndpoint = '/api/groceries/list';
  static const String groceryListSharingEndpoint = '/api/groceries/list/sharing';
  static const String sharedGroceryListsEndpoint = '/api/groceries/shared-lists';
  static const String groceryInvitationsEndpoint = '/api/groceries/invitations';
  static const String groceryCollaboratorsEndpoint = '/api/groceries/collaborators';
}
```

### **Phase 3: Enhanced State Management (Providers)**

#### **3.1 Enhanced Grocery Provider**

**File**: `lib/src/features/groceries/providers/grocery_provider.dart`
```dart
class GroceryState {
  // Existing fields
  final List<GroceryItem> items;
  final bool isLoading;
  final String? error;
  final List<String> suggestions;
  final bool isLoadingSuggestions;
  final String? errorSuggestions;
  final GroceryInsights? insights;
  final bool isLoadingInsights;
  final String? errorInsights;

  // NEW: Collaboration state
  final GroceryList? groceryList;
  final List<GroceryList> sharedLists;
  final List<GroceryListInvitation> pendingInvitations;
  final bool isLoadingCollaboration;
  final String? collaborationError;
  final String? currentListOwnerId; // Track which list we're viewing

  // Enhanced copyWith method to include new fields
}

class GroceryNotifier extends StateNotifier<GroceryState> {
  final GroceryService _groceryService;

  // ENHANCED: Add listOwnerId support to existing methods
  Future<void> fetchGroceries({String? listOwnerId}) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final items = await _groceryService.getGroceryItems(listOwnerId: listOwnerId);
      state = state.copyWith(
        items: items,
        isLoading: false,
        currentListOwnerId: listOwnerId,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> addItem(String name, {String? quantity, String? categoryName}) async {
    // Use currentListOwnerId for context-aware adding
    final listOwnerId = state.currentListOwnerId;

    // ... existing optimistic update logic

    final addedItems = await _groceryService.addGroceryItems([itemData], listOwnerId: listOwnerId);

    // ... existing state update logic
  }

  // NEW: Collaboration methods
  Future<void> fetchGroceryList() async {
    state = state.copyWith(isLoadingCollaboration: true, collaborationError: null);
    try {
      final groceryList = await _groceryService.getGroceryList();
      state = state.copyWith(groceryList: groceryList, isLoadingCollaboration: false);
    } catch (e) {
      state = state.copyWith(isLoadingCollaboration: false, collaborationError: e.toString());
    }
  }

  Future<void> fetchSharedLists() async {
    try {
      final sharedLists = await _groceryService.getSharedLists();
      state = state.copyWith(sharedLists: sharedLists);
    } catch (e) {
      state = state.copyWith(collaborationError: e.toString());
    }
  }

  Future<void> fetchPendingInvitations() async {
    try {
      final invitations = await _groceryService.getPendingInvitations();
      state = state.copyWith(pendingInvitations: invitations);
    } catch (e) {
      state = state.copyWith(collaborationError: e.toString());
    }
  }

  Future<void> switchToSharedList(String listOwnerId) async {
    await fetchGroceries(listOwnerId: listOwnerId);
    await fetchGroceryList(); // Refresh collaboration info
  }

  Future<void> switchToMyList() async {
    await fetchGroceries(); // No listOwnerId = personal list
    await fetchGroceryList();
  }

  Future<void> acceptInvitation(String token) async {
    try {
      await _groceryService.acceptInvitation(token);
      await fetchPendingInvitations(); // Refresh invitations
      await fetchSharedLists(); // Refresh shared lists
    } catch (e) {
      state = state.copyWith(collaborationError: e.toString());
    }
  }
}
```

### **Phase 4: UI Components (Maintaining Simplicity)**

#### **4.1 Enhanced Quick Add (KEEP SIMPLE)**

**File**: `lib/src/features/dashboard/widgets/quick_add_sheet.dart`
```dart
class QuickAddSheet extends ConsumerStatefulWidget {
  @override
  ConsumerState<QuickAddSheet> createState() => _QuickAddSheetState();
}

class _QuickAddSheetState extends ConsumerState<QuickAddSheet> {
  @override
  Widget build(BuildContext context) {
    final groceryState = ref.watch(groceryProvider);

    return Container(
      child: Column(
        children: [
          // NEW: Subtle context indicator (only when on shared list)
          if (groceryState.currentListOwnerId != null)
            _buildListContextIndicator(groceryState),

          // UNCHANGED: Simple quick add form
          _buildQuickAddForm(),
        ],
      ),
    );
  }

  Widget _buildListContextIndicator(GroceryState groceryState) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(LucideIcons.users, size: 16, color: Colors.blue),
          SizedBox(width: 8),
          Text(
            'Adding to shared list',
            style: TextStyle(
              color: Colors.blue,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // UNCHANGED: Existing quick add form logic
  Widget _buildQuickAddForm() {
    // ... existing implementation stays exactly the same
  }
}
```

#### **4.2 Enhanced Grocery List Item**

**File**: `lib/src/features/groceries/widgets/grocery_list_item.dart`
```dart
class GroceryListItem extends StatelessWidget {
  final GroceryItem item;
  // ... existing parameters

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          // Existing item content
          _buildMainContent(),

          // NEW: Subtle collaboration info (only when relevant)
          if (_hasCollaborationInfo())
            _buildCollaborationInfo(),
        ],
      ),
    );
  }

  bool _hasCollaborationInfo() {
    return item.addedBy != null || item.checkedBy != null;
  }

  Widget _buildCollaborationInfo() {
    return Padding(
      padding: EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: Row(
        children: [
          if (item.addedBy != null) ...[
            Icon(LucideIcons.userPlus, size: 10, color: Colors.grey),
            SizedBox(width: 4),
            Text(
              'Added by ${item.addedBy!.name}',
              style: TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ],
          if (item.checkedBy != null) ...[
            if (item.addedBy != null) Text(' • ', style: TextStyle(fontSize: 10, color: Colors.grey)),
            Icon(LucideIcons.check, size: 10, color: Colors.grey),
            SizedBox(width: 4),
            Text(
              'Checked by ${item.checkedBy!.name}',
              style: TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ],
        ],
      ),
    );
  }
}
```

#### **4.3 New Popup-Based Widgets**

**File**: `lib/src/features/groceries/widgets/list_switcher_popup.dart` *(NEW)*
```dart
class ListSwitcherPopup extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final groceryState = ref.watch(groceryProvider);

    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Switch List',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),

          // Personal list option
          _buildListOption(
            context: context,
            ref: ref,
            icon: LucideIcons.user,
            title: 'My Grocery List',
            subtitle: 'Personal list • ${groceryState.items.length} items',
            isSelected: groceryState.currentListOwnerId == null,
            onTap: () => _switchToPersonalList(ref, context),
          ),

          // Shared lists
          ...groceryState.sharedLists.map((sharedList) => _buildListOption(
            context: context,
            ref: ref,
            icon: LucideIcons.users,
            title: sharedList.name ?? 'Shared List',
            subtitle: '${sharedList.collaboratorCount} collaborators • ${sharedList.itemCounts?.total ?? 0} items',
            isSelected: groceryState.currentListOwnerId == sharedList.userId,
            onTap: () => _switchToSharedList(ref, context, sharedList.userId),
          )),

          SizedBox(height: 16),
          Divider(),

          // Management options
          TextButton.icon(
            icon: Icon(LucideIcons.mail),
            label: Text('View Invitations (${groceryState.pendingInvitations.length})'),
            onPressed: () => _showInvitations(context, ref),
          ),

          TextButton.icon(
            icon: Icon(LucideIcons.settings),
            label: Text('Manage Sharing'),
            onPressed: () => _showCollaborationSettings(context, ref),
          ),
        ],
      ),
    );
  }

  Widget _buildListOption({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Radio<bool>(
        value: true,
        groupValue: isSelected,
        onChanged: (_) => onTap(),
      ),
      title: Row(
        children: [
          Icon(icon, size: 16),
          SizedBox(width: 8),
          Text(title),
        ],
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  void _switchToPersonalList(WidgetRef ref, BuildContext context) {
    ref.read(groceryProvider.notifier).switchToMyList();
    Navigator.of(context).pop();
  }

  void _switchToSharedList(WidgetRef ref, BuildContext context, String listOwnerId) {
    ref.read(groceryProvider.notifier).switchToSharedList(listOwnerId);
    Navigator.of(context).pop();
  }
}
```

**File**: `lib/src/features/groceries/widgets/collaboration_bottom_sheet.dart` *(NEW)*
```dart
class CollaborationBottomSheet extends ConsumerStatefulWidget {
  final GroceryList groceryList;

  const CollaborationBottomSheet({required this.groceryList, super.key});

  @override
  ConsumerState<CollaborationBottomSheet> createState() => _CollaborationBottomSheetState();
}

class _CollaborationBottomSheetState extends ConsumerState<CollaborationBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Collaboration Settings',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),

          _buildSharingToggle(),
          SizedBox(height: 16),

          if (widget.groceryList.isShared) ...[
            _buildShareSettings(),
            SizedBox(height: 16),
            _buildCollaboratorsList(),
            SizedBox(height: 16),
            _buildAddCollaboratorButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildSharingToggle() {
    return SwitchListTile(
      title: Text('Share this list'),
      subtitle: Text('Allow others to collaborate'),
      value: widget.groceryList.isShared,
      onChanged: (value) => _toggleSharing(value),
    );
  }

  Widget _buildCollaboratorsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Collaborators (${widget.groceryList.collaborators.length})',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: 8),
        ...widget.groceryList.collaborators.map((collaborator) =>
          _buildCollaboratorTile(collaborator)
        ),
      ],
    );
  }

  Widget _buildCollaboratorTile(GroceryCollaborator collaborator) {
    return ListTile(
      leading: CircleAvatar(
        child: Text(collaborator.userId.name[0].toUpperCase()),
      ),
      title: Text(collaborator.userId.name),
      subtitle: Text('${collaborator.userId.email} • ${collaborator.role.name}'),
      trailing: PopupMenuButton(
        itemBuilder: (context) => [
          PopupMenuItem(
            child: Text('Change Role'),
            onTap: () => _changeCollaboratorRole(collaborator),
          ),
          PopupMenuItem(
            child: Text('Remove'),
            onTap: () => _removeCollaborator(collaborator),
          ),
        ],
      ),
    );
  }
}
```

### **Phase 5: Enhanced Screens (Minimal Changes)**

#### **5.1 Enhanced Groceries Screen**

**File**: `lib/src/features/groceries/screens/groceries_screen.dart`
```dart
class _GroceriesScreenState extends ConsumerState<GroceriesScreen> {
  @override
  Widget build(BuildContext context) {
    final groceryState = ref.watch(groceryProvider);

    return Scaffold(
      body: Column(
        children: [
          // NEW: Subtle list context header (only for shared lists)
          if (groceryState.currentListOwnerId != null)
            _buildSharedListHeader(groceryState),

          // UNCHANGED: Header with search and add
          _buildAddItemHeader(),

          // UNCHANGED: Tab bar and content
          _buildTabBar(),
          _buildMainContent(),

          // UNCHANGED: Bottom action bar
          _buildBottomActionBar(),
        ],
      ),
    );
  }

  Widget _buildSharedListHeader(GroceryState groceryState) {
    final groceryList = groceryState.groceryList;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.blue.withOpacity(0.2))),
      ),
      child: Row(
        children: [
          Icon(LucideIcons.users, size: 16, color: Colors.blue),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  groceryList?.name ?? 'Shared List',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                if (groceryList != null)
                  Text(
                    '${groceryList.collaborators.length} collaborators • You\'re ${groceryList.userRole?.name ?? 'Member'}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(LucideIcons.settings, size: 20, color: Colors.blue),
            onPressed: () => _showCollaborationSettings(),
          ),
          IconButton(
            icon: Icon(LucideIcons.users, size: 20, color: Colors.blue),
            onPressed: () => _showListSwitcher(),
          ),
        ],
      ),
    );
  }

  void _showListSwitcher() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => ListSwitcherPopup(),
    );
  }

  void _showCollaborationSettings() {
    final groceryList = ref.read(groceryProvider).groceryList;
    if (groceryList != null) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) => CollaborationBottomSheet(groceryList: groceryList),
      );
    }
  }

  // UNCHANGED: All existing methods remain the same
  Widget _buildAddItemHeader() {
    // ... existing implementation unchanged
  }
}
```

### **Phase 6: Enhanced Navigation & Dashboard**

#### **6.1 Enhanced Dashboard Shell**

**File**: `lib/src/features/dashboard/screens/dashboard_shell.dart`
```dart
@override
Widget build(BuildContext context, WidgetRef ref) {
  final int selectedIndex = _calculateSelectedIndex(context);

  return Scaffold(
    appBar: AppBar(
      title: Text(_getTitle(selectedIndex)),
      actions: [
        // NEW: Add collaboration indicators for groceries (subtle)
        if (selectedIndex == 2) ...[
          _buildInvitationBadge(ref),
        ],
        // ... existing actions
      ],
    ),
    // ... rest of build method unchanged
  );
}

Widget _buildInvitationBadge(WidgetRef ref) {
  final groceryState = ref.watch(groceryProvider);
  final pendingCount = groceryState.pendingInvitations.length;

  if (pendingCount == 0) return SizedBox.shrink();

  return IconButton(
    icon: Badge(
      label: Text('$pendingCount'),
      child: Icon(LucideIcons.mail),
    ),
    onPressed: () => _showInvitations(context, ref),
    tooltip: 'Pending Invitations',
  );
}

void _showInvitations(BuildContext context, WidgetRef ref) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => InvitationManagementPopup(),
  );
}
```

#### **6.2 Enhanced App Router (No New Routes)**

**File**: `lib/src/core/routing/app_router.dart`
```dart
// NO CHANGES NEEDED - All collaboration features accessed via popups
// Maintains existing navigation structure
GoRoute(
  path: '/dashboard/groceries',
  builder: (context, state) => const GroceriesScreen(),
  // No sub-routes needed - everything is popup-based
),
```

### **Phase 7: Real-Time Updates & Notifications**

#### **7.1 Enhanced Messaging Service**

**File**: `lib/src/core/services/messaging_service.dart`
```dart
class MessagingService {
  // Enhanced to handle grocery collaboration notifications

  void _handleGroceryCollaborationMessage(RemoteMessage message) {
    final data = message.data;

    switch (data['type']) {
      case 'grocery_invitation':
        _showInvitationNotification(data);
        break;
      case 'grocery_item_added':
        _showItemAddedNotification(data);
        break;
      case 'grocery_list_updated':
        _refreshGroceryList();
        break;
    }
  }

  void _showInvitationNotification(Map<String, dynamic> data) {
    // Show local notification for new invitation
    _notificationService.showNotification(
      title: 'Grocery List Invitation',
      body: '${data['inviterName']} invited you to collaborate',
      payload: jsonEncode(data),
    );
  }

  void _refreshGroceryList() {
    // Trigger grocery list refresh
    // This will be handled by the grocery provider
  }
}
```

#### **7.2 Enhanced Notification Handling**

**File**: `lib/src/core/services/notification_service.dart`
```dart
class NotificationService {
  void _handleNotificationTap(String? payload) {
    if (payload != null) {
      final data = jsonDecode(payload);

      switch (data['type']) {
        case 'grocery_invitation':
          _navigateToInvitations();
          break;
        case 'grocery_item_added':
          _navigateToGroceries(data['listOwnerId']);
          break;
      }
    }
  }

  void _navigateToInvitations() {
    // Navigate to groceries and show invitations popup
    GoRouter.of(context).go('/dashboard/groceries');
    // Show invitations popup after navigation
  }
}
```

### **Phase 8: Testing Strategy**

#### **8.1 Unit Tests**

**Files to Create**:
- `test/features/groceries/models/grocery_list_test.dart`
- `test/features/groceries/providers/grocery_collaboration_test.dart`
- `test/core/services/grocery_service_collaboration_test.dart`

```dart
// Example test structure
void main() {
  group('GroceryList Model', () {
    test('should parse collaboration data correctly', () {
      // Test JSON parsing with collaboration fields
    });

    test('should handle user roles correctly', () {
      // Test role-based permissions
    });
  });

  group('Grocery Collaboration Provider', () {
    test('should switch lists correctly', () {
      // Test list switching functionality
    });

    test('should handle invitations correctly', () {
      // Test invitation acceptance/decline
    });
  });
}
```

#### **8.2 Widget Tests**

**Files to Create**:
- `test/features/groceries/widgets/list_switcher_popup_test.dart`
- `test/features/groceries/widgets/collaboration_bottom_sheet_test.dart`

#### **8.3 Integration Tests**

**Files to Create**:
- `integration_test/grocery_collaboration_test.dart`

```dart
void main() {
  group('Grocery Collaboration Flow', () {
    testWidgets('should switch between personal and shared lists', (tester) async {
      // Test complete list switching flow
    });

    testWidgets('should handle invitation acceptance', (tester) async {
      // Test invitation flow
    });
  });
}
```

---

## **🚀 Implementation Timeline**

### **Week 1: Foundation**
- ✅ Enhanced data models (GroceryItem, GroceryList, GroceryInvitation)
- ✅ Enhanced grocery service with collaboration methods
- ✅ Enhanced grocery provider state management

### **Week 2: Core UI**
- ✅ Enhanced grocery list items with collaboration info
- ✅ Subtle shared list header for groceries screen
- ✅ Enhanced quick add with context awareness

### **Week 3: Popup Features**
- ✅ List switcher popup
- ✅ Collaboration bottom sheet
- ✅ Invitation management popup

### **Week 4: Integration**
- ✅ Real-time updates via messaging service
- ✅ Push notifications for invitations
- ✅ Dashboard integration with invitation badges

### **Week 5: Testing & Polish**
- ✅ Unit tests for all new functionality
- ✅ Widget tests for popup components
- ✅ Integration tests for collaboration flows
- ✅ Performance optimization
- ✅ Error handling improvements

### **Week 6: Final Testing**
- ✅ End-to-end testing with real backend
- ✅ Cross-platform testing (Android/iOS)
- ✅ Performance testing with multiple collaborators
- ✅ User acceptance testing

---

## **✅ Success Criteria & Validation**

### **1. FlashTasks Simplicity Maintained**
- ✅ Quick add voice/text input unchanged
- ✅ Personal lists show zero collaboration UI
- ✅ Adding items remains one-tap + voice/text
- ✅ No new bottom navigation complexity

### **2. Collaboration Features Work Seamlessly**
- ✅ Users can switch between personal and shared lists
- ✅ Items automatically target correct list context
- ✅ Real-time updates work across all collaborators
- ✅ Invitations can be sent, received, and managed

### **3. UI/UX Excellence**
- ✅ Shared list context is subtle but clear
- ✅ Collaboration info is helpful but not intrusive
- ✅ All advanced features accessed via popups
- ✅ Consistent with existing app design patterns

### **4. Technical Excellence**
- ✅ All new code has comprehensive test coverage
- ✅ Performance remains excellent with collaboration
- ✅ Error handling is robust and user-friendly
- ✅ Real-time updates are reliable and efficient

### **5. User Validation**
- ✅ Family users can collaborate effectively
- ✅ Individual users see no complexity increase
- ✅ Quick add remains the fastest interaction
- ✅ Shared lists enhance rather than complicate workflow

---

## **🎯 Key Design Decisions**

### **1. Popup-Based Architecture**
- **Decision**: All collaboration features accessed via popups/bottom sheets
- **Rationale**: Maintains FlashTasks simplicity while providing powerful features
- **Impact**: Zero navigation bloat, progressive disclosure of complexity

### **2. Context-Aware Quick Add**
- **Decision**: Quick add automatically targets current list context
- **Rationale**: Smart behavior without user complexity
- **Impact**: Seamless collaboration without changing core interaction

### **3. Subtle Collaboration Indicators**
- **Decision**: Minimal visual indicators for collaboration activity
- **Rationale**: Helpful information without UI clutter
- **Impact**: Users stay informed without distraction

### **4. Personal List Purity**
- **Decision**: Personal lists show zero collaboration UI
- **Rationale**: Maintains simplicity for individual users
- **Impact**: No complexity increase for non-collaborative users

This comprehensive plan ensures shared groceries enhance FlashTasks' collaborative capabilities while preserving its core philosophy of simple, fast input! 🚀✨