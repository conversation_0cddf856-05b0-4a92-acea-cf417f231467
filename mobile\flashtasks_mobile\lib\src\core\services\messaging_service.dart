import 'messaging_service_interface.dart';

// Use conditional import to select the appropriate implementation
// based on whether we're running on the web or mobile
import 'messaging_service_mobile.dart'
    if (dart.library.js) 'messaging_service_web.dart' as implementation;

/// Messaging Service for handling push notifications
/// This is a facade that delegates to platform-specific implementations
class MessagingService implements MessagingServiceInterface {
  // Singleton pattern
  static final MessagingService _instance = MessagingService._internal();
  
  // The actual implementation (mobile or web)
  final _service = implementation.MessagingService();
  
  factory MessagingService() {
    return _instance;
  }
  
  MessagingService._internal();
  
  @override
  Future<void> initialize() async {
    await _service.initialize();
  }
  
  @override
  Future<String?> getToken() async {
    return await _service.getToken();
  }
  
  @override
  Future<void> subscribeToTopic(String topic) async {
    await _service.subscribeToTopic(topic);
  }
  
  @override
  Future<void> unsubscribeFromTopic(String topic) async {
    await _service.unsubscribeFromTopic(topic);
  }
  
  @override
  void setupForegroundNotificationHandling() {
    _service.setupForegroundNotificationHandling();
  }
  
  @override
  void setupBackgroundMessaging() {
    _service.setupBackgroundMessaging();
  }
  
  @override
  void setupNotificationTapHandling() {
    _service.setupNotificationTapHandling();
  }
} 