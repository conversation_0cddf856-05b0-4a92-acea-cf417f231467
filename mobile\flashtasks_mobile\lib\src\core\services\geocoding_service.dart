import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AddressSuggestion {
  final String id;
  final String displayName;
  final double latitude;
  final double longitude;
  final String? addressLine1;
  final String? addressLine2;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  AddressSuggestion({
    required this.id,
    required this.displayName,
    required this.latitude,
    required this.longitude,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  @override
  String toString() => displayName;
}

class GeocodingService {
  final Dio _dio = Dio();
  final String _baseUrl = 'https://api.mapbox.com/geocoding/v5/mapbox.places';
  final String _apiKey = 'pk.eyJ1Ijoicm9iaW5vc2MiLCJhIjoiY2tuNXQ2Mmw1MDY3aTJucXZseDVsdTAxdyJ9.wK3qnnBq9Q64VIS4TVq8pg';

  GeocodingService() {
    print('GeocodingService initialized with Mapbox API key');
  }

  /// Search for addresses using Mapbox Geocoding API
  Future<List<AddressSuggestion>> searchAddress(String query) async {
    try {
      if (query.isEmpty) return [];

      final response = await _dio.get(
        '$_baseUrl/$query.json',
        queryParameters: {
          'access_token': _apiKey,
          'types': 'address,poi,place,neighborhood,locality',
          'limit': 5,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> features = response.data['features'] ?? [];
        return features.map((feature) {
          final coords = feature['center'] as List<dynamic>;
          final properties = feature['properties'] ?? {};
          final context = feature['context'] ?? [];
          
          // Extract address components
          String? addressLine1;
          String? city;
          String? state;
          String? country;
          String? postalCode;
          
          for (var item in context) {
            if (item is Map) {
              final id = item['id'] as String? ?? '';
              if (id.contains('place')) {
                city = item['text'] as String?;
              } else if (id.contains('region')) {
                state = item['text'] as String?;
              } else if (id.contains('country')) {
                country = item['text'] as String?;
              } else if (id.contains('postcode')) {
                postalCode = item['text'] as String?;
              }
            }
          }
          
          addressLine1 = feature['text'] as String?;
          
          return AddressSuggestion(
            id: feature['id'] as String? ?? '',
            displayName: feature['place_name'] as String? ?? feature['text'] as String? ?? '',
            latitude: (coords[1] as num).toDouble(),
            longitude: (coords[0] as num).toDouble(),
            addressLine1: addressLine1,
            city: city,
            state: state,
            country: country,
            postalCode: postalCode,
          );
        }).toList();
      }
      return [];
    } catch (e) {
      // In a production app, you might want to log this error
      return [];
    }
  }

  /// Get address from coordinates (reverse geocoding)
  Future<AddressSuggestion?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/$longitude,$latitude.json',
        queryParameters: {
          'access_token': _apiKey,
          'types': 'address,poi,place,neighborhood,locality',
        },
      );

      if (response.statusCode == 200) {
        final features = response.data['features'] as List<dynamic>?;
        if (features != null && features.isNotEmpty) {
          final feature = features.first;
          final coords = feature['center'] as List<dynamic>;
          
          return AddressSuggestion(
            id: feature['id'] as String? ?? '',
            displayName: feature['place_name'] as String? ?? '',
            latitude: (coords[1] as num).toDouble(),
            longitude: (coords[0] as num).toDouble(),
          );
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}

// Provider for GeocodingService
final geocodingServiceProvider = Provider<GeocodingService>((ref) {
  return GeocodingService();
});
