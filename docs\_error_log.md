 *** Request ***
I/flutter (12588): DIO LOG: uri: http://********:3001/api/groceries/shared
I/flutter (12588): DIO LOG: method: GET
I/flutter (12588): DIO LOG: responseType: ResponseType.json
I/flutter (12588): DIO LOG: followRedirects: true
I/flutter (12588): DIO LOG: persistentConnection: true
I/flutter (12588): DIO LOG: connectTimeout: 0:00:30.000000
I/flutter (12588): DIO LOG: sendTimeout: null
I/flutter (12588): DIO LOG: receiveTimeout: 0:00:30.000000
I/flutter (12588): DIO LOG: receiveDataWhenStatusError: true
I/flutter (12588): DIO LOG: extra: {withCredentials: true, androidDebug: true}
I/flutter (12588): DIO LOG: headers:
I/flutter (12588): DIO LOG:  Accept: application/json
I/flutter (12588): DIO LOG:  content-type: application/json
I/flutter (12588): DIO LOG: data:
I/flutter (12588): DIO LOG: null
I/flutter (12588): DIO LOG:
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:71:15)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 API Request: GET /api/settings/grocery-preferences
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:72:15)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 Query parameters: {}
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:71:15)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 API Request: GET /api/groceries/shared
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:72:15)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 Query parameters: {}
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:87:17)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 Adding auth header for /api/settings/grocery-preferences
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:87:17)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 Adding auth header for /api/groceries/shared
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): *** Request ***
I/flutter (12588): uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): method: GET
I/flutter (12588): responseType: ResponseType.json
I/flutter (12588): followRedirects: true
I/flutter (12588): persistentConnection: true
I/flutter (12588): connectTimeout: 0:00:30.000000
I/flutter (12588): sendTimeout: null
I/flutter (12588): receiveTimeout: 0:00:30.000000
I/flutter (12588): receiveDataWhenStatusError: true
I/flutter (12588): extra: {withCredentials: true, androidDebug: true}
I/flutter (12588): headers:
I/flutter (12588):  Accept: application/json
I/flutter (12588):  content-type: application/json
I/flutter (12588):  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************.oMUte_4MGZ-En_WfThhZ0E9U7pAOf5dUOM2o9s0PdQ0
I/flutter (12588): data:
I/flutter (12588): null
I/flutter (12588): 
I/flutter (12588): *** Request ***
I/flutter (12588): uri: http://********:3001/api/groceries/shared
I/flutter (12588): method: GET
I/flutter (12588): responseType: ResponseType.json
I/flutter (12588): followRedirects: true
I/flutter (12588): persistentConnection: true
I/flutter (12588): connectTimeout: 0:00:30.000000
I/flutter (12588): sendTimeout: null
I/flutter (12588): receiveTimeout: 0:00:30.000000
I/flutter (12588): receiveDataWhenStatusError: true
I/flutter (12588): extra: {withCredentials: true, androidDebug: true}
I/flutter (12588): headers:
I/flutter (12588):  Accept: application/json
I/flutter (12588):  content-type: application/json
I/flutter (12588):  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************.oMUte_4MGZ-En_WfThhZ0E9U7pAOf5dUOM2o9s0PdQ0
I/flutter (12588): data:
I/flutter (12588): null
I/flutter (12588):
I/flutter (12588): DIO LOG: *** Response ***
I/flutter (12588): DIO LOG: uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): DIO LOG: statusCode: 200
I/flutter (12588): DIO LOG: headers:
I/flutter (12588): DIO LOG:  x-dns-prefetch-control: off
I/flutter (12588): DIO LOG:  ratelimit-limit: 20000
I/flutter (12588): DIO LOG:  date: Wed, 28 May 2025 08:57:43 GMT
I/flutter (12588): DIO LOG:  vary: Origin
I/flutter (12588): DIO LOG:  origin-agent-cluster: ?1
I/flutter (12588): DIO LOG:  access-control-expose-headers: X-CSRF-Token
I/flutter (12588): DIO LOG:  ratelimit-remaining: 19979
I/flutter (12588): DIO LOG:  ratelimit-policy: 20000;w=3600
I/flutter (12588): DIO LOG:  cross-origin-resource-policy: same-site
I/flutter (12588): DIO LOG:  content-length: 106
I/flutter (12588): DIO LOG:  etag: W/"6a-Bnf2/YNEUzPReJTc8WPvHAAGJsE"
I/flutter (12588): DIO LOG:  x-frame-options: DENY
I/flutter (12588): DIO LOG:  content-security-policy: default-src 'self';script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;font-src 'self' https://fonts.gstatic.com;img
-src 'self' data: https://hebbkx1anhila5yf.public.blob.vercel-storage.com;connect-src 'self' http://********:3000 http://localhost:3000 http://********:3001 http://localhost:3001 http://********:3001 http://localhost:3001;frame-src 'none';object-src 'none';upgrade-insecure-requests;base-uri 'self';form-action 'self';frame-ancestors 'self';script-src-attr 'none'
I/flutter (12588): DIO LOG:  connection: keep-alive
I/flutter (12588): DIO LOG:  strict-transport-security: max-age=31536000; includeSubDomains; preload
I/flutter (12588): DIO LOG:  referrer-policy: strict-origin-when-cross-origin
I/flutter (12588): DIO LOG:  x-permitted-cross-domain-policies: none
I/flutter (12588): DIO LOG:  cross-origin-opener-policy: same-origin
I/flutter (12588): DIO LOG:  content-type: application/json; charset=utf-8
I/flutter (12588): DIO LOG:  x-xss-protection: 0
I/flutter (12588): DIO LOG:  access-control-allow-credentials: true
I/flutter (12588): DIO LOG:  keep-alive: timeout=5
I/flutter (12588): DIO LOG:  x-download-options: noopen
I/flutter (12588): DIO LOG:  x-content-type-options: nosniff
I/flutter (12588): DIO LOG:  ratelimit-reset: 3306
I/flutter (12588): DIO LOG: Response Text:
I/flutter (12588): DIO LOG: {"success":true,"data":{"defaultListType":"shared","defaultSharedListOwnerId":"67f9fd5fe33431ef3dbfa5d7"}}
I/flutter (12588): DIO LOG:
I/flutter (12588): *** Response ***
I/flutter (12588): uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): statusCode: 200
I/flutter (12588): headers:
I/flutter (12588):  x-dns-prefetch-control: off
I/flutter (12588):  ratelimit-limit: 20000
I/flutter (12588):  date: Wed, 28 May 2025 08:57:43 GMT
I/flutter (12588):  vary: Origin
I/flutter (12588):  origin-agent-cluster: ?1
I/flutter (12588):  access-control-expose-headers: X-CSRF-Token
I/flutter (12588):  ratelimit-remaining: 19979
I/flutter (12588):  ratelimit-policy: 20000;w=3600
I/flutter (12588):  cross-origin-resource-policy: same-site
I/flutter (12588):  content-length: 106
I/flutter (12588):  etag: W/"6a-Bnf2/YNEUzPReJTc8WPvHAAGJsE"
I/flutter (12588):  x-frame-options: DENY
I/flutter (12588):  content-security-policy: default-src 'self';script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;font-src 'self' https://fonts.gstatic.com;img-src 'sel
f' data: https://hebbkx1anhila5yf.public.blob.vercel-storage.com;connect-src 'self' http://********:3000 http://localhost:3000 http://********:3001 http://localhost:3001 http://********:3001 http://localhost:3001;frame-src 'none';object-src 'none';upgrade-insecure-requests;base-uri 'self';form-action 'self';frame-ancestors 'self';script-src-attr 'none'
I/flutter (12588):  connection: keep-alive
I/flutter (12588):  strict-transport-security: max-age=31536000; includeSubDomains; preload
I/flutter (12588):  referrer-policy: strict-origin-when-cross-origin
I/flutter (12588):  x-permitted-cross-domain-policies: none
I/flutter (12588):  cross-origin-opener-policy: same-origin
I/flutter (12588):  content-type: application/json; charset=utf-8
I/flutter (12588):  x-xss-protection: 0
I/flutter (12588):  access-control-allow-credentials: true
I/flutter (12588):  keep-alive: timeout=5
I/flutter (12588):  x-download-options: noopen
I/flutter (12588):  x-content-type-options: nosniff
I/flutter (12588):  ratelimit-reset: 3306
I/flutter (12588): Response Text:
I/flutter (12588): {"success":true,"data":{"defaultListType":"shared","defaultSharedListOwnerId":"67f9fd5fe33431ef3dbfa5d7"}}
I/flutter (12588):

══╡ EXCEPTION CAUGHT BY RENDERING LIBRARY ╞═════════════════════════════════════════════════════════
The following assertion was thrown during layout:
A RenderFlex overflowed by 12 pixels on the bottom.

The relevant error-causing widget was:
  Column
  Column:file:///C:/Users/<USER>/OneDrive%20-%20OSC%20AS/Repositories/TasksOrg/mobile/flashtasks_mobile/lib/src/features/settings/screens/grocery_preferences_screen.dart:312:28

To inspect this widget in Flutter DevTools, visit:
http://127.0.0.1:9102/#/inspector?uri=http%3A%2F%2F127.0.0.1%3A58137%2FqdDB-5ydJvg%3D%2F&inspectorRef=inspector-0

The overflowing RenderFlex has an orientation of Axis.vertical.
The edge of the RenderFlex that is overflowing has been marked in the rendering with a yellow and
black striped pattern. This is usually caused by the contents being too big for the RenderFlex.
Consider applying a flex factor (e.g. using an Expanded widget) to force the children of the
RenderFlex to fit within the available space instead of being sized to their natural size.
This is considered an error condition because it indicates that there is content that cannot be
seen. If the content is legitimately bigger than the available space, consider clipping it with a
ClipRect widget before putting it in the flex, or using a scrollable container rather than a Flex,
like a ListView.
The specific RenderFlex in question is: RenderFlex#3151d relayoutBoundary=up7 OVERFLOWING:
  creator: Column ← Align ← ConstrainedBox ← Semantics ← DropdownMenuItem<String> ← IgnorePointer ←
    _Visibility ← _VisibilityScope ← Visibility ← _RawIndexedStack ← IndexedStack ← Row ← ⋯
  parentData: offset=Offset(0.0, 0.0) (can use size)
  constraints: BoxConstraints(0.0<=w<=Infinity, 0.0<=h<=24.0)
  size: Size(77.2, 24.0)
  direction: vertical
  mainAxisAlignment: start
  mainAxisSize: min
  crossAxisAlignment: start
  textDirection: ltr
  verticalDirection: down
  spacing: 0.0
◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤
════════════════════════════════════════════════════════════════════════════════════════════════════

I/flutter (12588): DIO LOG: *** Response ***
I/flutter (12588): DIO LOG: uri: http://********:3001/api/groceries/shared
I/flutter (12588): DIO LOG: statusCode: 200
I/flutter (12588): DIO LOG: headers:
I/flutter (12588): DIO LOG:  x-dns-prefetch-control: off
I/flutter (12588): DIO LOG:  ratelimit-limit: 20000
I/flutter (12588): DIO LOG:  date: Wed, 28 May 2025 08:57:43 GMT
I/flutter (12588): DIO LOG:  vary: Origin
I/flutter (12588): DIO LOG:  origin-agent-cluster: ?1
I/flutter (12588): DIO LOG:  access-control-expose-headers: X-CSRF-Token
I/flutter (12588): DIO LOG:  ratelimit-remaining: 19978
I/flutter (12588): DIO LOG:  ratelimit-policy: 20000;w=3600
I/flutter (12588): DIO LOG:  cross-origin-resource-policy: same-site
I/flutter (12588): DIO LOG:  content-length: 1619
I/flutter (12588): DIO LOG:  etag: W/"653-uzEI4WDVjF7+7GYZYx353Q2rF+0"
I/flutter (12588): DIO LOG:  x-frame-options: DENY
I/flutter (12588): DIO LOG:  content-security-policy: default-src 'self';script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;font-src 'self' https://fonts.gstatic.com;img
-src 'self' data: https://hebbkx1anhila5yf.public.blob.vercel-storage.com;connect-src 'self' http://********:3000 http://localhost:3000 http://********:3001 http://localhost:3001 http://********:3001 http://localhost:3001;frame-src 'none';object-src 'none';upgrade-insecure-requests;base-uri 'self';form-action 'self';frame-ancestors 'self';script-src-attr 'none'
I/flutter (12588): DIO LOG:  connection: keep-alive
I/flutter (12588): DIO LOG:  strict-transport-security: max-age=31536000; includeSubDomains; preload
I/flutter (12588): DIO LOG:  referrer-policy: strict-origin-when-cross-origin
I/flutter (12588): DIO LOG:  x-permitted-cross-domain-policies: none
I/flutter (12588): DIO LOG:  cross-origin-opener-policy: same-origin
I/flutter (12588): DIO LOG:  content-type: application/json; charset=utf-8
I/flutter (12588): DIO LOG:  x-xss-protection: 0
I/flutter (12588): DIO LOG:  access-control-allow-credentials: true
I/flutter (12588): DIO LOG:  keep-alive: timeout=5
I/flutter (12588): DIO LOG:  x-download-options: noopen
I/flutter (12588): DIO LOG:  x-content-type-options: nosniff
I/flutter (12588): DIO LOG:  ratelimit-reset: 3306
I/flutter (12588): DIO LOG: Response Text:
I/flutter (12588): DIO LOG: {"success":true,"data":[{"shareSettings":{"allowCollaboratorInvites":false,"requireApprovalForEdits":false,"notifyOnChanges":true},"_id":"68356d91d76b8edc03aeed38","userId":{"_id":"68356d80d76b8edc03a
eed24","name":"Robin Viken","email":"<EMAIL>"},"isShared":true,"collaborators":[{"userId":{"_id":"67f9fd5fe33431ef3dbfa5d7","name":"Robin Viken","email":"<EMAIL>","id":"67f9fd5fe33431ef3dbfa5d7"},"role"
:"editor","joinedAt":"2025-05-27T21:16:24.004Z","invitedBy":{"_id":"68356d80d76b8edc03aeed24","name":"Robin Viken","email":"<EMAIL>","id":"68356d80d76b8edc03aeed24"}}],"lastCollaborativeActivity":"2025-05-27T21:16:2
4.010Z","createdAt":"2025-05-27T07:45:21.948Z","updatedAt":"2025-05-27T21:16:24.011Z","__v":1,"collaboratorCount":2,"id":"68356d91d76b8edc03aeed38","userRole":null},{"shareSettings":{"allowCollaboratorInvites":false,"requireApprovalForEdits":false,"notifyOnChanges":true},"_id":"68356bd6d76b8edc03aeecfd","userId":{"_id":"67f9fd5fe33431ef3dbfa5d7","name":"Ro
I/flutter (12588): DIO LOG:
I/flutter (12588): *** Response ***
I/flutter (12588): uri: http://********:3001/api/groceries/shared
I/flutter (12588): statusCode: 200
I/flutter (12588): headers:
I/flutter (12588):  x-dns-prefetch-control: off
I/flutter (12588):  ratelimit-limit: 20000
I/flutter (12588):  date: Wed, 28 May 2025 08:57:43 GMT
I/flutter (12588):  vary: Origin
I/flutter (12588):  origin-agent-cluster: ?1
I/flutter (12588):  access-control-expose-headers: X-CSRF-Token
I/flutter (12588):  ratelimit-remaining: 19978
I/flutter (12588):  ratelimit-policy: 20000;w=3600
I/flutter (12588):  cross-origin-resource-policy: same-site
I/flutter (12588):  content-length: 1619
I/flutter (12588):  etag: W/"653-uzEI4WDVjF7+7GYZYx353Q2rF+0"
I/flutter (12588):  x-frame-options: DENY
I/flutter (12588):  content-security-policy: default-src 'self';script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;font-src 'self' https://fonts.gstatic.com;img-src 'sel
f' data: https://hebbkx1anhila5yf.public.blob.vercel-storage.com;connect-src 'self' http://********:3000 http://localhost:3000 http://********:3001 http://localhost:3001 http://********:3001 http://localhost:3001;frame-src 'none';object-src 'none';upgrade-insecure-requests;base-uri 'self';form-action 'self';frame-ancestors 'self';script-src-attr 'none'
I/flutter (12588):  connection: keep-alive
I/flutter (12588):  strict-transport-security: max-age=31536000; includeSubDomains; preload
I/flutter (12588):  referrer-policy: strict-origin-when-cross-origin
I/flutter (12588):  x-permitted-cross-domain-policies: none
I/flutter (12588):  cross-origin-opener-policy: same-origin
I/flutter (12588):  content-type: application/json; charset=utf-8
I/flutter (12588):  x-xss-protection: 0
I/flutter (12588):  access-control-allow-credentials: true
I/flutter (12588):  keep-alive: timeout=5
I/flutter (12588):  x-download-options: noopen
I/flutter (12588):  x-content-type-options: nosniff
I/flutter (12588):  ratelimit-reset: 3306
I/flutter (12588): Response Text:
I/flutter (12588): {"success":true,"data":[{"shareSettings":{"allowCollaboratorInvites":false,"requireApprovalForEdits":false,"notifyOnChanges":true},"_id":"68356d91d76b8edc03aeed38","userId":{"_id":"68356d80d76b8edc03aeed24","n
ame":"Robin Viken","email":"<EMAIL>"},"isShared":true,"collaborators":[{"userId":{"_id":"67f9fd5fe33431ef3dbfa5d7","name":"Robin Viken","email":"<EMAIL>","id":"67f9fd5fe33431ef3dbfa5d7"},"role":"editor"
,"joinedAt":"2025-05-27T21:16:24.004Z","invitedBy":{"_id":"68356d80d76b8edc03aeed24","name":"Robin Viken","email":"<EMAIL>","id":"68356d80d76b8edc03aeed24"}}],"lastCollaborativeActivity":"2025-05-27T21:16:24.010Z","
createdAt":"2025-05-27T07:45:21.948Z","updatedAt":"2025-05-27T21:16:24.011Z","__v":1,"collaboratorCount":2,"id":"68356d91d76b8edc03aeed38","userRole":null},{"shareSettings":{"allowCollaboratorInvites":false,"requireApprovalForEdits":false,"notifyOnChanges":true},"_id":"68356bd6d76b8edc03aeecfd","userId":{"_id":"67f9fd5fe33431ef3dbfa5d7","name":"Robin Viken
I/flutter (12588):
I/flutter (12588): 📊 Response status code: 200
I/flutter (12588): 📊 Response data type: _Map<String, dynamic>
I/flutter (12588): 📊 Fetched 2 shared grocery lists
I/flutter (12588): 📊 Shared list: Unnamed (Owner: 68356d80d76b8edc03aeed24, Role: Unknown, ID: 68356d91d76b8edc03aeed38)
I/flutter (12588): 📊 Shared list: Unnamed (Owner: 67f9fd5fe33431ef3dbfa5d7, Role: Unknown, ID: 68356bd6d76b8edc03aeecfd)
I/flutter (12588): 📊 Shared lists parsed successfully!
E/libEGL  (12588): called unimplemented OpenGL ES API
I/flutter (12588): DIO LOG: *** Request ***
I/flutter (12588): DIO LOG: uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): DIO LOG: method: PUT
I/flutter (12588): DIO LOG: responseType: ResponseType.json
I/flutter (12588): DIO LOG: followRedirects: true
I/flutter (12588): DIO LOG: persistentConnection: true
I/flutter (12588): DIO LOG: connectTimeout: 0:00:30.000000
I/flutter (12588): DIO LOG: sendTimeout: null
I/flutter (12588): DIO LOG: receiveTimeout: 0:00:30.000000
I/flutter (12588): DIO LOG: receiveDataWhenStatusError: true
I/flutter (12588): DIO LOG: extra: {withCredentials: true, androidDebug: true}
I/flutter (12588): DIO LOG: headers:
I/flutter (12588): DIO LOG:  Accept: application/json
I/flutter (12588): DIO LOG:  content-type: application/json
I/flutter (12588): DIO LOG: data:
I/flutter (12588): DIO LOG: {groceryPreferences: {autoSwitchToDefault: true}}
I/flutter (12588): DIO LOG:
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:71:15)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 API Request: PUT /api/settings/grocery-preferences
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:72:15)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 Query parameters: {}
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): │ #0   AppLogger.d (package:flashtasks_mobile/src/core/utils/app_logger.dart:21:13)
I/flutter (12588): │ #1   ApiClient._onRequest (package:flashtasks_mobile/src/core/api/api_client.dart:87:17)
I/flutter (12588): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (12588): │ 🐛 🔄 Adding auth header for /api/settings/grocery-preferences
I/flutter (12588): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (12588): *** Request ***
I/flutter (12588): uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): method: PUT
I/flutter (12588): responseType: ResponseType.json
I/flutter (12588): followRedirects: true
I/flutter (12588): persistentConnection: true
I/flutter (12588): connectTimeout: 0:00:30.000000
I/flutter (12588): sendTimeout: null
I/flutter (12588): receiveTimeout: 0:00:30.000000
I/flutter (12588): receiveDataWhenStatusError: true
I/flutter (12588): extra: {withCredentials: true, androidDebug: true}
I/flutter (12588): headers:
I/flutter (12588):  Accept: application/json
I/flutter (12588):  content-type: application/json
I/flutter (12588):  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************.oMUte_4MGZ-En_WfThhZ0E9U7pAOf5dUOM2o9s0PdQ0
I/flutter (12588): data:
I/flutter (12588): {groceryPreferences: {autoSwitchToDefault: true}}
I/flutter (12588):
I/flutter (12588): DIO LOG: *** Response ***
I/flutter (12588): DIO LOG: uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): DIO LOG: statusCode: 200
I/flutter (12588): DIO LOG: headers:
I/flutter (12588): DIO LOG:  x-dns-prefetch-control: off
I/flutter (12588): DIO LOG:  ratelimit-limit: 20000
I/flutter (12588): DIO LOG:  date: Wed, 28 May 2025 08:57:55 GMT
I/flutter (12588): DIO LOG:  vary: Origin
I/flutter (12588): DIO LOG:  origin-agent-cluster: ?1
I/flutter (12588): DIO LOG:  access-control-expose-headers: X-CSRF-Token
I/flutter (12588): DIO LOG:  ratelimit-remaining: 19977
I/flutter (12588): DIO LOG:  ratelimit-policy: 20000;w=3600
I/flutter (12588): DIO LOG:  cross-origin-resource-policy: same-site
I/flutter (12588): DIO LOG:  content-length: 133
I/flutter (12588): DIO LOG:  etag: W/"85-yl8clZRqUY+7Ma4pH9JPCzlh7A0"
I/flutter (12588): DIO LOG:  x-frame-options: DENY
I/flutter (12588): DIO LOG:  content-security-policy: default-src 'self';script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;font-src 'self' https://fonts.gstatic.com;img
-src 'self' data: https://hebbkx1anhila5yf.public.blob.vercel-storage.com;connect-src 'self' http://********:3000 http://localhost:3000 http://********:3001 http://localhost:3001 http://********:3001 http://localhost:3001;frame-src 'none';object-src 'none';upgrade-insecure-requests;base-uri 'self';form-action 'self';frame-ancestors 'self';script-src-attr 'none'
I/flutter (12588): DIO LOG:  connection: keep-alive
I/flutter (12588): DIO LOG:  strict-transport-security: max-age=31536000; includeSubDomains; preload
I/flutter (12588): DIO LOG:  referrer-policy: strict-origin-when-cross-origin
I/flutter (12588): DIO LOG:  x-permitted-cross-domain-policies: none
I/flutter (12588): DIO LOG:  cross-origin-opener-policy: same-origin
I/flutter (12588): DIO LOG:  content-type: application/json; charset=utf-8
I/flutter (12588): DIO LOG:  x-xss-protection: 0
I/flutter (12588): DIO LOG:  access-control-allow-credentials: true
I/flutter (12588): DIO LOG:  keep-alive: timeout=5
I/flutter (12588): DIO LOG:  x-download-options: noopen
I/flutter (12588): DIO LOG:  x-content-type-options: nosniff
I/flutter (12588): DIO LOG:  ratelimit-reset: 3295
I/flutter (12588): DIO LOG: Response Text:
I/flutter (12588): DIO LOG: {"success":true,"data":{"defaultListType":"shared","defaultSharedListOwnerId":"67f9fd5fe33431ef3dbfa5d7","autoSwitchToDefault":true}}
I/flutter (12588): DIO LOG:
I/flutter (12588): *** Response ***
I/flutter (12588): uri: http://********:3001/api/settings/grocery-preferences
I/flutter (12588): statusCode: 200
I/flutter (12588): headers:
I/flutter (12588):  x-dns-prefetch-control: off
I/flutter (12588):  ratelimit-limit: 20000
I/flutter (12588):  date: Wed, 28 May 2025 08:57:55 GMT
I/flutter (12588):  vary: Origin
I/flutter (12588):  origin-agent-cluster: ?1
I/flutter (12588):  access-control-expose-headers: X-CSRF-Token
I/flutter (12588):  ratelimit-remaining: 19977
I/flutter (12588):  ratelimit-policy: 20000;w=3600
I/flutter (12588):  cross-origin-resource-policy: same-site
I/flutter (12588):  content-length: 133
I/flutter (12588):  etag: W/"85-yl8clZRqUY+7Ma4pH9JPCzlh7A0"
I/flutter (12588):  x-frame-options: DENY
I/flutter (12588):  content-security-policy: default-src 'self';script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;font-src 'self' https://fonts.gstatic.com;img-src 'sel
f' data: https://hebbkx1anhila5yf.public.blob.vercel-storage.com;connect-src 'self' http://********:3000 http://localhost:3000 http://********:3001 http://localhost:3001 http://********:3001 http://localhost:3001;frame-src 'none';object-src 'none';upgrade-insecure-requests;base-uri 'self';form-action 'self';frame-ancestors 'self';script-src-attr 'none'
I/flutter (12588):  connection: keep-alive
I/flutter (12588):  strict-transport-security: max-age=31536000; includeSubDomains; preload
I/flutter (12588):  referrer-policy: strict-origin-when-cross-origin
I/flutter (12588):  x-permitted-cross-domain-policies: none
I/flutter (12588):  cross-origin-opener-policy: same-origin
I/flutter (12588):  content-type: application/json; charset=utf-8
I/flutter (12588):  x-xss-protection: 0
I/flutter (12588):  access-control-allow-credentials: true
I/flutter (12588):  keep-alive: timeout=5
I/flutter (12588):  x-download-options: noopen
I/flutter (12588):  x-content-type-options: nosniff
I/flutter (12588):  ratelimit-reset: 3295
I/flutter (12588): Response Text:
I/flutter (12588): {"success":true,"data":{"defaultListType":"shared","defaultSharedListOwnerId":"67f9fd5fe33431ef3dbfa5d7","autoSwitchToDefault":true}}
I/flutter (12588):
E/libEGL  (12588): called unimplemented OpenGL ES API
