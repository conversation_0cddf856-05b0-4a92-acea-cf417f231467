import mongoose, { Document, Schema, Types } from 'mongoose';

// Define interface for list member
interface IListMember {
  userId: Types.ObjectId;
  role: 'owner' | 'editor' | 'viewer';
  joinedAt: Date;
  invitedBy: Types.ObjectId;
}

// Define interface for SharedGroceryList document
export interface ISharedGroceryList extends Document {
  name: string;
  description?: string;
  owner: Types.ObjectId;
  members: IListMember[];
  isActive: boolean;
  settings: {
    allowMemberInvites: boolean;
    requireApprovalForEdits: boolean;
    notifyOnChanges: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  lastActivityAt: Date;
  
  // Methods
  addMember(userId: Types.ObjectId, role: 'editor' | 'viewer', invitedBy: Types.ObjectId): Promise<void>;
  removeMember(userId: Types.ObjectId): Promise<void>;
  updateMemberRole(userId: Types.ObjectId, newRole: 'editor' | 'viewer'): Promise<void>;
  isMember(userId: Types.ObjectId): boolean;
  getMemberRole(userId: Types.ObjectId): 'owner' | 'editor' | 'viewer' | null;
  canEdit(userId: Types.ObjectId): boolean;
  canView(userId: Types.ObjectId): boolean;
}

// Define schema for list member subdocument
const listMemberSchema = new Schema<IListMember>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  role: {
    type: String,
    enum: ['owner', 'editor', 'viewer'],
    required: true,
  },
  joinedAt: {
    type: Date,
    default: Date.now,
  },
  invitedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, { _id: false });

// SharedGroceryList Schema
const sharedGroceryListSchema = new Schema<ISharedGroceryList>(
  {
    name: {
      type: String,
      required: [true, 'List name is required'],
      trim: true,
      maxlength: [100, 'List name cannot exceed 100 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    owner: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    members: [listMemberSchema],
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    settings: {
      allowMemberInvites: {
        type: Boolean,
        default: false,
      },
      requireApprovalForEdits: {
        type: Boolean,
        default: false,
      },
      notifyOnChanges: {
        type: Boolean,
        default: true,
      },
    },
    lastActivityAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for efficient querying
sharedGroceryListSchema.index({ owner: 1, isActive: 1 });
sharedGroceryListSchema.index({ 'members.userId': 1, isActive: 1 });
sharedGroceryListSchema.index({ lastActivityAt: -1 });

// Method to add a member to the list
sharedGroceryListSchema.methods.addMember = async function(
  userId: Types.ObjectId, 
  role: 'editor' | 'viewer', 
  invitedBy: Types.ObjectId
): Promise<void> {
  // Check if user is already a member
  const existingMember = this.members.find((member: IListMember) => 
    member.userId.toString() === userId.toString()
  );
  
  if (existingMember) {
    throw new Error('User is already a member of this list');
  }
  
  // Add the new member
  this.members.push({
    userId,
    role,
    joinedAt: new Date(),
    invitedBy,
  });
  
  this.lastActivityAt = new Date();
  await this.save();
};

// Method to remove a member from the list
sharedGroceryListSchema.methods.removeMember = async function(userId: Types.ObjectId): Promise<void> {
  // Cannot remove the owner
  if (this.owner.toString() === userId.toString()) {
    throw new Error('Cannot remove the owner from the list');
  }
  
  const initialLength = this.members.length;
  this.members = this.members.filter((member: IListMember) => 
    member.userId.toString() !== userId.toString()
  );
  
  if (this.members.length === initialLength) {
    throw new Error('User is not a member of this list');
  }
  
  this.lastActivityAt = new Date();
  await this.save();
};

// Method to update a member's role
sharedGroceryListSchema.methods.updateMemberRole = async function(
  userId: Types.ObjectId, 
  newRole: 'editor' | 'viewer'
): Promise<void> {
  // Cannot change owner's role
  if (this.owner.toString() === userId.toString()) {
    throw new Error('Cannot change the owner\'s role');
  }
  
  const member = this.members.find((member: IListMember) => 
    member.userId.toString() === userId.toString()
  );
  
  if (!member) {
    throw new Error('User is not a member of this list');
  }
  
  member.role = newRole;
  this.lastActivityAt = new Date();
  await this.save();
};

// Method to check if a user is a member
sharedGroceryListSchema.methods.isMember = function(userId: Types.ObjectId): boolean {
  if (this.owner.toString() === userId.toString()) {
    return true;
  }
  
  return this.members.some((member: IListMember) => 
    member.userId.toString() === userId.toString()
  );
};

// Method to get a user's role in the list
sharedGroceryListSchema.methods.getMemberRole = function(userId: Types.ObjectId): 'owner' | 'editor' | 'viewer' | null {
  if (this.owner.toString() === userId.toString()) {
    return 'owner';
  }
  
  const member = this.members.find((member: IListMember) => 
    member.userId.toString() === userId.toString()
  );
  
  return member ? member.role : null;
};

// Method to check if a user can edit the list
sharedGroceryListSchema.methods.canEdit = function(userId: Types.ObjectId): boolean {
  const role = this.getMemberRole(userId);
  return role === 'owner' || role === 'editor';
};

// Method to check if a user can view the list
sharedGroceryListSchema.methods.canView = function(userId: Types.ObjectId): boolean {
  return this.isMember(userId);
};

export const SharedGroceryList = mongoose.model<ISharedGroceryList>(
  'SharedGroceryList',
  sharedGroceryListSchema
);
