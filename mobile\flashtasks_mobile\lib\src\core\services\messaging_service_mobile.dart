import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:ui';
import '../../../firebase_options.dart';
import 'messaging_service_interface.dart';

/// Background message handler - MUST BE A TOP-LEVEL FUNCTION
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase if not already done (required for background handler)
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  
  if (kDebugMode) {
    print("Handling a background message: ${message.messageId}");
    print('Message data: ${message.data}');
    if (message.notification != null) {
      print('Message notification: ${message.notification!.title}');
    }
  }
}

/// Mobile implementation of MessagingServiceInterface using Firebase Messaging
class MessagingService implements MessagingServiceInterface {
  static final MessagingService _instance = MessagingService._internal();
  
  factory MessagingService() => _instance;
  
  MessagingService._internal();
  
  // Local notifications plugin for displaying foreground notifications
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  // Android notification channel
  static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description: 'This channel is used for important notifications.', // description
    importance: Importance.high,
  );
  
  @override
  Future<void> initialize() async {
    // Initialize Firebase if not already done
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    
    // Set up local notifications
    await _setupLocalNotifications();
    
    // Request permission
    await _requestPermission();
    
    // Log FCM token for debugging
    await _logToken();
  }
  
  /// Request notification permission
  Future<void> _requestPermission() async {
    final messaging = FirebaseMessaging.instance;
    
    final settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    
    if (kDebugMode) {
      print('User granted permission: ${settings.authorizationStatus}');
    }
  }
  
  /// Set up local notifications for displaying when app is in foreground
  Future<void> _setupLocalNotifications() async {
    // Create Android notification channel
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(_channel);
    
    // Initialize local notifications
    await _localNotifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: _handleNotificationTap,
    );
  }
  
  /// Handle notification tap
  void _handleNotificationTap(NotificationResponse response) {
    // Extract payload and navigate accordingly
    if (response.payload != null) {
      if (kDebugMode) {
        print('Notification tapped with payload: ${response.payload}');
      }
      
      // TODO: Implement navigation based on payload
      // Example: if payload contains taskId, navigate to task details
    }
  }
  
  @override
  void setupForegroundNotificationHandling() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');
      }
      
      // Show notification if it contains a notification payload
      if (message.notification != null) {
        if (kDebugMode) {
          print('Message also contained a notification: ${message.notification}');
        }
        
        // Display the notification using local notifications plugin
        _showLocalNotification(message);
      }
    });
  }
  
  /// Show a local notification for foreground messages
  void _showLocalNotification(RemoteMessage message) {
    final notification = message.notification;
    final android = message.notification?.android;
    
    if (notification != null) {
      _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            _channel.id,
            _channel.name,
            channelDescription: _channel.description,
            icon: android?.smallIcon ?? 'mipmap/ic_launcher',
            color: const Color(0xFF0EA5E9), // Sky blue color to match app theme
          ),
          iOS: const DarwinNotificationDetails(),
        ),
        payload: message.data['route'] ?? '',
      );
    }
  }
  
  @override
  void setupBackgroundMessaging() {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }
  
  @override
  void setupNotificationTapHandling() {
    // Listen for notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('A notification was tapped from background state!');
        print('Message data: ${message.data}');
      }
      
      // TODO: Implement navigation based on message data
      // Example: if message.data contains taskId, navigate to task details
    });
    
    // Check for initial message (app opened from terminated state via notification)
    _checkInitialMessage();
  }
  
  /// Check for initial message (app opened from terminated state via notification)
  Future<void> _checkInitialMessage() async {
    // Get any messages which caused the application to open from a terminated state
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    
    if (initialMessage != null) {
      if (kDebugMode) {
        print('App opened from terminated state via notification');
        print('Initial message data: ${initialMessage.data}');
      }
      
      // TODO: Implement navigation based on initial message data
    }
  }
  
  /// Log FCM token for debugging
  Future<void> _logToken() async {
    final token = await FirebaseMessaging.instance.getToken();
    if (kDebugMode) {
      print('FCM Token: $token');
    }
  }
  
  @override
  Future<String?> getToken() async {
    return await FirebaseMessaging.instance.getToken();
  }
  
  @override
  Future<void> subscribeToTopic(String topic) async {
    await FirebaseMessaging.instance.subscribeToTopic(topic);
    if (kDebugMode) {
      print('Subscribed to topic: $topic');
    }
  }
  
  @override
  Future<void> unsubscribeFromTopic(String topic) async {
    await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
    if (kDebugMode) {
      print('Unsubscribed from topic: $topic');
    }
  }
} 