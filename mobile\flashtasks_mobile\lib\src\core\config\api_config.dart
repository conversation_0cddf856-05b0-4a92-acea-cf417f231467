import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb, kDebugMode, kReleaseMode;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/network_provider.dart';

/// Configuration for API endpoints
class ApiConfig {
  /// Base URL for the API - Dynamically set based on platform
  static String baseUrl = getBaseUrl();

  /// Global provider container reference, set at app startup
  static ProviderContainer? _container;

  /// Production API URL - Replace with your actual production backend URL
  static const String productionApiUrl = 'https://flashtasks-ai.onrender.com';

  /// Is the app running in a production environment
  static bool _isProduction = false;

  /// Public getter for production status
  static bool get isProduction => _isProduction;

  /// Set the provider container for notifications
  static void setProviderContainer(ProviderContainer container) {
    _container = container;
  }

  /// Initialize the API config by loading any saved custom URL
  /// Must be called at app startup
  static Future<void> initialize() async {
    // Determine if we're in production mode
    _detectProductionMode();

    // If in production mode, always use production URL
    if (_isProduction) {
      baseUrl = productionApiUrl;
      print('API using production URL: $baseUrl');
      return;
    }

    // For development, check for saved custom URL
    final prefs = await SharedPreferences.getInstance();
    final savedUrl = prefs.getString('custom_server_url');

    if (savedUrl != null && savedUrl.isNotEmpty) {
      baseUrl = savedUrl;
      print('API base URL loaded from storage: $baseUrl');
    } else {
      print('No saved API URL found, using default: $baseUrl');
    }
  }

  /// Detect if app is running in production mode (Firebase deployed)
  static void _detectProductionMode() {
    try {
      // Use kReleaseMode to check for production build
      _isProduction = kReleaseMode;

      // In release mode, always use production URL
      if (_isProduction) {
        baseUrl = productionApiUrl;
      }

      print('App running in ${_isProduction ? 'PRODUCTION' : 'DEVELOPMENT'} mode');
      print('Using API URL: $baseUrl');
    } catch (e) {
      print('Error detecting production mode: $e');
      _isProduction = false;
    }
  }

  /// Get the appropriate base URL for the current platform
  static String getBaseUrl() {
    // For release builds, always use production URL regardless of platform
    if (kReleaseMode) {
      return productionApiUrl;
    }

    // For debug builds, use appropriate development URLs
    if (kIsWeb) {
      return 'http://localhost:3001'; // Web
    } else if (Platform.isAndroid) {
      return 'http://********:3001'; // Android emulator
    } else if (Platform.isIOS) {
      return 'http://localhost:3001'; // iOS simulator
    } else {
      return 'http://localhost:3001'; // Default fallback
    }
  }

  // API path prefix
  static const String apiPrefix = '/api';

  // Auth endpoints
  static const String authPrefix = '$apiPrefix/auth';
  static const String loginEndpoint = '$authPrefix/login';
  static const String registerEndpoint = '$authPrefix/register';
  static const String refreshEndpoint = '$authPrefix/refresh';
  static const String logoutEndpoint = '$authPrefix/logout';
  static const String validateTokenEndpoint = '$authPrefix/validate-token';
  static const String googleLoginEndpoint = '$authPrefix/google';

  // User endpoints
  static const String usersPrefix = '$apiPrefix/users';
  static const String currentUserEndpoint = '$usersPrefix/profile';
  static const String changePasswordEndpoint = '$usersPrefix/change-password';
  static const String deleteAccountEndpoint = '$usersPrefix/account';

  // Settings endpoints
  static const String settingsPrefix = '$apiPrefix/settings';
  static const String settingsEndpoint = settingsPrefix;
  static const String settingsAllEndpoint = '$usersPrefix/settings/all';
  static const String preferencesEndpoint = '$usersPrefix/settings/preferences';
  static const String taskSettingsEndpoint = '$usersPrefix/settings/task-settings';
  static const String notificationSettingsEndpoint = '$usersPrefix/settings/notification-settings';
  static const String dateTimeSettingsEndpoint = '$usersPrefix/settings/datetime-settings';
  static const String keytagMappingsEndpoint = '$usersPrefix/settings/keytag-mappings';
  static const String categoryLocationMappingsEndpoint = '$usersPrefix/settings/category-location-mappings';
  static const String locationSuggestionSettingsEndpoint = '$usersPrefix/settings/location-suggestion-settings';

  // Tasks endpoints
  static const String tasksEndpoint = '$apiPrefix/tasks';
  static String taskByIdEndpoint(String taskId) => '$tasksEndpoint/$taskId';
  static String taskToggleEndpoint(String taskId) => '$tasksEndpoint/$taskId/toggle';
  static String taskCompletionEndpoint(String taskId) => '$tasksEndpoint/$taskId/toggle';
  static String tasksByLocationEndpoint(String locationId) => '$tasksEndpoint/location/$locationId';

  // Categories endpoints
  static const String categoriesEndpoint = '$apiPrefix/categories';
  static String categoryByIdEndpoint(String categoryId) => '$categoriesEndpoint/$categoryId';
  // Locations endpoints
  static const String locationsEndpoint = '$apiPrefix/locations';
  static String locationByIdEndpoint(String locationId) => '$locationsEndpoint/$locationId';

  // Groceries endpoints
  static const String groceriesEndpoint = '$apiPrefix/groceries'; // Legacy V1
  static const String v2GroceriesEndpoint = '$apiPrefix/v2/groceries';
  static String groceryItemByIdEndpoint(String itemId) => '$groceriesEndpoint/$itemId';
  static const String groceryDeleteCheckedEndpoint = '$groceriesEndpoint/checked';
  static const String grocerySuggestionsEndpoint = '$groceriesEndpoint/suggestions';
  static const String groceryInsightsEndpoint = '$groceriesEndpoint/insights';
  static const String groceryUpdateCategoriesEndpoint = '$groceriesEndpoint/categories'; // Use the standard groceries endpoint, not v2
  static const String groceryLibrarySearchEndpoint = '$groceriesEndpoint/library/search';

  // NEW: Collaboration endpoints
  static const String groceryListEndpoint = '$groceriesEndpoint/list';
  static const String groceryListSharingEndpoint = '$groceriesEndpoint/share';
  static const String groceryListUnsharingEndpoint = '$groceriesEndpoint/unshare';
  static const String groceryShareSettingsEndpoint = '$groceriesEndpoint/share-settings';
  static const String sharedGroceryListsEndpoint = '$groceriesEndpoint/shared';
  static const String groceryInvitationsEndpoint = '$groceriesEndpoint/invitations/pending';
  static const String groceryCollaboratorsEndpoint = '$groceriesEndpoint/collaborators';
  static String groceryInvitationAcceptEndpoint(String token) => '$groceriesEndpoint/invitations/$token/accept';
  static String groceryInvitationDeclineEndpoint(String token) => '$groceriesEndpoint/invitations/$token/decline';
  static String groceryCollaboratorRemoveEndpoint(String collaboratorId) => '$groceryCollaboratorsEndpoint/$collaboratorId';

  // AI endpoints
  static const String aiPrefix = '$apiPrefix/ai';
  static const String aiQuickAddEndpoint = '$aiPrefix/quick-add';
  static const String aiInsightsEndpoint = '$aiPrefix/insights';

  // API v2 uses POST /insights/:id/action instead of PATCH /insights/:id
  static String aiInsightActionEndpoint(String insightId) => '$aiInsightsEndpoint/$insightId/action';
  static const String aiGenerateInsightsEndpoint = '$aiPrefix/generate-insights';
  static const String aiAskEndpoint = '$aiPrefix/ask';

  // Notifications endpoints
  static const String notificationsPrefix = '$apiPrefix/notifications';
  static const String registerPushTokenEndpoint = '$notificationsPrefix/push-token';
  static const String unregisterPushTokenEndpoint = '$notificationsPrefix/push-token';

  // System health check (remains at root API level)
  static const String pingEndpoint = '$apiPrefix/ping';

  /// Update the base URL (for testing different connections)
  static void updateBaseUrl(String newUrl) {
    // Don't allow URL updates in production mode
    if (_isProduction) {
      print('Warning: Cannot update base URL in production mode');
      return;
    }

    baseUrl = newUrl;
    print('API base URL updated to: $baseUrl');

    // Save to shared preferences for persistence
    SharedPreferences.getInstance().then((prefs) {
      prefs.setString('custom_server_url', newUrl);
    });

    // Force refresh any providers that depend on this URL
    // This is required to ensure all API clients get updated
    _notifyUrlChanged();
  }

  // Use this to notify listeners that the URL has changed
  static void _notifyUrlChanged() {
    // Refresh network providers if container is set
    if (_container != null) {
      refreshNetworkProviders(_container!);
      print('Network providers refreshed with new URL: $baseUrl');
    } else {
      print('Warning: Provider container not set, cannot refresh network providers');
    }
  }

  /// Get absolute URL for an endpoint
  static String getAbsoluteUrl(String endpoint) {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    if (!endpoint.startsWith('/')) {
      endpoint = '/$endpoint';
    }
    return '$baseUrl$endpoint';
  }
}
