Okay, let'
s devise a comprehensive plan for implementing robust user settings in the Flutter mobile app, ensuring feature parity with the web app and adding some mobile-specific creative touches.

**I. Core Principles for Mobile Settings**

1.  **Native Feel:** Settings should feel like a natural part of the mobile experience, using platform-appropriate controls and navigation.
2.  **Discoverability:** Key settings should be easy to find.
3.  **Clarity:** Options should be clearly explained.
4.  **Performance:** Settings screens should load quickly and respond smoothly.
5.  **Offline Considerations (Future):** While not MVP, think about how settings might sync if changed offline. For now, assume online-only for saving.
6.  **Consistency with Web:** Strive for functional parity, even if the UI presentation differs.
7.  **Accessibility:** Ensure settings are usable by everyone.

**II. Settings Structure (Mirroring Web where logical, adapting for Mobile)**

Based on your web app's settings structure (`frontend/app/settings/`), we'll aim for a similar organization.

*   **Main Settings Screen (`settings_screen.dart`):**
    *   Entry point for all settings, likely a `ListView` of setting categories.
    *   Sections: Account, Application, AI & Automation, Developer (as per your web layout).

*   **Sub-Screens (Navigated from Main Settings):**
    1.  **Profile Settings (`profile_settings_screen.dart`):**
        *   Edit Name
        *   Edit Email (with re-verification flow if email changes)
        *   Avatar Management (Upload/Select - if applicable on mobile)
    2.  **Account & Security Settings (`account_settings_screen.dart`):**
        *   Change Password
        *   Two-Factor Authentication (Future Enhancement)
        *   Connected Devices/Sessions (Future Enhancement)
        *   Delete Account (with proper confirmation)
    3.  **Task Settings (`task_settings_screen.dart`):**
        *   Default Task View (List, Board, Calendar - if mobile supports these)
        *   Default Sort Field & Order
        *   Default Deadline Time
        *   Auto-archive Completed Tasks (after X days)
        *   Auto-hide Completed Tasks (after X days)
        *   Default Reminder Offset (e.g., 30 minutes before)
    4.  **Notification Settings (`notification_settings_screen.dart`):**
        *   Toggle Email Notifications
        *   Toggle Push Notifications (per device - manage current device's registration)
        *   Toggle In-App Notifications
        *   Reminder Escalation (Enable, Configure intervals)
    5.  **Date & Time Settings (`datetime_settings_screen.dart`):**
        *   Date Format (MM/DD/YYYY, DD/MM/YYYY, etc.)
        *   Time Format (12-hour, 24-hour)
        *   Timezone (Allow selection, or use device timezone with option to override)
    6.  **AI & Automation Settings:**
        *   **Keytag Mappings (`keytag_mapping_settings_screen.dart`):**
            *   View, Add, Remove keyword -> category mappings.
            *   Toggle: Enable Fuzzy Matching
            *   Slider: Fuzzy Match Threshold
            *   List: Excluded Keywords (Add/Remove)
            *   Toggle: Apply to Manually Created Tasks
        *   **Category-Location Mappings (`category_location_mapping_settings_screen.dart`):**
            *   View, Add, Remove category -> location mappings.
            *   View, Add, Remove category aliases for locations.
            *   Toggle: Auto-map new tasks without confirmation.
            *   Toggle: Enable Fuzzy Matching for category-location.
            *   Slider: Fuzzy Match Threshold for category-location.
            *   Select: Default Location ID (if no mapping found).
            *   List: Excluded Categories (from auto-mapping).
            *   Map/List: Category Priority for mapping conflicts.
        *   **Location Suggestion Settings (`location_suggestion_settings_screen.dart`):**
            *   Sliders for weights (Proximity, Keyword, Category Mapping, AI Hint).
            *   Input: Max Distance Threshold.
            *   Toggle: Non-Linear Proximity Decay.
        *   **General AI Settings (New/Combined Screen or part of a general AI settings screen):**
            *   Toggle: Enable/Disable AI Autocorrection.
            *   Toggle: Enable/Disable AI Task Decomposition suggestions.
            *   Toggle: Enable/Disable AI Deadline/Time Slotting suggestions.
            *   Picker: AI Confidence Threshold for "Confirm AI Interpretation" insights (e.g., only show for "Low" confidence).
    7.  **Developer Settings (`server_settings_screen.dart`):**
        *   Configure Server Connection (IP/Port for local backend during dev).
        *   Test Connection button.
        *   Reset to Default URL button.

**III. Data Models & State Management (Flutter - Riverpod)**

1.  **Models (`mobile/flashtasks_mobile/lib/src/features/settings/models/`)**:
    *   You already have `user_all_settings.dart`, `user_preferences.dart`, `task_settings.dart`, `notification_settings.dart`, `datetime_settings.dart`, `keytag_mapping_settings.dart`, `category_location_mapping_settings.dart`, `location_suggestion_settings.dart`.
    *   **Ensure `fromJson` and `toJson` methods are robust and handle all fields, including new AI settings.**
    *   **Consider null safety and default values carefully.** If the backend might not send a field, ensure your `fromJson` provides a sensible default.

2.  **Providers (`mobile/flashtasks_mobile/lib/src/features/settings/providers/settings_providers.dart`)**:
    *   The existing `UserSettingsNotifier` and `UserSettingsState` are a good foundation.
    *   **`_fetchSettings()`:** Ensure this fetches ALL settings structures from the backend using `settingsService.getAllUserSettings()`.
    *   **Update Methods:**
        *   Instead of individual update methods for each setting type (e.g., `updateTaskSettings`), it's more efficient to have a single `updateAllSettings(UserAllSettings settings)` method that sends the entire updated settings object to the backend. This mirrors the web frontend's `PUT /users/settings/all`.
        *   If you want granular updates on the UI (e.g., user changes one toggle and it saves immediately), the Notifier methods can update the local state optimistically and then call `_settingsService.updateAllUserSettings(state.settings!)`.
        *   Alternatively, provide a "Save Changes" button on each settings sub-screen, which then calls `ref.read(userSettingsProvider.notifier).updateAllSettings(locallyModifiedSettings)`. This might be better for UX to prevent too many API calls.

3.  **Services (`mobile/flashtasks_mobile/lib/src/core/services/settings_service.dart`)**:
    *   `getAllUserSettings()`: Should call the backend's `/api/users/settings/all` (or similar consolidated endpoint). Ensure the response is correctly parsed into `UserAllSettings`.
    *   `updateAllUserSettings(UserAllSettings settings)`: Should send a `PUT` request to `/api/users/settings/all` with the complete settings object.

**IV. UI Implementation (Flutter Widgets)**

1.  **Main Settings Screen (`settings_screen.dart`):**
    *   Use `ListView.separated` to display sections and items.
    *   Each item should be a `ListTile` navigating to the respective sub-screen.
    *   Use `LucideIcons` for visual consistency.

2.  **Profile Settings Screen (`profile_settings_screen.dart`):**
    *   Form with `TextFormField` for Name and Email.
    *   Button for "Save Profile".
    *   Consider adding avatar upload/selection if mobile supports it easily (e.g., using `image_picker`).

3.  **Account Settings Screen (`account_settings_screen.dart`):**
    *   "Change Password" section:
        *   Form with `TextFormField` for Current Password, New Password, Confirm New Password.
        *   Toggle for password visibility.
    *   "Delete Account" section:
        *   Prominent warning.
        *   Button to trigger a confirmation dialog before actual deletion.

4.  **Sub-Settings Screens (General Pattern):**
    *   Each screen will load its specific part of the `UserAllSettings` from the `userSettingsProvider`.
    *   Use appropriate Flutter widgets:
        *   `SwitchListTile` for boolean toggles.
        *   `DropdownButtonFormField` or custom dropdowns for selections (Date Format, Time Format).
        *   `Slider` for numerical ranges (Fuzzy Match Threshold, Reminder Offset).
        *   `TextFormField` with `keyboardType: TextInputType.number` for numerical inputs.
        *   For lists like "Excluded Keywords" or "Reminder Intervals":
            *   Display existing items (e.g., using `Wrap` with `Chip` widgets).
            *   Provide an "Add" button that opens a small dialog or an inline `TextField` to add a new item.
            *   Each `Chip` should have a delete icon.
    *   **Saving:**
        *   **Option A (Save per screen):** Each screen has a "Save Changes" button. When pressed, it updates the relevant part of the `UserAllSettings` object in the `UserSettingsNotifier` and then calls `notifier.updateAllUserSettings(updatedFullSettingsObject)`.
        *   **Option B (Save per change - more reactive):** Each individual setting control (Switch, Slider, etc.) directly calls a method on the `UserSettingsNotifier` to update that specific field and then trigger `updateAllUserSettings`. This can lead to many API calls but provides immediate feedback. For Alpha, Option A is often simpler to manage.

5.  **Keytag & Category-Location Mapping Screens:**
    *   **Displaying Mappings:** Use `ListView.builder` or `Column` with `Card` for each mapping.
        *   Show "Keyword -> Category Name" or "Category Name -> Location Name".
        *   Provide "Edit" and "Delete" buttons for each mapping.
    *   **Adding/Editing Mappings:** Use a `Dialog` or a separate form screen.
        *   `DropdownButtonFormField` to select existing Categories/Locations.
        *   `TextFormField` for new keywords/aliases.
    *   **Categories/Locations Data:** These screens will need to asynchronously load all available categories and locations (using `categoryProvider` and `locationsProvider`) to populate dropdowns for selection.

6.  **Creative Mobile-Specific Ideas:**
    *   **Quick Toggles on Main Settings:** For very common settings (e.g., "Dark Mode", "Push Notifications On/Off"), consider placing them directly on the main `SettingsScreen` for quick access, in addition to their detailed sub-screens.
    *   **"Test Notification" Button:** On the `NotificationSettingsScreen`, include a button that sends a test push notification to the current device to confirm settings are working.
    *   **Visual Feedback for Sliders:** As the user drags a slider (e.g., for fuzzy matching), display the current value dynamically.
    *   **Permission Checks & Prompts:** For settings that rely on device permissions (e.g., location for "Proximity Weight" in location suggestions), check the permission status. If not granted, show an informative message and a button to "Grant Permission" which navigates the user to app settings or uses `permission_handler` to request it.
    *   **AI Setting Explanations:** For AI-related toggles (Autocorrection, Sub-task Suggestions), include a small info icon (`LucideIcons.info`) next to them. Tapping it could show a bottom sheet or dialog explaining what the feature does and its potential impact.
    *   **"Reset to Defaults" for Sections:** Each major settings section (Tasks, Notifications, AI) could have a "Reset to Defaults" button.

**V. Implementation Steps (High-Level)**

1.  **Backend First (Verify):**
    *   Ensure the `/api/users/settings/all` GET endpoint in `UserSettingsControllerV2` correctly returns the entire `UserAllSettings` structure, including all nested objects with their defaults if not set by the user.
    *   Ensure the `/api/users/settings/all` PUT endpoint correctly receives the entire `UserAllSettings` structure and updates the `UserSettings` document in MongoDB. The `deepMerge` logic in `UserSettingsControllerV2` is crucial here.

2.  **Flutter Model Parity:**
    *   Thoroughly review and update all models in `mobile/flashtasks_mobile/lib/src/features/settings/models/` to match the latest backend `UserSettings.ts` structure and frontend web `settings.model.ts`. Pay close attention to `fromJson` for all nested objects.

3.  **Flutter Service Layer (`SettingsService`):**
    *   Implement `getAllUserSettings()` to fetch all settings.
    *   Implement `updateAllUserSettings(UserAllSettings settings)` to save all settings.

4.  **Flutter Provider (`UserSettingsNotifier`):**
    *   Modify `_fetchSettings` to call `_settingsService.getAllUserSettings()`.
    *   Modify update methods (e.g., `updateTaskSettings`, `updateNotificationSettings`) to:
        1.  Create a *copy* of the current `state.settings`.
        2.  Update the specific part of that copied settings object.
        3.  Call `state = state.copyWith(settings: newFullSettingsObject, isLoading: true);` (optimistic update).
        4.  Call `await _settingsService.updateAllUserSettings(newFullSettingsObject);`.
        5.  On success, `state = state.copyWith(isLoading: false);`.
        6.  On error, revert state and set error: `state = state.copyWith(settings: originalSettings, isLoading: false, error: e.toString());`.

5.  **UI Screens (Iterative Development):**
    *   Start with the main `SettingsScreen` to list and navigate to sub-sections.
    *   Implement each sub-screen one by one:
        *   Fetch relevant part of settings from `userSettingsProvider`.
        *   Build UI with appropriate widgets.
        *   Implement "Save" logic that calls the relevant update method on `UserSettingsNotifier`.
    *   For Keytag and Category-Location mappings:
        *   Load existing categories and locations for dropdowns.
        *   Implement UI for adding, viewing, and deleting individual mappings.

**VI. Error Handling & User Feedback (Mobile)**

*   Use `try-catch` blocks in Notifier methods when calling services.
*   Update `state = state.copyWith(isLoading: false, error: e.toString());` on error.
*   Display errors to the user using `ScaffoldMessenger.of(context).showSnackBar(...)` or inline error messages.
*   Provide loading indicators (`CircularProgressIndicator`) while settings are being fetched or saved.

This plan provides a detailed roadmap. Remember to implement and test incrementally. Start with the data flow (models, services, provider), then build out the UI screens one by one. The AI & Automation settings will likely be the most complex due to their nested nature and interaction with other data like categories and locations.