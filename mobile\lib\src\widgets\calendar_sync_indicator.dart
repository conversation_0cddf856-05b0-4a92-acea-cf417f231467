import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/calendar_service.dart';
import '../models/task.dart';

class CalendarSyncIndicator extends ConsumerWidget {
  final Task task;

  const CalendarSyncIndicator({
    Key? key,
    required this.task,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarService = ref.watch(calendarServiceProvider);
    final syncStatus = calendarService.getTaskSyncStatus(task);
    final lastSync = calendarService.getTaskLastSync(task);

    if (syncStatus == null) return const SizedBox.shrink();

    Color getStatusColor() {
      switch (syncStatus) {
        case 'synced':
          return Colors.green;
        case 'failed':
          return Colors.red;
        case 'pending':
          return Colors.grey;
        default:
          return Colors.grey;
      }
    }

    IconData getStatusIcon() {
      switch (syncStatus) {
        case 'synced':
          return Icons.check_circle_outline;
        case 'failed':
          return Icons.error_outline;
        case 'pending':
          return Icons.sync;
        default:
          return Icons.calendar_today;
      }
    }

    String getTooltipText() {
      switch (syncStatus) {
        case 'synced':
          return lastSync != null
              ? 'Synced to Google Calendar on ${lastSync.toLocal()}'
              : 'Synced to Google Calendar';
        case 'failed':
          return 'Failed to sync with Google Calendar';
        case 'pending':
          return 'Syncing with Google Calendar...';
        default:
          return '';
      }
    }

    return Tooltip(
      message: getTooltipText(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.calendar_today,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Icon(
            getStatusIcon(),
            size: 16,
            color: getStatusColor(),
          ),
        ],
      ),
    );
  }
}
