import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_recognition_error.dart';

import 'speech_service_interface.dart';

/// Mobile implementation of SpeechServiceInterface using speech_to_text package
class SpeechService implements SpeechServiceInterface {
  final SpeechToText _speech = SpeechToText();
  String _finalText = '';
  Function(String)? _onStatusCallback;
  Function(double)? _onSoundLevelCallback;

  // Keep track of the max sound level to normalize values
  double _maxSoundLevel = 1.0;

  @override
  String get finalText => _finalText;

  @override
  bool get isListening => _speech.isListening;

  @override
  Future<bool> initialize() async {
    try {
      final bool available = await _speech.initialize(
        onError: (SpeechRecognitionError error) {
          debugPrint('Speech recognition error: ${error.errorMsg}');
        },
        debugLogging: kDebugMode,
        onStatus: (status) {
          if (_onStatusCallback != null) {
            _onStatusCallback!(status);
          }
        },
      );
      return available;
    } catch (e) {
      debugPrint('Error initializing speech recognition: $e');
      return false;
    }
  }

  @override
  Future<bool> startListening({
    Function(String text)? onResult,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
  }) async {
    // Reset final text and sound level tracking
    _finalText = '';
    _maxSoundLevel = 1.0;
    _onStatusCallback = onStatus;
    _onSoundLevelCallback = onSoundLevel;

    return await _speech.listen(
      onResult: (SpeechRecognitionResult result) {
        _finalText = result.recognizedWords;
        if (onResult != null) {
          onResult(result.recognizedWords);
        }
      },
      listenFor: const Duration(seconds: 45), // Increased from 30s
      pauseFor: const Duration(seconds: 3), // 3 seconds minimum thinking time before auto-stop
      partialResults: true,
      onSoundLevelChange: (level) {
        // Use sound level for visual feedback
        if (level > _maxSoundLevel) {
          _maxSoundLevel = level;
        }

        // Normalize the level to a 0.0-1.0 range
        double normalizedLevel = level / _maxSoundLevel;

        if (_onSoundLevelCallback != null) {
          _onSoundLevelCallback!(normalizedLevel);
        }
      },
      cancelOnError: true,
      listenMode: ListenMode.confirmation,
    );
  }

  @override
  Future<void> stopListening() async {
    await _speech.stop();
  }

  @override
  Future<bool> isAvailable() async {
    return _speech.isAvailable;
  }

  @override
  void dispose() {
    _speech.cancel();
  }
}