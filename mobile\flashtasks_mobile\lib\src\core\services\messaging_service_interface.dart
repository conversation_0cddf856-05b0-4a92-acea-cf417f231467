
/// Interface for messaging service
abstract class MessagingServiceInterface {
  /// Initialize the messaging service
  Future<void> initialize();
  
  /// Get the device token
  Future<String?> getToken();
  
  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic);
  
  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic);
  
  /// Register for foreground messages
  void setupForegroundNotificationHandling();
  
  /// Set up background messaging (only for mobile platforms)
  void setupBackgroundMessaging();
  
  /// Handle notification tap
  void setupNotificationTapHandling();
} 