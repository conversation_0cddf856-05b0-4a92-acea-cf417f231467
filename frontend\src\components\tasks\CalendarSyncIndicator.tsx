import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, TooltipTrigger } from '../../../components/ui/tooltip';
import { Calendar, Check, X, Loader2 } from 'lucide-react';

interface CalendarSyncIndicatorProps {
  syncStatus?: 'pending' | 'synced' | 'failed';
  lastSync?: Date | null;
  // Backward compatibility - accept task object
  task?: any;
}

export const CalendarSyncIndicator: React.FC<CalendarSyncIndicatorProps> = ({
  syncStatus,
  lastSync,
  task,
}) => {
  // Backward compatibility: extract from task if provided
  // Backend stores calendar sync in metadata object
  const actualSyncStatus = syncStatus || task?.metadata?.googleCalendarSyncStatus;
  const actualLastSync = lastSync || (task?.metadata?.googleCalendarLastSync ? new Date(task.metadata.googleCalendarLastSync) : null);

  if (!actualSyncStatus) return null;

  const getIcon = () => {
    switch (actualSyncStatus) {
      case 'synced':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <X className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getTooltipText = () => {
    switch (actualSyncStatus) {
      case 'synced':
        return `Synced to Google Calendar${actualLastSync ? ` on ${actualLastSync.toLocaleString()}` : ''}`;
      case 'failed':
        return 'Failed to sync with Google Calendar';
      case 'pending':
        return 'Syncing with Google Calendar...';
      default:
        return '';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            {getIcon()}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
