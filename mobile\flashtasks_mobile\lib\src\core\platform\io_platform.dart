// IO platform implementation (mobile)
import 'dart:io' as io;

// Platform detection - directly from dart:io
final bool isAndroid = io.Platform.isAndroid;
final bool isIOS = io.Platform.isIOS;
final bool isLinux = io.Platform.isLinux;
final bool isMacOS = io.Platform.isMacOS;
final bool isWindows = io.Platform.isWindows;
final bool isFuchsia = io.Platform.isFuchsia;
const bool isWeb = false;

// Platform information
String get operatingSystem => io.Platform.operatingSystem;
String get operatingSystemVersion => io.Platform.operatingSystemVersion;

// Platform helper functions
bool isAndroidPlatform() => isAndroid;
bool isIOSPlatform() => isIOS; 