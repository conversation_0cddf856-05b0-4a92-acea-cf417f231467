import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/grocery_provider.dart';
import '../widgets/grocery_item_widget.dart';
import '../widgets/grocery_quick_add_widget.dart';
import '../widgets/shared_list_selector.dart';
import '../widgets/collaboration_panel.dart';


/// Screen for managing shared grocery lists
class SharedGroceriesScreen extends ConsumerStatefulWidget {
  const SharedGroceriesScreen({super.key});

  @override
  ConsumerState<SharedGroceriesScreen> createState() => _SharedGroceriesScreenState();
}

class _SharedGroceriesScreenState extends ConsumerState<SharedGroceriesScreen> {
  @override
  void initState() {
    super.initState();
    // Refresh data when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(groceryProvider.notifier).fetchSharedLists();
      ref.read(groceryProvider.notifier).fetchPendingInvitations();
    });
  }

  @override
  Widget build(BuildContext context) {
    final groceryState = ref.watch(groceryProvider);
    final groceryNotifier = ref.read(groceryProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Shared Groceries'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // Collaboration panel button
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.people),
                if (groceryState.pendingInvitations.isNotEmpty)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      ),
                      child: Text(
                        '${groceryState.pendingInvitations.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () => _showCollaborationPanel(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Shared list selector
          if (groceryState.sharedLists.isNotEmpty)
            SharedListSelector(
              sharedLists: groceryState.sharedLists,
              currentListOwnerId: groceryState.currentListOwnerId,
              onListSelected: (listOwnerId) {
                if (listOwnerId != null) {
                  groceryNotifier.switchToSharedList(listOwnerId);
                } else {
                  groceryNotifier.switchToPersonalList();
                }
              },
            ),

          // Quick add widget
          if (groceryState.currentListOwnerId != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: GroceryQuickAddWidget(
                onAddItem: (name, {quantity, categoryName}) {
                  groceryNotifier.addItem(name, quantity: quantity, categoryName: categoryName);
                },
              ),
            ),

          // Content area
          Expanded(
            child: _buildContent(context, groceryState, groceryNotifier),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, GroceryState state, GroceryNotifier notifier) {
    // Show loading state
    if (state.isLoadingSharedLists) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Show error state
    if (state.errorSharedLists != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading shared lists',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.errorSharedLists!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => notifier.fetchSharedLists(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Show empty state
    if (state.sharedLists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Shared Lists',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'You don\'t have access to any shared grocery lists yet.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _showCollaborationPanel(context),
              child: const Text('Check Invitations'),
            ),
          ],
        ),
      );
    }

    // Show grocery items if a list is selected
    if (state.currentListOwnerId != null) {
      return _buildGroceryList(context, state, notifier);
    }

    // Show list selection prompt
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.list_alt,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Select a Shared List',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Choose a shared grocery list from the dropdown above to view and edit items.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGroceryList(BuildContext context, GroceryState state, GroceryNotifier notifier) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text('Error loading items', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(state.error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => notifier.fetchGroceries(listOwnerId: state.currentListOwnerId),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.shopping_cart_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text('No Items', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(
              'This shared list is empty. Add some items to get started!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.items.length,
      itemBuilder: (context, index) {
        final item = state.items[index];
        return GroceryItemWidget(
          item: item,
          onToggleCheck: () => notifier.toggleCheck(item.id),
          onDelete: () => notifier.deleteItem(item.id),
          onUpdate: (updates) => notifier.updateItem(item.id, updates),
          showCollaborationInfo: true, // Show who added/modified items
        );
      },
    );
  }

  void _showCollaborationPanel(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) => CollaborationPanel(
          scrollController: scrollController,
        ),
      ),
    );
  }
}
