'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { format, addHours, isBefore } from 'date-fns'; // Date utility functions
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { AlertCircle, AlertTriangle, Calendar as CalendarIcon, CalendarDays, CalendarOff, Check, Clock, Edit, ExternalLink, Loader2, List, Grid, MoveRight, PanelLeft, Plus, Trash } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { taskService } from '@/lib/task-service';
import type { TaskModel, TaskPriority, TaskFilters } from '@/lib/types/task.model';
import type { LocationModel } from '@/lib/types/location.model';
import { useToast } from '@/components/ui/use-toast';
import { CalendarEvent, mapTaskToCalendarEvent } from '@/lib/calendar-utils'; // Calendar utilities
import { NewTaskDialog, type TaskFormValues } from '../new-task-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

type CalendarView = 'dayGridMonth' | 'timeGridWeek' | 'timeGridDay' | 'listWeek';

// Define view buttons for the calendar
const VIEW_BUTTONS = {
  dayGridMonth: { icon: Grid, label: 'Month' },
  timeGridWeek: { icon: CalendarDays, label: 'Week' },
  timeGridDay: { icon: CalendarIcon, label: 'Day' },
  listWeek: { icon: List, label: 'List' }
};

const PRIORITY_COLORS = {
  Low: 'bg-blue-100 text-blue-800',
  Medium: 'bg-green-100 text-green-800',
  High: 'bg-yellow-100 text-yellow-800',
  Critical: 'bg-red-100 text-red-800',
};

interface ExtendedTaskFormValues extends Omit<TaskFormValues, 'id' | 'priority'> {
  id?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  locationId?: string | null;
}

interface CalendarEventExtendedProps {
  type: 'task' | 'event';
  taskId?: string;
  completed?: boolean;
  priority?: TaskPriority;
  description?: string;
  location?: string | LocationModel;
  categories?: any[];
  reminders?: any[];
  syncStatus?: 'synced' | 'pending' | 'failed' | 'not_synced';
  syncProvider?: 'google' | 'outlook' | 'apple' | string;
  syncLastUpdated?: string | Date;
}

export default function FullCalendarComponent() {
  const router = useRouter();
  const { toast } = useToast();
  const calendarRef = useRef<FullCalendar>(null);
  // Track current view for UI state
  const [currentView, setCurrentView] = useState<CalendarView>('dayGridMonth');
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  // Keep selectedEvent state for potential future use
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [formData, setFormData] = useState<ExtendedTaskFormValues>({
    title: '',
    description: '',
    dueDate: new Date(),
    dueTime: format(new Date(), 'HH:mm'),
    priority: 'medium' as const,
    categories: [],
    reminders: [],
    useAI: true
  });
  const [isNewTaskDialogOpen, setIsNewTaskDialogOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [categories, setCategories] = useState<{id: string; name: string; color: string}[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const fetchTasks = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Set up filters to get all tasks
      const filters: TaskFilters = {
        status: 'all',
      };
      
      // Fetch tasks from the API
      const response = await taskService.getTasks(filters);
      
      // Map tasks to calendar events
      const calendarEvents = response.data.map((task: TaskModel) => {
        // Use deadline for due date if available, otherwise use current date
        const dueDate = task.deadline ? new Date(task.deadline) : new Date();
        
        // Map task priority to lowercase for consistency
        const priority = task.priority ? task.priority.toLowerCase() as 'low' | 'medium' | 'high' | 'critical' : 'medium';
        
        // Handle location - it can be a string, LocationModel, or undefined
        let location: string | { name: string } | undefined = undefined;
        if (task.location) {
          location = typeof task.location === 'string' 
            ? task.location 
            : { name: task.location.name || 'Location' };
        }
        
        return mapTaskToCalendarEvent(task);
      });
      
      setEvents(calendarEvents);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast({
        title: 'Error',
        description: 'Failed to load tasks. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const fetchCategories = useCallback(async () => {
    try {
      setIsLoadingCategories(true);
      // Use the API to get categories
      const response = await fetch('/api/categories');
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: 'Error',
        description: 'Failed to load categories. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingCategories(false);
    }
  }, [toast]);

  // Fetch tasks and categories on component mount
  useEffect(() => {
    fetchTasks();
    fetchCategories();
    
    const handleResize = () => {
      const isMobileScreen = window.innerWidth < 768;
      setIsMobile(isMobileScreen);
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call on initial render

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle category toggle for filtering
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

  // Change calendar view (month, week, day, list)
  const changeView = (view: CalendarView) => {
    setCurrentView(view);
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.changeView(view);
    }
  };

  // Handle task click
  const handleTaskClick = (task: TaskModel) => {
    // Find the corresponding event
    const event = events.find(e => e.id === task._id);
    if (event) {
      setSelectedEvent(event);
      setIsDialogOpen(true);
    }
  };
  
  // Handle task completion
  const handleMarkComplete = async (taskId: string) => {
    try {
      setIsLoading(true);
      // Use the update method with completed: true
      await taskService.updateTask(taskId, { completed: true });
      
      // Update events list
      setEvents(prev => prev.map(event => {
        if (event.id === taskId) {
          return { ...event, extendedProps: { ...event.extendedProps, completed: true } };
        }
        return event;
      }));
      
      toast({
        title: "Success",
        description: "Task marked as complete",
      });
      
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error completing task:', error);
      toast({
        title: "Error",
        description: "Failed to update task status",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle task deletion
  const handleDeleteTask = async (taskId: string) => {
    try {
      setIsLoading(true);
      await taskService.deleteTask(taskId);
      
      // Remove from events list
      setEvents(prev => prev.filter(event => event.id !== taskId));
      
      toast({
        title: "Success",
        description: "Task deleted successfully",
      });
      
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error deleting task:', error);
      toast({
        title: "Error",
        description: "Failed to delete task",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle task edit
  const handleEditTask = (taskId: string) => {
    // Find the task
    const event = events.find(e => e.id === taskId);
    if (event) {
      // Prepare form data
      const dueDate = event.start ? new Date(event.start) : new Date();
      setFormData({
        id: taskId,
        title: event.title || '',
        description: event.extendedProps?.description || '',
        dueDate,
        dueTime: format(dueDate, 'HH:mm'),
        priority: (event.extendedProps?.priority || 'medium').toLowerCase() as 'low' | 'medium' | 'high' | 'critical',
        // Handle categories safely
        categories: Array.isArray(event.extendedProps?.categories) 
          ? event.extendedProps.categories.map((cat: any) => cat.id || cat._id || cat) 
          : [],
        // Handle reminders safely
        reminders: Array.isArray(event.extendedProps?.reminders) 
          ? event.extendedProps.reminders 
          : [],
        useAI: true,
      });
      
      setIsDialogOpen(false);
      setIsNewTaskDialogOpen(true);
    }
  };

  // Handle event click in the calendar
  const handleEventClick = (info: any) => {
    const eventId = info.event.id;
    const event = events.find(e => e.id === eventId);
    if (event) {
      setSelectedEvent(event);
      setIsDialogOpen(true);
    }
  };

  // Handle date click in the calendar
  const handleDateClick = (info: any) => {
    const clickedDate = new Date(info.date);
    
    // Initialize form data for a new task
    setFormData({
      title: '',
      description: '',
      dueDate: clickedDate,
      dueTime: format(new Date(), 'HH:mm'),
      priority: 'medium' as const,
      categories: [],
      reminders: [],
      useAI: true
    });
    
    setSelectedEvent(null);
    setIsNewTaskDialogOpen(true);
  };

  // Handle date select in the calendar (for creating tasks with duration)
  const handleDateSelect = (info: any) => {
    const startDate = new Date(info.start);
    const endDate = new Date(info.end);
    
    // Initialize form data for a new task
    setFormData({
      title: '',
      description: '',
      dueDate: startDate,
      dueTime: format(startDate, 'HH:mm'),
      priority: 'medium' as const,
      categories: [],
      reminders: [],
      useAI: true
    });
    
    setSelectedEvent(null);
    setIsNewTaskDialogOpen(true);
  };
  
  // Handle event drop (drag and drop)
  const handleEventDrop = async (info: any) => {
    try {
      const { event, oldEvent } = info;
      const taskId = event.id;
      const newDate = new Date(event.start);
      
      // Show loading state
      setIsLoading(true);
      
      // Confirm with user
      const confirmed = window.confirm(
        `Are you sure you want to move "${event.title}" to ${format(newDate, 'PPP')}?`
      );
      
      if (confirmed) {
        // Update the task in the database
        await taskService.updateTask(taskId, {
          deadline: newDate.toISOString()
        });
        
        // Show success message
        toast({
          title: "Task Rescheduled",
          description: `Task moved to ${format(newDate, 'PPP')}`
        });
      } else {
        // Revert the drag if user cancels
        info.revert();
      }
    } catch (error) {
      console.error('Error updating task date:', error);
      toast({
        title: "Error",
        description: "Failed to reschedule task. Please try again.",
        variant: "destructive"
      });
      
      // Revert the drag on error
      info.revert();
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle event resize (for duration change)
  const handleEventResize = async (info: any) => {
    try {
      const { event, oldEvent } = info;
      const taskId = event.id;
      const newStartDate = new Date(event.start);
      const newEndDate = new Date(event.end);
      
      // Show loading state
      setIsLoading(true);
      
      // Confirm with user
      const confirmed = window.confirm(
        `Are you sure you want to change the duration of "${event.title}"?`
      );
      
      if (confirmed) {
        // Update the task in the database
        await taskService.updateTask(taskId, {
          deadline: newEndDate.toISOString()
        });
        
        // Show success message
        toast({
          title: "Task Updated",
          description: `Task duration changed`
        });
      } else {
        // Revert the resize if user cancels
        info.revert();
      }
    } catch (error) {
      console.error('Error updating task duration:', error);
      toast({
        title: "Error",
        description: "Failed to update task duration. Please try again.",
        variant: "destructive"
      });
      
      // Revert the resize on error
      info.revert();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle saving a task
  const handleSaveTask = async (taskData: ExtendedTaskFormValues) => {
    try {
      setIsLoading(true);
      
      // Combine date and time for deadline
      const dueDate = new Date(taskData.dueDate || new Date());
      const [hours, minutes] = (taskData.dueTime || '00:00').split(':').map(Number);
      dueDate.setHours(hours, minutes);
      
      // Create task DTO
      const taskDTO = {
        title: taskData.title,
        content: taskData.description || '',
        deadline: dueDate.toISOString(),
        priority: taskData.priority.toUpperCase() as TaskPriority,
        categories: taskData.categories,
        reminders: taskData.reminders,
        useAI: taskData.useAI
      };
      
      // Save task to API
      const response = await taskService.createTask(taskDTO);
      
      // Add new task to events
      const newEvent = mapTaskToCalendarEvent(response);
      setEvents(prev => [...prev, newEvent]);
      
      // Show success message
      toast({
        title: 'Success',
        description: 'Task created successfully.',
      });
      
      // Reset form data
      setFormData({
        title: '',
        description: '',
        dueDate: new Date(),
        dueTime: format(new Date(), 'HH:mm'),
        priority: 'medium',
        categories: [],
        reminders: [],
        useAI: true,
      });
      
      return response;
    } catch (error) {
      console.error('Error saving task:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save task. Please try again.';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && events.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Mobile header with menu button */}
      <div className="md:hidden flex items-center justify-between p-2 border-b">
        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <PanelLeft className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-[280px] sm:w-[300px]">
            <SheetHeader className="p-4 border-b">
              <SheetTitle>Calendar Menu</SheetTitle>
            </SheetHeader>
            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Calendar View</h3>
                <div className="flex space-x-2">
                  {Object.entries(VIEW_BUTTONS).map(([view, { icon: Icon, label }]) => (
                    <Button
                      key={view}
                      variant={currentView === view ? 'default' : 'outline'}
                      size="sm"
                      className="h-9 px-2"
                      onClick={() => setCurrentView(view as CalendarView)}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="sr-only">{label}</span>
                    </Button>
                  ))}
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Categories</h3>
                <div className="space-y-1">
                  {Array.isArray(categories) && categories.length > 0 ? (
                    categories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`mobile-category-${category.id}`}
                          checked={selectedCategories.includes(category.id)}
                          onChange={() => handleCategoryToggle(category.id)}
                          className="h-4 w-4"
                        />
                        <label
                          htmlFor={`mobile-category-${category.id}`}
                          className="text-sm"
                        >
                          {category.name}
                        </label>
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-gray-500">No categories available</div>
                  )}
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>
        <h1 className="text-xl font-semibold">
          {format(selectedDate, 'MMMM yyyy')}
        </h1>
        <div className="w-10"></div> {/* Spacer for flex layout */}
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <div className="flex space-x-2">
              <Button
                variant={currentView === 'dayGridMonth' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('dayGridMonth')}
              >
                <Grid className="h-4 w-4 mr-2" />
                Month
              </Button>
              <Button
                variant={currentView === 'timeGridWeek' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('timeGridWeek')}
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Week
              </Button>
              <Button
                variant={currentView === 'timeGridDay' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('timeGridDay')}
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Day
              </Button>
              <Button
                variant={currentView === 'listWeek' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('listWeek')}
              >
                <List className="h-4 w-4 mr-2" />
                List
              </Button>
            </div>
            <Button
              onClick={() => {
                const now = new Date();
                
                // Initialize form data for a new task
                setFormData({
                  title: '',
                  description: '',
                  dueDate: now,
                  dueTime: format(now, 'HH:mm'),
                  priority: 'medium' as const,
                  categories: [],
                  reminders: [],
                  useAI: true
                });
                
                setSelectedEvent(null);
                setIsNewTaskDialogOpen(true);
                
                // Update calendar view
                const calendarApi = calendarRef.current?.getApi();
                if (calendarApi) {
                  calendarApi.changeView('dayGridMonth');
                  calendarApi.gotoDate(now);
                }
              }}
              variant="default"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </div>

          <Dialog open={isNewTaskDialogOpen} onOpenChange={setIsNewTaskDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Task</DialogTitle>
                <DialogDescription>
                  Add a new task to your calendar
                </DialogDescription>
              </DialogHeader>
              {/* Close the dialog when task is added and refetch tasks */}
              <NewTaskDialog
                onTaskAdded={() => {
                  setIsNewTaskDialogOpen(false);
                  fetchTasks();
                }}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <div className="flex items-center justify-between">
                  <DialogTitle className="text-xl">
                    {selectedEvent?.title}
                    {selectedEvent?.extendedProps?.completed && (
                      <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                        <Check className="h-3 w-3 mr-1" />
                        Completed
                      </span>
                    )}
                  </DialogTitle>
                  {/* Show calendar sync status if available */}
                  <div className="tooltip" data-tip={selectedEvent?.extendedProps?.syncStatus === 'synced' 
                    ? `Synced with ${selectedEvent?.extendedProps?.syncProvider || 'external'} calendar` 
                    : selectedEvent?.extendedProps?.syncStatus === 'pending' 
                      ? 'Sync in progress' 
                      : selectedEvent?.extendedProps?.syncStatus === 'failed' 
                        ? 'Sync failed' 
                        : 'Not synced with any calendar'}>
                    {selectedEvent?.extendedProps?.syncStatus === 'synced' && (
                      <CalendarDays className="h-4 w-4 text-blue-500" />
                    )}
                    {selectedEvent?.extendedProps?.syncStatus === 'pending' && (
                      <Loader2 className="h-4 w-4 text-yellow-500 animate-spin" />
                    )}
                    {selectedEvent?.extendedProps?.syncStatus === 'failed' && (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    {(!selectedEvent?.extendedProps?.syncStatus || selectedEvent?.extendedProps?.syncStatus === 'not_synced') && (
                      <CalendarOff className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>
                <DialogDescription className="mt-2 text-sm">
                  {selectedEvent?.extendedProps?.description || 'No description provided.'}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <div className="col-span-3">
                    {selectedEvent?.start ? format(new Date(selectedEvent.start), 'PPP p') : 'No date set'}
                  </div>
                </div>
                
                {selectedEvent?.extendedProps?.priority && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <AlertTriangle className="h-4 w-4 text-gray-500" />
                    <div className="col-span-3">
                      <span className={cn(
                        'inline-block px-2 py-1 text-xs rounded-full',
                        PRIORITY_COLORS[selectedEvent.extendedProps.priority.charAt(0).toUpperCase() + selectedEvent.extendedProps.priority.slice(1).toLowerCase() as keyof typeof PRIORITY_COLORS]
                      )}>
                        {selectedEvent.extendedProps.priority}
                      </span>
                    </div>
                  </div>
                )}
                
                {/* Display categories if available */}
                {selectedEvent?.extendedProps && 
                 Array.isArray(selectedEvent.extendedProps.categories) && 
                 selectedEvent.extendedProps.categories.length > 0 && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <div className="h-4 w-4 flex items-center justify-center text-gray-500">
                      <span className="block h-3 w-3 rounded-full bg-blue-500"></span>
                    </div>
                    <div className="col-span-3 flex flex-wrap gap-1">
                      {selectedEvent.extendedProps.categories.map((category: any, idx: number) => (
                        <span key={category.id || category._id || idx} 
                          className="inline-block px-2 py-1 text-xs rounded-full" 
                          style={{
                            backgroundColor: category.color ? `${category.color}20` : '#f3f4f6',
                            color: category.color || '#374151'
                          }}>
                          {category.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedEvent?.extendedProps?.location && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <div className="h-4 w-4 flex items-center justify-center text-gray-500">
                      <ExternalLink className="h-4 w-4" />
                    </div>
                    <div className="col-span-3">
                      {typeof selectedEvent.extendedProps.location === 'string' 
                        ? selectedEvent.extendedProps.location 
                        : selectedEvent.extendedProps.location.name}
                    </div>
                  </div>
                )}
                
                {/* Display reminders if available */}
                {selectedEvent?.extendedProps && 
                 Array.isArray(selectedEvent.extendedProps.reminders) && 
                 selectedEvent.extendedProps.reminders.length > 0 && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <div className="h-4 w-4 flex items-center justify-center text-gray-500">
                      <Clock className="h-4 w-4" />
                    </div>
                    <div className="col-span-3 flex flex-col gap-1">
                      {selectedEvent.extendedProps.reminders.map((reminder: any, index: number) => (
                        <span key={index} className="text-xs text-gray-600">
                          Reminder: {reminder.type === 'before' ? 'Before' : 'At'} {reminder.offsetValue} {reminder.offsetUnit}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex flex-wrap justify-between gap-2 pt-2 border-t">
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleDeleteTask(selectedEvent?.id || '')}
                    variant="destructive"
                    size="sm"
                  >
                    <Trash className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                  
                  <Button
                    onClick={() => {
                      // Open date picker for rescheduling
                      // This would be expanded in a future implementation
                      toast({
                        title: "Coming Soon",
                        description: "Task rescheduling will be available soon"
                      });
                    }}
                    variant="outline"
                    size="sm"
                  >
                    <MoveRight className="h-4 w-4 mr-1" />
                    Move
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleEditTask(selectedEvent?.id || '')}
                    variant="outline"
                    size="sm"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  
                  {!selectedEvent?.extendedProps?.completed ? (
                    <Button
                      onClick={() => handleMarkComplete(selectedEvent?.id || '')}
                      variant="default"
                      size="sm"
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Complete
                    </Button>
                  ) : (
                    <Button
                      onClick={() => {
                        // Handle unmark as complete
                        // This would be implemented in a future update
                        toast({
                          title: "Coming Soon",
                          description: "Unmarking tasks will be available soon"
                        });
                      }}
                      variant="secondary"
                      size="sm"
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Completed
                    </Button>
                  )}
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <div className="flex-1 overflow-auto">
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
              initialView={currentView}
              headerToolbar={false} // We're using our own header
              events={events}
              eventClick={handleEventClick}
              dateClick={handleDateClick}
              select={handleDateSelect}
              selectable={true}
              selectMirror={true}
              editable={true} // Enable drag and drop
              eventDrop={handleEventDrop} // Handle drag and drop
              eventResize={handleEventResize} // Handle resize
              dayMaxEvents={3} // Show max 3 events per day
              moreLinkClick="day" // Click on +more to go to day view
              weekends={true}
              height="100%"
              eventTimeFormat={{
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              }}
              dayCellDidMount={(info) => {
                // Add task count indicator to each day cell
                const date = info.date;
                const dayEvents = events.filter(event => {
                  const eventDate = new Date(event.start || '');
                  return eventDate.getDate() === date.getDate() && 
                         eventDate.getMonth() === date.getMonth() && 
                         eventDate.getFullYear() === date.getFullYear();
                });
                
                if (dayEvents.length > 0) {
                  const badge = document.createElement('div');
                  badge.className = 'task-count-badge';
                  badge.innerHTML = `${dayEvents.length}`;
                  badge.style.position = 'absolute';
                  badge.style.top = '2px';
                  badge.style.right = '2px';
                  badge.style.backgroundColor = '#e2e8f0';
                  badge.style.color = '#475569';
                  badge.style.borderRadius = '9999px';
                  badge.style.fontSize = '0.7rem';
                  badge.style.fontWeight = '600';
                  badge.style.padding = '1px 6px';
                  badge.style.lineHeight = '1';
                  info.el.appendChild(badge);
                  
                  // Add color indicator based on priority
                  const hasCritical = dayEvents.some(e => e.extendedProps?.priority === 'Critical');
                  const hasHigh = dayEvents.some(e => e.extendedProps?.priority === 'High');
                  
                  if (hasCritical) {
                    info.el.style.borderLeft = '3px solid #ef4444';
                  } else if (hasHigh) {
                    info.el.style.borderLeft = '3px solid #f59e0b';
                  }
                }
              }}
              eventContent={(eventInfo) => {
                const priority = eventInfo.event.extendedProps?.priority || 'Medium';
                const completed = eventInfo.event.extendedProps?.completed || false;
                
                // Create custom event rendering
                const wrapper = document.createElement('div');
                wrapper.className = `event-wrapper ${completed ? 'completed-event' : ''}`;
                wrapper.style.display = 'flex';
                wrapper.style.alignItems = 'center';
                wrapper.style.gap = '4px';
                wrapper.style.width = '100%';
                wrapper.style.overflow = 'hidden';
                
                // Priority indicator
                const indicator = document.createElement('div');
                indicator.style.width = '8px';
                indicator.style.height = '8px';
                indicator.style.borderRadius = '50%';
                indicator.style.flexShrink = '0';
                
                // Set color based on priority
                switch (priority) {
                  case 'Critical':
                    indicator.style.backgroundColor = '#ef4444';
                    break;
                  case 'High':
                    indicator.style.backgroundColor = '#f59e0b';
                    break;
                  case 'Medium':
                    indicator.style.backgroundColor = '#10b981';
                    break;
                  case 'Low':
                    indicator.style.backgroundColor = '#3b82f6';
                    break;
                  default:
                    indicator.style.backgroundColor = '#6b7280';
                }
                
                // Title with strikethrough if completed
                const title = document.createElement('div');
                title.innerText = eventInfo.event.title;
                title.style.overflow = 'hidden';
                title.style.textOverflow = 'ellipsis';
                title.style.whiteSpace = 'nowrap';
                
                if (completed) {
                  title.style.textDecoration = 'line-through';
                  title.style.color = '#9ca3af';
                }
                
                wrapper.appendChild(indicator);
                wrapper.appendChild(title);
                
                return { domNodes: [wrapper] };
              }}
              views={{
                dayGridMonth: {
                  dayMaxEventRows: 3,
                  moreLinkContent: (args: { num: number }) => (
                    <div className="text-xs font-medium text-blue-600 bg-blue-100 px-1 rounded-sm">
                      +{args.num} more
                    </div>
                  )
                },
                timeGridWeek: {
                  dayMaxEventRows: 4,
                },
                timeGridDay: {
                  dayMaxEventRows: 6,
                },
                listWeek: {
                  type: 'listWeek',
                  duration: { weeks: 1 },
                },
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
