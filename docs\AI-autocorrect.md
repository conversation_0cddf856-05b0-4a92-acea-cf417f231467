We now will be Adding autocorrection with a review mechanism is a smart way to enhance robustness while maintaining user control.

Here's how we can implement this:

**Phase 1: AI Prompt and Backend Logic for Autocorrection**

**1. Update AI Prompt (`aiPrompts.yaml`) for `processQuickAdd`**

We'll instruct the AI to perform corrections and report them.

```yaml
// backend/src/prompts/aiPrompts.yaml
# AI Prompts for Task OrganAIzer

# ... (existing system_message and other prompts)

prompts:
  processQuickAdd:
    description: "Processes natural language input for quick task or grocery creation. Includes spelling/typo correction with reporting. Focuses on intuitive naming, description, priority, and capturing user intent accurately."
    system_message: |
      You are an expert task organizing AI. Your goal is to meticulously parse user input into structured task objects or grocery items.
      IMPORTANT INSTRUCTIONS:

      1.  SPELLING & TYPO CORRECTION:
          Before parsing, review the `original_utterance_segment` for obvious spelling mistakes, typos, or common voice recognition errors (e.g., "too" vs "to", "their" vs "there", "remind me too call" -> "remind me to call").
          If you make a correction, you MUST:
          - Set `was_auto_corrected` to `true`.
          - Provide the `original_text_before_correction` (the segment with the error).
          - Provide the `corrected_text_segment` (the segment after your correction).
          Use the `corrected_text_segment` for subsequent parsing steps (title, content generation, etc.).
          If no correction was made for a segment, set `was_auto_corrected` to `false` and the text fields to `null`.
          Focus on clear, unambiguous errors. Do NOT change phrasing or meaning, only correct obvious mistakes.

      2.  MULTI-TASK SEGMENTATION:
          Carefully check for multiple distinct tasks in a single input. Split input into separate tasks based on sequential indicators ("then", "after"), conjunctions ("and", "also"), lists, or clear contextual shifts.
          For EACH task identified, provide its `original_utterance_segment`.

      3.  PER-TASK ATTRIBUTE EXTRACTION & INTUITIVE NAMING (using corrected text if applicable):
          For EACH task, extract/generate:
          - `intent` (string): "task" or "grocery".
          - `confidence_level` (string: "High" | "Medium" | "Low"): Your confidence in parsing this specific task accurately.
          - `original_utterance_segment` (string): The part of the user's input corresponding to this task.
          - `was_auto_corrected` (boolean): True if you corrected spelling/typos in this segment.
          - `original_text_before_correction` (string or null): The text segment *before* your correction.
          - `corrected_text_segment` (string or null): The text segment *after* your correction.
          - `title` (string): A CONCISE, ACTIONABLE title (from corrected text). Capitalize only the first word unless a proper noun.
          - `summary_title` (string or null): A slightly more descriptive title (from corrected text, max 70 chars) if the main `title` is very short.
          - `content` (string or null): EITHER the `corrected_text_segment` (if different from title) OR the `original_utterance_segment` (if no correction or for context).
          - `parsed_description` (string or null): If you generated a specific description by cleaning up or structuring details (from corrected text).
          - `categoryNames` (array of strings or empty array): Categories from `User's existing categories`.
          - `locationName` (string or null): Location from `User's existing locations`.
          - `deadline` (string: "YYYY-MM-DD" or null): Use `{{currentDateTime}}` as reference.
          - `time` (string: "HH:MM" 24-hour format or null).
          - `priority` (string: "Low" | "Medium" | "High" | "Critical" | null): Infer priority (details in previous prompt).
          - `reminders` (array of objects or empty array): Each object: `{"offsetValue": number, "offsetUnit": "minutes"|"hours"|"days"|"weeks"}`.
          - `clarification_needed` (string or null): If a key detail is missing or ambiguous.

      4.  IMPLICIT PROJECT SUGGESTION: (Same as before)
      5.  OUTPUT JSON STRUCTURE: (Same as before, but ensure new fields are in examples)
      6.  GROCERY HANDLING: (Same as before, but add correction fields to grocery object too if applicable, though less common for grocery items)
          `{ "intent": "grocery", "confidence_level": "High", "original_utterance_segment": "...", "was_auto_corrected": false, "original_text_before_correction": null, "corrected_text_segment": null, "items": [...] }`
      7.  TIME & DATE INTERPRETATION: (Same as before)
      8.  AMBIGUITY & SHORT INPUTS: (Same as before)

    template: |
      User Input: "{{quickInputText}}"

      Current date and time: {{currentDateTime}}
      User's existing categories: [{{#if categoryNamesList}}{{categoryNamesList}}{{/if}}]
      User's existing locations: [{{#if locationNamesList}}{{locationNamesList}}{{/if}}]

      Analyze the input and respond ONLY with a single valid JSON object.

      Desired JSON Output Structure Example (for tasks with correction):
      {
        "suggestedProjectTitle": null,
        "tasks": [
          {
            "intent": "task",
            "confidence_level": "High",
            "original_utterance_segment": "Call Jhon about Project X tomorow by 5pm, remind me 2 houres before",
            "was_auto_corrected": true,
            "original_text_before_correction": "Call Jhon about Project X tomorow by 5pm, remind me 2 houres before",
            "corrected_text_segment": "Call John about Project X tomorrow by 5pm, remind me 2 hours before",
            "title": "Call John (Project X)",
            "summary_title": "Call John about Project X by tomorrow 5pm",
            "content": "Call John about Project X tomorrow by 5pm, remind me 2 hours before", // This would be the corrected_text_segment
            "parsed_description": "Discuss Project X with John. Get feedback before EOD.",
            "categoryNames": ["Work", "Project X"],
            "locationName": null,
            "deadline": "{{resolved_tomorrow_date_based_on_currentDateTime}}",
            "time": "17:00",
            "priority": "High",
            "reminders": [{"offsetValue": 2, "offsetUnit": "hours"}],
            "clarification_needed": null
          }
        ]
      }
      // ... (keep other examples)
  // ... (other prompts)
```

**2. Update Task Model (`Task.ts`)**

Add fields to `metadata` to store the original and corrected text if a correction occurred.

```typescript
// backend/src/models/Task.ts
// ...
export interface ITask extends Document {
  // ... (existing fields)
  metadata?: {
    // ... (existing metadata fields)
    aiWasAutoCorrected?: boolean; // NEW: Flag indicating AI made a correction
    aiOriginalTextSegment?: string; // NEW: The text segment before AI correction
    aiCorrectedTextSegment?: string; // NEW: The text segment after AI correction
    [key: string]: any;
  };
  // ...
}

const taskSchema = new Schema<ITask>(
  {
    // ... (existing schema definition)
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
    // ...
  },
  {
    timestamps: true,
  }
);
// ... (rest of the file)
export const Task = mongoose.model<ITask>('Task', taskSchema);
```

**3. Update Insight Model (`Insight.ts`)**

Add a new insight type `AI_AUTOCORRECTION_REVIEW`.

```typescript
// backend/src/models/Insight.ts
// ...
export interface IInsight extends Document {
  // ...
  type:
    | 'NEW_CATEGORY_SUGGESTION'
    // ... (other types)
    | 'CONFIRM_AI_INTERPRETATION'
    | 'AI_AUTOCORRECTION_REVIEW'; // NEW
  // ...
  relatedData: {
    // ... (other relatedData fields)
    originalText?: string; // For AI_AUTOCORRECTION_REVIEW
    correctedText?: string; // For AI_AUTOCORRECTION_REVIEW
    // targetTaskId will be used for AI_AUTOCORRECTION_REVIEW as well
    [key: string]: any;
  };
  // ...
}

const InsightSchema: Schema = new Schema(
  {
    // ...
    type: {
      type: String,
      enum: [
        // ... existing types
        'CONFIRM_AI_INTERPRETATION',
        'AI_AUTOCORRECTION_REVIEW', // NEW
      ],
      // ...
    },
    // ...
  },
  // ...
);
// ...
const Insight = mongoose.model<IInsight>('Insight', InsightSchema);
export default Insight;
```

**4. Update AI Controller (`aiController.ts`)**

In `processQuickAdd`, when creating a task, store correction details and create an insight if a correction was made.

```typescript
// backend/src/controllers/aiController.ts
// ... (other imports)
// import Insight from '../models/Insight'; // Already imported
// import { handleNewInsightCreated } from '../services/insightNotificationService'; // Already imported

export const processQuickAdd = async (req: Request, res: Response, next: NextFunction) => {
  // ... (existing setup and AI call)
  // Inside the loop for processing itemsArrayFromAI (intent === 'task')
      // ...
      for (const taskData of itemsArrayFromAI) {
        // ... (check intent === 'task')

        const {
          // ... (existing destructuring)
          was_auto_corrected: aiWasAutoCorrected, // NEW
          original_text_before_correction: aiOriginalTextBeforeCorrection, // NEW
          corrected_text_segment: aiCorrectedTextSegment, // NEW
          // ... (other fields)
        } = taskData;

        // ... (existing logic for title, content, deadline, priority, etc.)
        // IMPORTANT: Ensure that finalTitle, finalContent, etc., are derived from aiCorrectedTextSegment if aiWasAutoCorrected is true.
        // The prompt already instructs the AI to use the corrected text for subsequent parsing, so aiTitle and aiContent should reflect corrections.

        let finalTitle = selectBestTitle(aiTitle, aiSummaryTitle);
        // If AI corrected the segment used for the title, finalTitle already reflects it.
        // If AI corrected the segment used for content, finalContent should reflect it.
        let finalContent = selectBestDescription(aiContent, aiParsedDescription, aiCorrectedTextSegment || aiOriginalUtterance);


        const taskMetadata: ITask['metadata'] = {
           // ... (existing metadata fields)
           aiWasAutoCorrected: aiWasAutoCorrected, // Store correction flag
           aiOriginalTextSegment: aiWasAutoCorrected ? aiOriginalTextBeforeCorrection : undefined,
           aiCorrectedTextSegment: aiWasAutoCorrected ? aiCorrectedTextSegment : undefined,
           // ... (other AI metadata fields like confidence, clarification)
        };
        
        // If AI made a clarification request, append it to the content
        if (taskMetadata.aiClarificationNeeded) {
            const clarificationText = `\n\n[AI Needs Clarification: ${taskMetadata.aiClarificationNeeded}]`;
            finalContent = finalContent ? `${finalContent}${clarificationText}` : clarificationText.trim();
        }


        const taskToSave = new Task({
          userId: new Types.ObjectId(userId),
          title: finalTitle, // Should be based on corrected text if any
          content: finalContent, // Should be based on corrected text if any
          // ... (rest of the task fields)
          metadata: taskMetadata,
        });

        const savedTask = await taskToSave.save();
        // ... (keytag mapping logic)
        createdTasks.push(savedTask);

        // ... (existing insight creation for CONFIRM_AI_INTERPRETATION)

        // NEW: Create insight for auto-correction review
        if (aiWasAutoCorrected && aiOriginalTextBeforeCorrection && aiCorrectedTextSegment) {
          const correctionInsightDesc = `AI corrected text for task "${finalTitle}". Original: "${aiOriginalTextBeforeCorrection.substring(0, 50)}..." Corrected to: "${aiCorrectedTextSegment.substring(0,50)}...". Review?`;
          const newCorrectionInsight = await Insight.create({
            userId: new Types.ObjectId(userId),
            type: 'AI_AUTOCORRECTION_REVIEW',
            description: correctionInsightDesc,
            relatedData: {
              targetTaskId: savedTask._id,
              taskTitle: finalTitle, // Store current title for context
              originalText: aiOriginalTextBeforeCorrection,
              correctedText: aiCorrectedTextSegment, // This is what's currently in the task
            },
            status: 'pending',
          });
          if (newCorrectionInsight) {
             await handleNewInsightCreated(new Types.ObjectId(userId!), {}, newCorrectionInsight);
          }
        }
      } // End of for...of loop
  // ... (rest of the function)
};

// ... (other controller functions)
```

**Phase 2: Frontend UI for Reviewing Autocorrections**

**1. Update Insight Card (`frontend/components/insight-card.tsx`)**

Add a new case to render the `AI_AUTOCORRECTION_REVIEW` insight.

```typescript
// frontend/components/insight-card.tsx
// ... (imports, existing functions like getInsightIcon)
import { taskService, TaskDTO } from '@/lib/task-service'; // Make sure TaskDTO is imported

// ...

// Add a case to getTitle
const getTitle = (type: Insight['type']): string => {
  switch (type) {
    // ... (existing cases)
    case 'AI_AUTOCORRECTION_REVIEW': // NEW
      return 'AI Text Correction';
    default:
      // ...
  }
};

// Add a case to getBackgroundColor
const getBackgroundColor = (type: Insight['type']): string => {
  switch (type) {
    // ... (existing cases)
    case 'AI_AUTOCORRECTION_REVIEW': // NEW
      return 'bg-sky-50 dark:bg-sky-950/30'; // Example color
    default:
      // ...
  }
};

// Add a case to getBorderColor
const getBorderColor = (type: Insight['type']): string => {
  switch (type) {
    // ... (existing cases)
    case 'AI_AUTOCORRECTION_REVIEW': // NEW
      return 'border-sky-200 dark:border-sky-800'; // Example color
    default:
      // ...
  }
};


export const InsightCard: React.FC<InsightCardProps> = ({
  insight,
  onAccept, // This will now mean "Keep Correction" for this insight type
  onDismiss,
  // ... (other props like onEditTask which will be called by "Edit Manually")
  onMarkComplete,
  onChangeDeadline,
  onDeleteTask,
  onReviewDuplicates,
  onMergeTasks,
  isLoading
}) => {
  // ... (existing handleAccept, handleDismiss)

  const handleRevertCorrection = async () => {
    if (isLoading || !insight.relatedData.targetTaskId || !insight.relatedData.originalText) return;
    // Call onAccept which is now wired to a generic "accept/keep" function
    // The actual task update logic will be here.
    try {
      // Update the task with the original text
      // Assuming the AI's corrected text affected the task's title and/or content.
      // We need to decide which field(s) to revert. For simplicity, let's assume it primarily affects 'title' and 'content'.
      // A more robust solution might store which field was corrected in `relatedData`.
      const updates: Partial<TaskDTO> = {};
      const taskCurrentTitle = insight.relatedData.taskTitle; // The title after AI correction
      const taskCurrentContent = (await taskService.getTaskById(insight.relatedData.targetTaskId)).content; // Fetch current content for comparison

      // Heuristic: if original text is shorter and looks like a title, update title.
      // If it's longer, update content. Or update both if they seem to match the corrected segment.
      // This is a simplified approach.
      if (insight.relatedData.correctedText === taskCurrentTitle) {
        updates.title = insight.relatedData.originalText;
      } else if (insight.relatedData.correctedText === taskCurrentContent) {
        updates.content = insight.relatedData.originalText;
      } else {
        // Fallback: update title if it was likely the corrected part
        updates.title = insight.relatedData.originalText;
        // Optionally, if originalText is long, also put it in content:
        // if (insight.relatedData.originalText.length > 50) {
        //   updates.content = insight.relatedData.originalText;
        // }
      }

      await taskService.updateTask(insight.relatedData.targetTaskId, updates);
      toast({
        title: "Correction Reverted",
        description: `Task "${taskCurrentTitle}" restored to original text.`,
      });
      onDismiss(insight._id); // Dismiss insight after reverting
      // onActionComplete?.(); // This should be called by onDismiss if that's the pattern
    } catch (error: any) {
      toast({
        title: "Error Reverting",
        description: error.message || "Could not revert AI correction.",
        variant: "destructive",
      });
    }
  };

  const handleEditCorrectedTask = () => {
    if (insight.relatedData.targetTaskId && onEditTask) { // onEditTask should be passed down
      onEditTask(insight.relatedData.targetTaskId); // Call the prop to open edit dialog
      onDismiss(insight._id); // Dismiss the insight after opening edit
    }
  };


  // ... (existing getInsightIcon, getTitle, etc.)

  // Update getRelatedContext
  const getRelatedContext = () => {
    if (!insight.relatedData) return null;

    switch (insight.type) {
      // ... (existing cases)
      case 'AI_AUTOCORRECTION_REVIEW': // NEW
        return (
          <div className="mt-2 text-sm space-y-1">
            <p><span className="font-semibold text-muted-foreground">Original:</span> "{insight.relatedData.originalText}"</p>
            <p><span className="font-semibold text-muted-foreground">Corrected to:</span> "{insight.relatedData.correctedText}"</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card /* ... existing card props ... */ >
      {/* ... existing CardHeader and CardContent ... */}
      <CardFooter className="flex justify-end space-x-2 pb-3">
        {/* ... existing common dismiss button ... */}

        {insight.type === 'AI_AUTOCORRECTION_REVIEW' && insight.relatedData?.targetTaskId && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRevertCorrection}
              disabled={isLoading}
              aria-label="Revert to Original Text"
            >
              Revert
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleEditCorrectedTask} // Use the new handler
              disabled={isLoading}
              aria-label="Edit Task Manually"
            >
              Edit Task
            </Button>
            <Button
              variant="default" // "Keep Correction" becomes the primary "accept" action
              size="sm"
              onClick={handleAccept} // Default accept action
              disabled={isLoading}
              aria-label="Keep AI Correction"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Check className="mr-1 h-4 w-4" /> Keep
            </Button>
          </>
        )}

        {/* ... other existing specific action buttons ... */}
         {(insight.type === 'NEW_CATEGORY_SUGGESTION' || 
          insight.type === 'PRIORITY_CHECK' || 
          insight.type === 'RECURRING_TASK_SUGGESTION' || 
          insight.type === 'CATEGORY_CLEANUP') && (
          // Default Accept button for simple insights
          <Button
            variant="default"
            size="sm"
            onClick={handleAccept}
            disabled={isLoading}
            aria-label="Accept suggestion"
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Check className="mr-1 h-4 w-4" /> Accept
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

// Ensure onEditTask prop is added to AiInsightsList and passed down to InsightCard
// frontend/components/ai-insights-list.tsx
// ...
interface AiInsightsListProps {
  onActionComplete?: () => void;
  refreshKey: number;
  onEditTask?: (taskId: string) => void; // NEW PROP
}

export const AiInsightsList: React.FC<AiInsightsListProps> = ({ onActionComplete, refreshKey, onEditTask }) => {
  // ...
  // When rendering InsightCard:
  // <InsightCard
  //   ...
  //   onEditTask={onEditTask} // Pass it down
  // />
  // ...
}
```

*   **In `frontend/app/dashboard/page.tsx` (or wherever `AiInsightsList` is used):**
    You'll need to pass the `onEditTask` prop to `AiInsightsList`. This function would typically open your `EditTaskDialog`.

    ```typescript
    // frontend/app/dashboard/page.tsx
    // ...
    // const handleEditRequest = async (taskId: string) => { ... }; // This function already exists
    // ...
    return (
      // ...
      <AiInsightsList
        onActionComplete={handleTaskActionComplete}
        refreshKey={refreshKey} // or contextRefreshKey if using context for this
        onEditTask={handleEditRequest} // Pass the existing edit handler
      />
      // ...
    );
    ```

**Explanation of Changes:**

1.  **AI Prompt:**
    *   The AI is now explicitly asked to look for and correct obvious mistakes.
    *   It's instructed to report these corrections via `was_auto_corrected`, `original_text_before_correction`, and `corrected_text_segment`. This is crucial for transparency and allowing user overrides.
    *   The prompt emphasizes correcting *obvious errors* and not changing meaning, to prevent overzealous AI changes.

2.  **Backend:**
    *   Task metadata now stores the correction details.
    *   If `aiWasAutoCorrected` is true, an `AI_AUTOCORRECTION_REVIEW` insight is generated. This insight contains the original and corrected text, allowing the user to review the change.
    *   The task itself is created/updated using the *AI-corrected text*.

3.  **Frontend:**
    *   A new `InsightCard` variant for `AI_AUTOCORRECTION_REVIEW` is added.
    *   **"Keep Correction"**: This is the "Accept" action. It simply dismisses the insight, as the task already has the corrected text.
    *   **"Revert to Original"**: This action updates the task's title/content back to `originalText` from the insight's `relatedData` and then dismisses the insight. This requires a new handler in `InsightCard` that calls `taskService.updateTask`.
    *   **"Edit Task"**: This opens the standard task edit dialog, pre-filled with the (corrected) task data. The insight is dismissed.

This approach ensures that "flash capture" still feels quick because the AI makes the correction automatically. The user is then notified non-intrusively via an insight, giving them the power to review, revert, or further edit if the AI's correction wasn't perfect or desired. This adds a layer of intelligence and safety to the input processing.