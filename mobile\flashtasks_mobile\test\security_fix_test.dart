import 'package:flutter_test/flutter_test.dart';
import 'package:flashtasks_mobile/src/core/storage/secure_storage.dart';

void main() {
  group('Security Fix Tests', () {
    test('SecureStorageService can store and retrieve credentials securely', () async {
      // This test verifies that our security fix works
      final secureStorage = SecureStorageService();
      
      // Test storing credentials
      await secureStorage.write(key: 'saved_email', value: '<EMAIL>');
      await secureStorage.write(key: 'saved_password', value: 'testpassword123');
      await secureStorage.write(key: 'remember_me', value: 'true');
      
      // Test retrieving credentials
      final savedEmail = await secureStorage.read(key: 'saved_email');
      final savedPassword = await secureStorage.read(key: 'saved_password');
      final rememberMe = await secureStorage.read(key: 'remember_me');
      
      // Verify the values
      expect(savedEmail, '<EMAIL>');
      expect(savedPassword, 'testpassword123');
      expect(rememberMe, 'true');
      
      // Test clearing credentials
      await secureStorage.delete(key: 'saved_email');
      await secureStorage.delete(key: 'saved_password');
      await secureStorage.delete(key: 'remember_me');
      
      // Verify they're cleared
      final clearedEmail = await secureStorage.read(key: 'saved_email');
      final clearedPassword = await secureStorage.read(key: 'saved_password');
      final clearedRememberMe = await secureStorage.read(key: 'remember_me');
      
      expect(clearedEmail, isNull);
      expect(clearedPassword, isNull);
      expect(clearedRememberMe, isNull);
    });
    
    test('SecureStorageService handles null values correctly', () async {
      final secureStorage = SecureStorageService();
      
      // Test writing null value (should delete)
      await secureStorage.write(key: 'test_key', value: 'test_value');
      await secureStorage.write(key: 'test_key', value: null);
      
      final result = await secureStorage.read(key: 'test_key');
      expect(result, isNull);
    });
  });
}
