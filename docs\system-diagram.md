graph LR
    %% ----- STYLES -----
    classDef userInterface fill:#D1E8FF,stroke:#005EB8,stroke-width:2px,color:#000
    classDef frontendService fill:#E1F5FE,stroke:#0277BD,stroke-width:1px,color:#000
    classDef backendGateway fill:#FFF9C4,stroke:#FBC02D,stroke-width:2px,color:#000
    classDef backendController fill:#FFE0B2,stroke:#EF6C00,stroke-width:1px,color:#000
    classDef backendService fill:#FFCCBC,stroke:#D84315,stroke-width:1px,color:#000
    classDef backendModel fill:#F8BBD0,stroke:#C2185B,stroke-width:1px,color:#000
    classDef database fill:#C8E6C9,stroke:#2E7D32,stroke-width:2px,color:#000
    classDef externalService fill:#E0E0E0,stroke:#424242,stroke-width:2px,color:#000
    classDef aiProcessing fill:#D7CCC8,stroke:#4E342E,stroke-width:1.5px,color:#000,font-style:italic
    classDef dataObject fill:#E1F5FE,stroke:#007BFF,stroke-width:1px,rx:5,ry:5,color:#000
    classDef insightProcess fill:#E0F2F1,stroke:#00796B,stroke-width:1px,color:#000

    %% ----- USER & FRONTEND -----
    subgraph "User Interaction Zone"
        direction LR
        U[(👤 User)]
        subgraph "Frontend (Web/Mobile)"
            direction TB
            QuickInputUI["⌨️ Quick Input / 🎤 Voice Input <br/> (Web or Mobile UI)"]
            APIService_FE["🌐 Frontend API Service <br/> (e.g., aiService.processQuickAdd)"]
            QuickInputUI -->|1. User Enters Text/Voice| APIService_FE
        end
    end
    class U,QuickInputUI,APIService_FE userInterface;
    class APIService_FE frontendService;

    %% ----- BACKEND API GATEWAY -----
    APIService_FE -->|2. HTTP POST Request <br/> (to /api/ai/quick-add)| BackendGateway["🚪 Backend API Gateway <br/> (app.ts - Express) <br/> Auth, CSRF, Rate Limiter, Validation Middleware"];
    class BackendGateway backendGateway;

    %% ----- BACKEND AI CONTROLLER -----
    BackendGateway --> AIController["🧠 AI Controller <br/> (aiController.ts - processQuickAdd)"];
    class AIController backendController;

    %% ----- CONTEXT RETRIEVAL -----
    subgraph "Context Preparation"
        direction TB
        AIController --> FetchUserContext["📚 Fetch User Context <br/> (Categories, Locations, Settings)"]
        FetchUserContext --> CategoryModel_BE["🗂️ Category Model"];
        FetchUserContext --> LocationModel_BE["📍 Location Model"];
        FetchUserContext --> UserSettingsModel_BE["⚙️ UserSettings Model"];
        CategoryModel_BE --> DB1["🗄️ MongoDB"];
        LocationModel_BE --> DB1;
        UserSettingsModel_BE --> DB1;
    end
    class FetchUserContext backendService;
    class CategoryModel_BE,LocationModel_BE,UserSettingsModel_BE backendModel;
    class DB1 database;

    %% ----- AI PROCESSING PIPELINE -----
    subgraph "AI Processing & Task Creation Pipeline"
        direction TB
        FetchUserContext --> PromptService["📜 Prompt Service <br/> (Formats 'processQuickAdd' prompt <br/> from aiPrompts.yaml)"]
        PromptService --> AIProviderService["🤖 AI Provider Service <br/> (Abstracts AI model calls - Gemini/Claude)"]
        AIProviderService --> ExternalAI["☁️ External AI Service <br/> (e.g., Google Gemini API)"]
        ExternalAI -->|AI JSON Response| AIProviderService
        AIProviderService --> ParseAIResponse["🧩 Parse AI JSON Output <br/> (Structured task(s) or grocery data)"]
        
        ParseAIResponse --> LoopTasks{"♻️ For Each Parsed Item..."}

        subgraph "Single Task Processing"
            direction TB
            LoopTasks -- "If 'task' intent" --> Autocorrect["🔍 AI Text Autocorrection <br/> (AI corrects typos/spelling in utterance)"]
            Autocorrect --> AttributeExtraction["📊 AI Attribute Extraction <br/> (Title, Content, Deadline, Priority, etc. <br/> from *corrected* text)"]
            
            AttributeExtraction --> BackendRefinement["🛠️ Backend Refinement Logic"]
            subgraph "Backend Refinement Steps"
                direction TB
                BackendRefinement --> RefineTitle["📝 Select Best Title <br/> (Heuristics: concise vs. summary)"]
                RefineTitle --> RefineContent["📄 Select Best Content/Description <br/> (Heuristics: parsed vs. original vs. corrected)"]
                RefineContent --> RefinePriority["🚦 Refine Priority <br/> (Keywords, Deadline proximity, Time context)"]
                RefinePriority --> SuggestLocation["🗺️ Suggest Location <br/> (LocationSuggestionService)"]
                SuggestLocation --> DB_LocRead["🗄️ MongoDB <br/>(Read Locations for Suggestion)"]
                DB_LocRead --> SuggestLocation
            end
            
            SuggestLocation --> AssembleTaskData["🧱 Assemble Final Task Data <br/> (Incl. AI Metadata: confidence, correction details, clarification)"]
            AssembleTaskData --> TaskModel_Save["💾 Task Model (Save)"]
            TaskModel_Save --> DB_TaskWrite["🗄️ MongoDB <br/> (Write New Task)"]
            
            DB_TaskWrite --> ApplyKeytags["🏷️ Apply Keytag Mappings <br/> (If applicable, update categories)"]
            ApplyKeytags --> DB_TaskUpdate["(Optional) <br/>🗄️ MongoDB <br/> (Update Task Categories)"]
            DB_TaskUpdate --> GenerateInsights
            
            GenerateInsights["💡 Generate Review Insights"] -->|If AI Corrected| Insight_AutocorrectReview["📝 Create 'AI_AUTOCORRECTION_REVIEW' Insight <br/> (originalText, correctedText, taskTitle)"]
            GenerateInsights -->|If Low Confidence or Clarification Needed| Insight_ConfirmInterpretation["❓ Create 'CONFIRM_AI_INTERPRETATION' Insight <br/> (originalInput, confidence, clarification)"]
            
            Insight_AutocorrectReview --> InsightModel_Save1["💾 Insight Model (Save)"]
            Insight_ConfirmInterpretation --> InsightModel_Save2["💾 Insight Model (Save)"]
            InsightModel_Save1 --> DB_InsightWrite1["🗄️ MongoDB <br/> (Write Insight)"]
            InsightModel_Save2 --> DB_InsightWrite2["🗄️ MongoDB <br/> (Write Insight)"]
            
            DB_InsightWrite1 --> Notify1["(Async) 📲 Notify User <br/> (Push Notification via FCM)"]
            DB_InsightWrite2 --> Notify2["(Async) 📲 Notify User <br/> (Push Notification via FCM)"]
            
            Notify1 --> FirebaseFCM["☁️ Firebase Cloud Messaging"]
            Notify2 --> FirebaseFCM
        end
        
        LoopTasks -- "If 'grocery' intent" --> GroceryProcessing["🛒 Process Grocery Items <br/> (Categorize, Add to User Library)"]
        GroceryProcessing --> GroceryModel_Save["💾 GroceryItem Model (Save)"]
        GroceryModel_Save --> DB_GroceryWrite["🗄️ MongoDB <br/> (Write Groceries)"]
    end
    class PromptService,AIProviderService,ParseAIResponse,LoopTasks,Autocorrect,AttributeExtraction,BackendRefinement,RefineTitle,RefineContent,RefinePriority,SuggestLocation,AssembleTaskData,ApplyKeytags,GenerateInsights,GroceryProcessing backendService;
    class ExternalAI externalService;
    class TaskModel_Save,InsightModel_Save1,InsightModel_Save2,GroceryModel_Save backendModel;
    class DB_LocRead,DB_TaskWrite,DB_TaskUpdate,DB_InsightWrite1,DB_InsightWrite2,DB_GroceryWrite database;
    class Insight_AutocorrectReview,Insight_ConfirmInterpretation insightProcess;
    class Notify1,Notify2 backendService;
    class FirebaseFCM externalService;
    
    %% ----- RESPONSE TO FRONTEND -----
    DB_TaskWrite --> APIResponse["📦 API Response to Frontend <br/> (201 Created with Task Data / Grocery Data)"]
    DB_GroceryWrite --> APIResponse;
    class APIResponse backendController;
    
    APIResponse --> FE_UpdateUI["✨ Frontend UI Update <br/> (Display new task/groceries, show insights)"]
    FE_UpdateUI --> FinalUserView["😊 User sees new items & potential review insights"]
    class FE_UpdateUI,FinalUserView userInterface;