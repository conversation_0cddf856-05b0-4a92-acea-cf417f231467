import { io, Socket } from 'socket.io-client';

// Simple auth token getter
const getAuthToken = (): string | null => {
  try {
    return localStorage.getItem('accessToken');
  } catch (error) {
    console.error('Failed to get auth token:', error);
    return null;
  }
};

interface GroceryItemEvent {
  item: any;
  addedBy?: string;
  updatedBy?: string;
  deletedBy?: string;
  timestamp: Date;
}

interface SharedListEvent {
  list: any;
  updatedBy: string;
  timestamp: Date;
}

interface MemberEvent {
  member?: any;
  memberId?: string;
  addedBy?: string;
  removedBy?: string;
  timestamp: Date;
}

interface TypingEvent {
  userId: string;
  userName: string;
  isTyping: boolean;
}

class SocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private isConnecting = false;

  // Event listeners storage
  private eventListeners: Map<string, Set<Function>> = new Map();

  constructor() {
    this.initializeSocket();
  }

  private initializeSocket(): void {
    if (this.isConnecting || this.socket?.connected) {
      return;
    }

    this.isConnecting = true;
    const token = getAuthToken();

    if (!token) {
      console.warn('No auth token available for socket connection');
      this.isConnecting = false;
      return;
    }

    const serverUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

    this.socket = io(serverUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventHandlers();
    this.isConnecting = false;
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected successfully');
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      this.emit('connected', null);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.emit('disconnected', { reason });

      // Auto-reconnect for certain disconnect reasons
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }

      this.handleReconnection();
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.emit('connection_error', { error: error.message });
      this.handleReconnection();
    });

    // Grocery list events
    this.socket.on('grocery-item-added', (data: GroceryItemEvent) => {
      this.emit('grocery-item-added', data);
    });

    this.socket.on('grocery-item-updated', (data: GroceryItemEvent) => {
      this.emit('grocery-item-updated', data);
    });

    this.socket.on('grocery-item-deleted', (data: { itemId: string; deletedBy: string; timestamp: Date }) => {
      this.emit('grocery-item-deleted', data);
    });

    this.socket.on('shared-list-updated', (data: SharedListEvent) => {
      this.emit('shared-list-updated', data);
    });

    this.socket.on('member-added', (data: MemberEvent) => {
      this.emit('member-added', data);
    });

    this.socket.on('member-removed', (data: MemberEvent) => {
      this.emit('member-removed', data);
    });

    this.socket.on('user-typing-grocery-item', (data: TypingEvent) => {
      this.emit('user-typing-grocery-item', data);
    });

    // Personal notifications
    this.socket.on('added-to-shared-list', (data: { listId: string; addedBy: string; timestamp: Date }) => {
      this.emit('added-to-shared-list', data);
    });

    this.socket.on('removed-from-shared-list', (data: { listId: string; removedBy: string; timestamp: Date }) => {
      this.emit('removed-from-shared-list', data);
    });

    // Room management responses
    this.socket.on('joined-grocery-list', (data: { listId: string }) => {
      this.emit('joined-grocery-list', data);
    });

    this.socket.on('left-grocery-list', (data: { listId: string }) => {
      this.emit('left-grocery-list', data);
    });

    this.socket.on('error', (data: { message: string }) => {
      console.error('Socket error:', data.message);
      this.emit('error', data);
    });
  }

  private handleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts_reached', null);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      if (!this.socket?.connected) {
        this.disconnect();
        this.initializeSocket();
      }
    }, delay);
  }

  // Public methods

  connect(): void {
    if (!this.socket || !this.socket.connected) {
      this.initializeSocket();
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Room management
  joinGroceryList(listId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('join-grocery-list', listId);
    }
  }

  leaveGroceryList(listId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('leave-grocery-list', listId);
    }
  }

  // Typing indicators
  emitTypingGroceryItem(listId: string, isTyping: boolean): void {
    if (this.socket?.connected) {
      this.socket.emit('typing-grocery-item', { listId, isTyping });
    }
  }

  // Event listener management
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) return;

    if (callback) {
      this.eventListeners.get(event)!.delete(callback);
    } else {
      this.eventListeners.get(event)!.clear();
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in socket event listener for ${event}:`, error);
        }
      });
    }
  }

  // Cleanup
  removeAllListeners(): void {
    this.eventListeners.clear();
  }

  // Reconnect manually
  reconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.initializeSocket();
  }
}

// Export singleton instance
export const socketService = new SocketService();
export default socketService;
