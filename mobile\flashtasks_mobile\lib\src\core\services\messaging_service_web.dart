import 'package:flutter/foundation.dart';
import 'messaging_service_interface.dart';

/// Web placeholder implementation of MessagingServiceInterface
/// This is a placeholder that doesn't use firebase_messaging_web to avoid js conflicts
class MessagingService implements MessagingServiceInterface {
  static final MessagingService _instance = MessagingService._internal();
  
  factory MessagingService() => _instance;
  
  MessagingService._internal() {
    if (kDebugMode) {
      print('Creating web placeholder messaging service');
    }
  }
  
  @override
  Future<void> initialize() async {
    if (kDebugMode) {
      print('Messaging service initialized (web placeholder)');
    }
  }
  
  @override
  Future<String?> getToken() async {
    if (kDebugMode) {
      print('Getting token (web placeholder)');
    }
    return 'web-placeholder-token';
  }
  
  @override
  Future<void> subscribeToTopic(String topic) async {
    if (kDebugMode) {
      print('Subscribing to topic: $topic (web placeholder)');
    }
  }
  
  @override
  Future<void> unsubscribeFromTopic(String topic) async {
    if (kDebugMode) {
      print('Unsubscribing from topic: $topic (web placeholder)');
    }
  }
  
  @override
  void setupForegroundNotificationHandling() {
    if (kDebugMode) {
      print('Setting up foreground notification handling (web placeholder)');
    }
  }
  
  @override
  void setupBackgroundMessaging() {
    if (kDebugMode) {
      print('Setting up background messaging (web placeholder)');
    }
    // Not supported on web
  }
  
  @override
  void setupNotificationTapHandling() {
    if (kDebugMode) {
      print('Setting up notification tap handling (web placeholder)');
    }
  }
} 