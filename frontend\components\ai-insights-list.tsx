'use client'; // This component uses hooks and interacts with the browser

import React, { useState, useEffect, useCallback } from 'react';
import { Insight } from '@/types/insight';
import { aiService } from '@/lib/ai-service';
import { taskService, Task, TaskDTO } from '@/lib/task-service'; // Import TaskDTO
import { insightService } from '@/lib/insight-service';
import { authService } from '@/lib/auth-service'; // Import auth service for CSRF token refresh
import { useRouter } from 'next/navigation'; // Import useRouter
import { InsightCard } from './insight-card';
import { ConfirmDeleteTaskModal } from './confirm-delete-task-modal';
import { ChangeDeadlineModal } from './change-deadline-modal';
import { ReviewDuplicatesModal } from './review-duplicates-modal';
import { MergeTasksModal } from './merge-tasks-modal'; // Import the new modal
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, Loader2, RefreshCcw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { categoryService, Category } from '@/lib/category-service'; // Add category service import
import { Button } from '@/components/ui/button';
import axios from 'axios';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"; // Import pagination components

interface AiInsightsListProps {
  onActionComplete?: () => void;
  refreshKey: number; // Add refreshKey prop
}

export const AiInsightsList: React.FC<AiInsightsListProps> = ({ onActionComplete, refreshKey }) => { // Destructure props
  const [insights, setInsights] = useState<Insight[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [taskToDelete, setTaskToDelete] = useState<{ id: string; title?: string; insightId: string } | null>(null);
  const [isChangeDeadlineOpen, setIsChangeDeadlineOpen] = useState<boolean>(false);
  const [taskToChangeDeadline, setTaskToChangeDeadline] = useState<{ id: string; title?: string; currentDeadline?: Date; insightId: string } | null>(null);
  const [isReviewDuplicatesOpen, setIsReviewDuplicatesOpen] = useState<boolean>(false);
  const [tasksToReview, setTasksToReview] = useState<{ id: string; title: string; content?: string; createdAt: Date }[]>([]);
  const [currentReviewInsightId, setCurrentReviewInsightId] = useState<string | null>(null);
  const [isMergeTasksOpen, setIsMergeTasksOpen] = useState<boolean>(false); // State for merge modal
  const [tasksToMerge, setTasksToMerge] = useState<{ id: string; title: string; content?: string; createdAt: Date; priority?: string; categories?: any[]; deadline?: Date }[]>([]); // State for merge task details
  const [currentMergeInsightId, setCurrentMergeInsightId] = useState<string | null>(null); // Track merge insight
  const [availableCategories, setAvailableCategories] = useState<Category[]>([]); // Add state for categories
  const { toast } = useToast();
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5); // Items per page as requested
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  // Add this helper function to convert task deadlines
  const convertTaskDeadlines = (tasks: Task[]): Task[] => {
    return tasks.map(task => {
      const convertedTask: Task = {
        ...task,
        deadline: task.deadline ? new Date(task.deadline) : undefined
      };
      return convertedTask;
    });
  };

  // Modify the useEffect where tasks are fetched
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        // Fetch the first page of tasks (or adjust if needed, though this list isn't paginated)
        const response = await taskService.getTasks(undefined, undefined, 1, 999); // Fetch a large number, assuming this list isn't meant to be paginated here
        const convertedTasks = convertTaskDeadlines(response.data); // Use response.data
        setTasks(convertedTasks);
        // Note: setIsLoading(false) was originally here, but it's better handled
        // in the fetchInsights logic which is the primary data loading for this component.
        // We'll leave it out here to avoid potential race conditions with isLoading state.
      } catch (error) {
        console.error('Error fetching tasks:', error);
        setIsLoading(false);
      }
    };

    fetchTasks();
  }, []);

  // Memoize fetchInsights, now accepting page and limit
  const fetchInsights = useCallback(async (page: number, limit: number) => {
    setIsLoading(true);
    setError(null);
    try {
      // Call the updated AI service method with page and limit
      const response = await aiService.getInsights(page, limit); // Use aiService and pass params
      setInsights(response.data); // Use response.data
      setTotalPages(response.pagination.totalPages); // Use response.pagination
      setTotalItems(response.pagination.totalItems); // Use response.pagination
      // Ensure currentPage state doesn't exceed totalPages fetched
      // This handles cases where the user might be on a page that no longer exists after a refresh/action
      setCurrentPage(response.pagination.currentPage); // Use response.pagination
    } catch (err: any) {
      console.error(`Failed to fetch AI insights (page: ${page}, limit: ${limit}):`, err);
      setError(err.message || `Failed to load AI insights for page ${page}.`);
      toast({
        title: 'Error Loading Insights',
        description: err.message || 'Could not fetch AI suggestions.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  // Removed toast from dependencies as it's stable, added pageSize
  }, [pageSize]); // fetchInsights depends on pageSize now

  // useEffect to fetch insights when page, size, or refreshKey changes
  useEffect(() => {
    // Fetch insights for the current page and size
    fetchInsights(currentPage, pageSize);
  // Added currentPage and pageSize dependencies
  }, [fetchInsights, currentPage, pageSize, refreshKey]);

  // Helper function to validate insight IDs
  const isValidInsightId = (insightId: string): boolean => {
    if (!insightId || typeof insightId !== 'string' || !/^[0-9a-fA-F]{24}$/.test(insightId)) {
      console.error(`Invalid insight ID format: ${insightId}`);
      return false;
    }
    return true;
  };

  const handleAction = async (insightId: string, action: 'accept' | 'dismiss') => {
    setActionLoading(insightId);
    try {
      if (!isValidInsightId(insightId)) {
        toast({
          title: `Error ${action === 'accept' ? 'Accepting' : 'Dismissing'} Suggestion`,
          description: `Invalid insight ID format: ${insightId}`,
          variant: 'destructive',
        });
        setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
        setActionLoading(null);
        return;
      }

      // Find the insight to check its type
      const insight = insights.find(i => i._id === insightId);

      // Special handling for AI autocorrection insights when accepting
      if (action === 'accept' && insight?.type === 'AI_AUTOCORRECTION_REVIEW' && insight.relatedData?.taskId) {
        try {
          // Update the task metadata to remove the autocorrection flag
          await taskService.updateTask(insight.relatedData.taskId as string, {
            metadata: {
              aiWasAutoCorrected: false,
              aiOriginalTextSegment: null,
              aiCorrectedTextSegment: null
            }
          });
          console.log(`Updated task ${insight.relatedData.taskId} metadata to remove autocorrection flag`);
        } catch (taskErr) {
          console.error(`Failed to update task metadata for ${insight.relatedData.taskId}:`, taskErr);
          // Continue with insight dismissal even if task update fails
        }
      }

      await insightService.handleInsightAction(insightId, action);
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      toast({
        title: `Suggestion ${action === 'accept' ? 'Accepted' : 'Dismissed'}`,
        description: `The AI suggestion has been ${action === 'accept' ? 'accepted' : 'dismissed'}.`,
      });
      if (action === 'accept' && onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error(`Failed to ${action} insight ${insightId}:`, err);

      let errorMsg = err.message || `Could not ${action} the AI suggestion.`;
      if (err.response?.data?.error?.message) {
        errorMsg = err.response.data.error.message;
      }

      toast({
        title: `Error ${action === 'accept' ? 'Accepting' : 'Dismissing'} Suggestion`,
        description: errorMsg,
        variant: 'destructive',
      });

      if (err.response?.status === 400 || err.response?.status === 404) {
        setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      }
    } finally {
      setActionLoading(null);
    }
  };

  const handleAccept = (insightId: string) => {
    handleAction(insightId, 'accept');
  };

  const handleDismiss = (insightId: string) => {
    handleAction(insightId, 'dismiss');
  };

  // Handler for marking an overdue task complete
  const handleMarkComplete = async (taskId: string, insightId: string) => {
    if (!isValidInsightId(insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      return;
    }

    setActionLoading(insightId); // Use insightId to track loading state
    try {
      // Check if the task exists before trying to update it
      try {
        await taskService.getTaskById(taskId);
      } catch (taskErr: any) {
        if (axios.isAxiosError(taskErr) && taskErr.response?.status === 404) {
          // Task already deleted or doesn't exist, just dismiss the insight
          await insightService.handleInsightAction(insightId, 'dismiss');
          setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
          toast({
            title: 'Task No Longer Exists',
            description: `The task no longer exists. Insight dismissed.`,
          });
          if (onActionComplete) {
            onActionComplete();
          }
          setActionLoading(null);
          return;
        }
      }

      // 1. Mark the task as complete
      await taskService.updateTask(taskId, { completed: true });
      toast({
        title: 'Task Marked Complete',
        description: `Task associated with the suggestion has been marked as complete.`,
      });

      // 2. Dismiss the insight (as the action is done)
      await insightService.handleInsightAction(insightId, 'dismiss');
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));

      // 3. Trigger general refresh if needed
      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error(`Failed to mark task ${taskId} complete or dismiss insight ${insightId}:`, err);
      toast({
        title: 'Error Completing Task',
        description: err.message || `Could not mark the overdue task as complete.`,
        variant: 'destructive',
      });

      // If the error relates to the insight, remove it from the UI
      if (err.response?.status === 400 || err.response?.status === 404) {
        if (err.response?.data?.error?.message?.includes('Insight')) {
          setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
        }
      }
    } finally {
      setActionLoading(null);
    }
  };

  // Handler to open the delete confirmation modal
  const openDeleteModal = (taskId: string, insightId: string) => {
    // Find the insight to get the task title for the modal description
    const insight = insights.find(i => i._id === insightId);
    setTaskToDelete({
      id: taskId,
      title: insight?.relatedData?.taskTitle, // Get title from insight data if available
      insightId: insightId,
    });
    setIsDeleteDialogOpen(true);
  };

  // Handler for confirming task deletion
  const handleDeleteTaskConfirm = async () => {
    if (!taskToDelete) return;

    if (!isValidInsightId(taskToDelete.insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== taskToDelete.insightId));
      setIsDeleteDialogOpen(false);
      setTaskToDelete(null);
      return;
    }

    setActionLoading(taskToDelete.insightId); // Use insightId for loading state
    try {
      // Check if the task exists before trying to delete it
      try {
        await taskService.getTaskById(taskToDelete.id);
      } catch (taskErr: any) {
        if (axios.isAxiosError(taskErr) && taskErr.response?.status === 404) {
          // Task already deleted or doesn't exist, just dismiss the insight
          try {
            await insightService.handleInsightAction(taskToDelete.insightId, 'dismiss');
          } catch (insightErr: any) {
            console.error(`Error dismissing insight after task not found: ${insightErr.message}`);
            // Continue anyway as we want to remove the insight from UI
          }
          setInsights((prevInsights) => prevInsights.filter((i) => i._id !== taskToDelete.insightId));
          toast({
            title: 'Task Already Deleted',
            description: `The task no longer exists. Insight dismissed.`,
          });
          setIsDeleteDialogOpen(false);
          setTaskToDelete(null);
          if (onActionComplete) {
            onActionComplete();
          }
          setActionLoading(null);
          return;
        }
        // If it's another error fetching the task, rethrow or handle
        throw taskErr;
      }

      // 1. Delete the task
      await taskService.deleteTask(taskToDelete.id);
      toast({
        title: 'Task Deleted',
        description: `Task "${taskToDelete.title || 'Task'}" has been deleted.`,
      });

      // 2. Try to dismiss the insight - with error handling if it fails
      try {
        await insightService.handleInsightAction(taskToDelete.insightId, 'dismiss');
      } catch (insightErr: any) {
        console.error(`Error dismissing insight after task deletion: ${insightErr.message}`);
        // Continue anyway as we want to remove the insight from UI
      }

      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== taskToDelete.insightId));

      // 3. Close modal and trigger refresh
      setIsDeleteDialogOpen(false);
      setTaskToDelete(null);
      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      // Check if the error was the task fetch error rethrown above
      if (axios.isAxiosError(err) && err.config?.url?.includes(`/tasks/${taskToDelete.id}`) && err.response?.status !== 404) {
        // Error fetching task before deletion attempt
        console.error(`Error fetching task ${taskToDelete.id} before deletion:`, err);
        toast({
          title: 'Error Preparing Deletion',
          description: err.message || 'Could not verify task status before deletion.',
          variant: 'destructive',
        });
      } else {
        // Error during deletion itself or subsequent steps (though dismissal is removed)
        console.error(`Failed to delete task ${taskToDelete.id}:`, err);
        toast({
          title: 'Error Deleting Task',
          description: err.message || `Could not delete the task.`,
          variant: 'destructive',
        });
      }
      // Close modal on error
      setIsDeleteDialogOpen(false);
      setTaskToDelete(null);
    } finally {
      setActionLoading(null);
    }
  };

  // Handler to open the change deadline modal
  const openChangeDeadlineModal = async (taskId: string, insightId: string) => {
    setActionLoading(insightId); // Show loading while fetching task details
    try {
      // Fetch task details to get current deadline and title accurately
      const task = await taskService.getTaskById(taskId);
      setTaskToChangeDeadline({
        id: taskId,
        title: task.title,
        currentDeadline: task.deadline ? (typeof task.deadline === 'string' ? new Date(task.deadline) : task.deadline) : undefined,
        insightId: insightId,
      });
      setIsChangeDeadlineOpen(true);
    } catch (err: any) {
      console.error(`Failed to fetch task ${taskId} details for deadline change:`, err);
      toast({
        title: 'Error Preparing Deadline Change',
        description: err.message || 'Could not load task details.',
        variant: 'destructive',
      });
    } finally {
       setActionLoading(null); // Stop loading indicator
    }
  };

  // Handler for reverting AI autocorrection
  const handleRevertCorrection = async (taskId: string, insightId: string, originalText: string, correctedText: string, taskTitle: string) => {
    if (!isValidInsightId(insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      return;
    }

    setActionLoading(insightId);
    try {
      // Check if the task exists before trying to update it
      try {
        await taskService.getTaskById(taskId);
      } catch (taskErr: any) {
        if (axios.isAxiosError(taskErr) && taskErr.response?.status === 404) {
          // Task already deleted or doesn't exist, just dismiss the insight
          try {
            await insightService.handleInsightAction(insightId, 'dismiss');
          } catch (insightErr: any) {
            console.error(`Error dismissing insight after task not found: ${insightErr.message}`);
            // Continue anyway as we want to remove the insight from UI
          }
          setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
          toast({
            title: 'Task No Longer Exists',
            description: `The task no longer exists. Insight dismissed.`,
          });
          if (onActionComplete) {
            onActionComplete();
          }
          setActionLoading(null);
          return;
        }
      }

      // 1. Revert the task title/content to the original text
      // We need to determine if the correction was in the title, content, or both
      // Update both the text and metadata
      await taskService.updateTask(taskId, {
        title: originalText,
        content: originalText,
        metadata: {
          aiWasAutoCorrected: false,
          aiOriginalTextSegment: null,
          aiCorrectedTextSegment: null
        }
      });

      toast({
        title: 'Correction Reverted',
        description: `Reverted AI correction for "${taskTitle}".`,
      });

      // 2. Dismiss the insight with error handling
      try {
        await insightService.handleInsightAction(insightId, 'dismiss');
      } catch (insightErr: any) {
        console.error(`Error dismissing insight after reverting correction: ${insightErr.message}`);
        // Continue anyway as we want to remove the insight from UI
      }

      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));

      // 3. Trigger general refresh if needed
      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error(`Failed to revert correction for task ${taskId} or dismiss insight ${insightId}:`, err);
      toast({
        title: 'Error Reverting Correction',
        description: err.message || `Could not revert the AI correction.`,
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Handler for editing a task that needs clarification
  const handleEditTask = async (taskId: string, insightId: string) => {
    if (!isValidInsightId(insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      return;
    }

    setActionLoading(insightId);
    try {
      // Check if the task exists before trying to update it
      try {
        await taskService.getTaskById(taskId);
      } catch (taskErr: any) {
        if (axios.isAxiosError(taskErr) && taskErr.response?.status === 404) {
          // Task already deleted or doesn't exist, just dismiss the insight
          try {
            await insightService.handleInsightAction(insightId, 'dismiss');
          } catch (insightErr: any) {
            console.error(`Error dismissing insight after task not found: ${insightErr.message}`);
            // Continue anyway as we want to remove the insight from UI
          }
          setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
          toast({
            title: 'Task No Longer Exists',
            description: `The task no longer exists. Insight dismissed.`,
          });
          if (onActionComplete) {
            onActionComplete();
          }
          setActionLoading(null);
          return;
        }
      }

      // Update the task metadata to remove the clarification needed flag
      await taskService.updateTask(taskId, {
        metadata: {
          aiClarificationNeeded: null
        }
      });

      // Dismiss the insight
      try {
        await insightService.handleInsightAction(insightId, 'dismiss');
      } catch (insightErr: any) {
        console.error(`Error dismissing insight after editing task: ${insightErr.message}`);
        // Continue anyway as we want to remove the insight from UI
      }

      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));

      // Navigate to the task edit page
      router.push(`/tasks/${taskId}/edit?source=ai-insight`);

      toast({
        title: 'Task Ready for Edit',
        description: `You can now edit the task to provide clarification.`,
      });

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error(`Failed to prepare task ${taskId} for editing or dismiss insight ${insightId}:`, err);
      toast({
        title: 'Error Preparing Task for Edit',
        description: err.message || `Could not prepare the task for editing.`,
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Handler for confirming the deadline change
  const handleConfirmChangeDeadline = async (newDeadline: Date) => {
    if (!taskToChangeDeadline) return;

    if (!isValidInsightId(taskToChangeDeadline.insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== taskToChangeDeadline.insightId));
      setIsChangeDeadlineOpen(false);
      setTaskToChangeDeadline(null);
      return;
    }

    setActionLoading(taskToChangeDeadline.insightId);
    try {
      // Check if the task exists before trying to update it
      try {
        await taskService.getTaskById(taskToChangeDeadline.id);
      } catch (taskErr: any) {
        if (axios.isAxiosError(taskErr) && taskErr.response?.status === 404) {
          // Task already deleted or doesn't exist, just dismiss the insight
          try {
            await insightService.handleInsightAction(taskToChangeDeadline.insightId, 'dismiss');
          } catch (insightErr: any) {
            console.error(`Error dismissing insight after task not found: ${insightErr.message}`);
            // Continue anyway as we want to remove the insight from UI
          }
          setInsights((prevInsights) => prevInsights.filter((i) => i._id !== taskToChangeDeadline.insightId));
          toast({
            title: 'Task No Longer Exists',
            description: `The task no longer exists. Insight dismissed.`,
          });
          setIsChangeDeadlineOpen(false);
          setTaskToChangeDeadline(null);
          if (onActionComplete) {
            onActionComplete();
          }
          setActionLoading(null);
          return;
        }
      }

      // 1. Update the task deadline
      await taskService.updateTask(taskToChangeDeadline.id, { deadline: newDeadline });
      toast({
        title: 'Task Deadline Updated',
        description: `Deadline for task "${taskToChangeDeadline.title || 'Task'}" updated.`,
      });

      // 2. Dismiss the insight - with error handling
      try {
        await insightService.handleInsightAction(taskToChangeDeadline.insightId, 'dismiss');
      } catch (insightErr: any) {
        console.error(`Error dismissing insight after deadline change: ${insightErr.message}`);
        // Continue anyway as we want to remove the insight from UI
      }

      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== taskToChangeDeadline.insightId));

      // 3. Close modal and trigger refresh
      setIsChangeDeadlineOpen(false);
      setTaskToChangeDeadline(null);
      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error(`Failed to update deadline for task ${taskToChangeDeadline.id} or dismiss insight ${taskToChangeDeadline.insightId}:`, err);
      toast({
        title: 'Error Updating Deadline',
        description: err.message || `Could not update the task deadline.`,
        variant: 'destructive',
      });
      // Keep modal open on error? Let's close for now.
      setIsChangeDeadlineOpen(false);
      setTaskToChangeDeadline(null);
    } finally {
      setActionLoading(null);
    }
  };

  // Enhanced version of openReviewDuplicatesModal to fetch more task details
  const openReviewDuplicatesModal = async (taskIds: string[], insightId: string) => {
    if (taskIds.length < 2) {
      console.error("Cannot review less than 2 duplicate tasks");
      return;
    }

    if (!isValidInsightId(insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      return;
    }

    setActionLoading(insightId);
    setCurrentReviewInsightId(insightId);
    try {
      // Find the matching insight to get the matchType (exact/similar)
      const insight = insights.find(i => i._id === insightId);
      const matchType = insight?.relatedData?.matchType || 'exact';

      // Fetch full task details for all tasks
      const taskPromises = taskIds.map(id => taskService.getTaskById(id).catch(err => {
        // Handle missing tasks gracefully
        if (axios.isAxiosError(err) && err.response?.status === 404) {
          console.warn(`Task ${id} no longer exists.`);
          return null; // Return null for missing tasks
        }
        throw err; // Rethrow other errors
      }));

      const resolvedTasks = await Promise.all(taskPromises);

      // Filter out null results (deleted tasks)
      const validTasks = resolvedTasks.filter(task => task !== null) as Task[];

      // If too many tasks are missing, just dismiss the insight
      if (validTasks.length < 2) {
        try {
          await insightService.handleInsightAction(insightId, 'dismiss');
        } catch (insightErr: any) {
          console.error(`Error dismissing insight with missing tasks: ${insightErr.message}`);
          // Continue anyway as we want to remove the insight from UI
        }

        setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
        toast({
          title: 'Cannot Review Duplicates',
          description: 'Some tasks no longer exist. The insight has been dismissed.',
        });
        if (onActionComplete) {
          onActionComplete();
        }
        setActionLoading(null);
        setCurrentReviewInsightId(null);
        return;
      }

      // Map tasks to a simpler format for the review modal
      const tasksForReview = validTasks.map(task => ({
        id: task._id,
        title: task.title,
        content: task.content,
        createdAt: task.createdAt,
      }));

      setTasksToReview(tasksForReview);
      console.log(`Opening review for ${validTasks.length} tasks with match type: ${matchType}`, tasksForReview);
      setIsReviewDuplicatesOpen(true);
    } catch (err: any) {
      console.error("Failed to fetch task details for duplicate review:", err);
      toast({
        title: "Error Preparing Review",
        description: err.message || "Could not retrieve task details.",
        variant: "destructive",
      });

      // If this was a critical error (not just missing tasks), dismiss the insight
      try {
        await handleDismiss(insightId);
      } catch (dismissErr) {
        console.error("Failed to dismiss invalid insight:", dismissErr);
        // Remove from UI anyway
        setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      }

      setCurrentReviewInsightId(null);
    } finally {
      setActionLoading(null);
    }
  };

  // Handler for confirming deletion of selected duplicates
  const handleConfirmDeleteDuplicates = async (taskIdsToDelete: string[]) => {
    if (!currentReviewInsightId || taskIdsToDelete.length === 0) return;

    if (!isValidInsightId(currentReviewInsightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== currentReviewInsightId));
      setIsReviewDuplicatesOpen(false);
      setTasksToReview([]);
      setCurrentReviewInsightId(null);
      return;
    }

    setActionLoading(currentReviewInsightId);
    try {
      // 1. Delete the selected tasks
      const deletePromises = taskIdsToDelete.map(id => taskService.deleteTask(id));
      await Promise.all(deletePromises);
      toast({
        title: 'Duplicate Tasks Deleted',
        description: `${taskIdsToDelete.length} task(s) have been deleted.`,
      });

      // 2. Dismiss the insight with error handling
      try {
        await insightService.handleInsightAction(currentReviewInsightId, 'dismiss');
      } catch (insightErr: any) {
        console.error(`Error dismissing insight after deleting duplicates: ${insightErr.message}`);
        // Continue anyway as we want to remove the insight from UI
      }

      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== currentReviewInsightId));

      // 3. Close modal and trigger refresh
      setIsReviewDuplicatesOpen(false);
      setTasksToReview([]);
      setCurrentReviewInsightId(null);
      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error(`Failed to delete duplicate tasks or dismiss insight ${currentReviewInsightId}:`, err);
      toast({
        title: 'Error Deleting Duplicates',
        description: err.message || `Could not delete the selected duplicate tasks.`,
        variant: 'destructive',
      });
      // Keep modal open on error? Let's close for now.
      setIsReviewDuplicatesOpen(false);
      setTasksToReview([]);
      setCurrentReviewInsightId(null);
    } finally {
      setActionLoading(null);
    }
  };

  // Add a function to fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      // Fetch categories - handle response structure
      const response = await categoryService.getCategories();

      // The API might return { categories: [...], completedTaskCount: number }
      // Or directly the categories array. We need to handle both cases
      const categoriesData = response.categories || response;

      // Make sure we have an array of categories
      if (!Array.isArray(categoriesData)) {
        console.error("Categories data is not an array:", categoriesData);
        setAvailableCategories([]);
        return;
      }

      // Extract and flatten the categories array from the response
      const flatten = (cats: Category[]): Category[] => {
        let flatList: Category[] = [];
        cats.forEach(cat => {
          flatList.push(cat);
          if (cat.subcategories && cat.subcategories.length > 0) {
            flatList = flatList.concat(flatten(cat.subcategories));
          }
        });
        return flatList;
      };

      // Flatten the categories array
      setAvailableCategories(flatten(categoriesData));
    } catch (error) {
      console.error("Failed to fetch categories for insights:", error);
      // Don't show a toast here, as it's not critical for the user experience
      setAvailableCategories([]);
    }
  }, []);

  // Add useEffect to fetch categories when component mounts
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Enhanced version of openMergeTasksModal to fetch more task details
  const openMergeTasksModal = async (taskIds: string[], insightId: string) => {
    if (taskIds.length < 2) {
      console.error("Cannot merge less than 2 tasks");
      return;
    }

    if (!isValidInsightId(insightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      return;
    }

    setActionLoading(insightId);
    setCurrentMergeInsightId(insightId);
    try {
      // Load all categories for the merge form
      const allCategories = await categoryService.getCategories();
      setAvailableCategories(allCategories.categories || []);

      // Fetch full task details for all tasks with error handling
      const taskPromises = taskIds.map(id => taskService.getTaskById(id).catch(err => {
        // Handle missing tasks gracefully
        if (axios.isAxiosError(err) && err.response?.status === 404) {
          console.warn(`Task ${id} no longer exists.`);
          return null; // Return null for missing tasks
        }
        throw err; // Rethrow other errors
      }));

      const resolvedTasks = await Promise.all(taskPromises);

      // Filter out null results (deleted tasks)
      const validTasks = resolvedTasks.filter(task => task !== null) as Task[];

      // If too many tasks are missing, just dismiss the insight
      if (validTasks.length < 2) {
        await insightService.handleInsightAction(insightId, 'dismiss');
        setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
        toast({
          title: 'Cannot Merge Tasks',
          description: 'Some tasks no longer exist. The insight has been dismissed.',
        });
        if (onActionComplete) {
          onActionComplete();
        }
        setActionLoading(null);
        return;
      }

      // Process the valid tasks
      const flattened = validTasks.map(task => {
        // Extract category IDs if populated objects, or use the IDs directly
        const categoryIds = Array.isArray(task.categories)
          ? task.categories.map(cat => typeof cat === 'string' ? cat : cat._id)
          : [];

        // Return simplified task for merge UI
        return {
          id: task._id,
          title: task.title,
          content: task.content,
          createdAt: task.createdAt,
          priority: task.priority,
          categories: categoryIds,
          deadline: task.deadline ? new Date(task.deadline) : undefined,
        };
      });

      setTasksToMerge(flattened);
      setIsMergeTasksOpen(true);
    } catch (err: any) {
      console.error("Failed to fetch task details for merge:", err);
      toast({
        title: "Error Preparing Merge",
        description: err.message || "Could not retrieve task details for merging.",
        variant: "destructive",
      });

      // If error occurs, try to dismiss the insight or at least remove it from UI
      try {
        await handleDismiss(insightId);
      } catch (dismissErr) {
        console.error("Failed to dismiss invalid insight:", dismissErr);
        setInsights((prevInsights) => prevInsights.filter((i) => i._id !== insightId));
      }

      setCurrentMergeInsightId(null);
    } finally {
      setActionLoading(null);
    }
  };

  // Enhanced version of handleConfirmMerge to handle expanded task data
  const handleConfirmMerge = async (mergedTaskData: Partial<TaskDTO>) => {
    if (!currentMergeInsightId || tasksToMerge.length < 2) return;

    if (!isValidInsightId(currentMergeInsightId)) {
      toast({
        title: 'Error Processing Action',
        description: 'Invalid insight ID format. This insight will be removed from your list.',
        variant: 'destructive',
      });
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== currentMergeInsightId));
      setIsMergeTasksOpen(false);
      setTasksToMerge([]);
      setCurrentMergeInsightId(null);
      return;
    }

    setActionLoading(currentMergeInsightId);
    try {
      // Refresh CSRF token before making state-changing requests
      try {
        await authService.refreshCsrfToken();
      } catch (csrfError) {
        console.error('Failed to refresh CSRF token before merging tasks:', csrfError);
        // Continue anyway and hope the existing token works
      }

      // 1. Create the new merged task with all the provided data
      const newTaskResponse = await taskService.createTask({
        title: mergedTaskData.title!,
        content: mergedTaskData.content || "",
        priority: mergedTaskData.priority || "Medium",
        categories: mergedTaskData.categories || [],
        deadline: mergedTaskData.deadline || undefined
      });

      // 2. Delete the original tasks
      const deletePromises = tasksToMerge.map(task => taskService.deleteTask(task.id));
      await Promise.all(deletePromises);

      // 3. Dismiss the insight with error handling
      try {
        await insightService.handleInsightAction(currentMergeInsightId, 'dismiss');
      } catch (insightErr: any) {
        console.error(`Error dismissing insight after merging tasks: ${insightErr.message}`);
        // Continue anyway as we want to remove the insight from UI
      }

      // 4. Update the UI
      setInsights((prevInsights) => prevInsights.filter((i) => i._id !== currentMergeInsightId));

      // 5. Show success message
      toast({
        title: "Tasks Merged Successfully",
        description: `Created new task "${mergedTaskData.title}" and deleted ${tasksToMerge.length} original tasks.`,
      });

      // 6. Close modal and trigger refresh
      setIsMergeTasksOpen(false);
      setTasksToMerge([]);
      setCurrentMergeInsightId(null);

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error("Failed to merge tasks:", err);
      toast({
        title: "Error Merging Tasks",
        description: err.message || "There was a problem merging the tasks.",
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleRefreshInsights = async () => {
    setIsRefreshing(true);
    try {
      await insightService.generateInsights();

      toast({
        title: 'Generating Insights',
        description: 'Generating new insights. Please wait a moment...',
      });

      await new Promise(resolve => setTimeout(resolve, 2000));
      // Call fetchInsights with the current page and page size
      await fetchInsights(currentPage, pageSize); // Corrected: Pass current page and size

      toast({
        title: 'Insights Refreshed',
        description: 'AI insights have been refreshed with the latest data.',
      });

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err: any) {
      console.error('Failed to refresh insights:', err);
      toast({
        title: 'Error Refreshing Insights',
        description: err.message || 'Could not refresh AI insights.',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Utility function to handle error display with specific formatting for 404 errors
  const handleTaskActionError = (err: any, actionType: string, insightId: string): boolean => {
    console.error(`Task action error (${actionType}) for insight ${insightId}:`, err);

    // Check for 404 errors specifically
    if (axios.isAxiosError(err) && err.response?.status === 404) {
      // Special handling for missing tasks
      toast({
        title: 'Task No Longer Exists',
        description: 'The task referenced by this insight no longer exists. The insight will be dismissed.',
      });

      // Try to dismiss the insight and return true to indicate we handled the error
      try {
        handleDismiss(insightId);
      } catch (dismissErr: unknown) {
        console.error(`Failed to dismiss insight ${insightId} after 404 error:`, dismissErr);
      }

      return true; // Error was handled
    }

    // Default error handling
    toast({
      title: `Error ${actionType}`,
      description: err.message || `An error occurred while performing this action.`,
      variant: 'destructive',
    });

    return false; // Error was not specially handled
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">Loading AI Insights...</span>
      </div>
    );
  }

  if (error) {
    return (
       <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
       </Alert>
    );
  }

  if (insights.length === 0) {
    return (
      <div className="text-center p-6 space-y-4">
        <p className="text-muted-foreground">No insights available at the moment.</p>
        <Button
          variant="outline"
          onClick={handleRefreshInsights}
          disabled={isRefreshing}
          className="mx-auto"
        >
          <RefreshCcw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Generate Insights'}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {isLoading ? (
        <div className="text-center p-4">
          <p className="text-muted-foreground">Loading insights...</p>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">AI Insights</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshInsights}
              disabled={isRefreshing}
            >
              <RefreshCcw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>

          {Array.isArray(insights) && insights.length > 0 ? (
            insights.map((insight) => (
              <InsightCard
                key={insight._id}
                insight={insight}
                onAccept={() => handleAccept(insight._id)}
                onDismiss={() => handleDismiss(insight._id)}
                onMarkComplete={(taskId) => handleMarkComplete(taskId, insight._id)}
                onDeleteTask={(taskId) => openDeleteModal(taskId, insight._id)}
                onChangeDeadline={(taskId) => openChangeDeadlineModal(taskId, insight._id)}
                onRevertCorrection={(taskId, insightId, originalText, correctedText, taskTitle) => handleRevertCorrection(taskId, insightId, originalText, correctedText, taskTitle)}
                onEditTask={(taskId) => handleEditTask(taskId, insight._id)}
                onReviewDuplicates={(tasks) => openReviewDuplicatesModal(tasks, insight._id)}
                onMergeTasks={(tasks) => openMergeTasksModal(tasks, insight._id)}
                isLoading={actionLoading === insight._id}
              />
            ))
          ) : (
            <p className="text-center text-muted-foreground p-4">No insights available.</p>
          )}

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <Pagination className="mt-6">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (currentPage > 1) setCurrentPage(currentPage - 1); }}
                    aria-disabled={currentPage <= 1 || isLoading || isRefreshing || !!actionLoading}
                  />
                </PaginationItem>
                {/* Basic page number display - can be enhanced later */}
                {[...Array(totalPages)].map((_, i) => (
                  <PaginationItem key={i}>
                    <PaginationLink
                      href="#"
                      onClick={(e) => { e.preventDefault(); setCurrentPage(i + 1); }}
                      isActive={currentPage === i + 1}
                      aria-disabled={isLoading || isRefreshing || !!actionLoading}
                    >
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                {/* Add Ellipsis if needed - logic depends on desired complexity */}
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (currentPage < totalPages) setCurrentPage(currentPage + 1); }}
                    aria-disabled={currentPage >= totalPages || isLoading || isRefreshing || !!actionLoading}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </>
      )}

      {/* Render the Delete Confirmation Modal */}
      <ConfirmDeleteTaskModal
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteTaskConfirm}
        taskTitle={taskToDelete?.title}
        isLoading={actionLoading === taskToDelete?.insightId}
      />

      {/* Render the Change Deadline Modal */}
      <ChangeDeadlineModal
        isOpen={isChangeDeadlineOpen}
        onClose={() => setIsChangeDeadlineOpen(false)}
        onConfirm={handleConfirmChangeDeadline}
        taskTitle={taskToChangeDeadline?.title}
        currentDeadline={taskToChangeDeadline?.currentDeadline}
        isLoading={actionLoading === taskToChangeDeadline?.insightId}
      />

       {/* Render the Review Duplicates Modal */}
       <ReviewDuplicatesModal
         isOpen={isReviewDuplicatesOpen}
         onClose={() => {
           setIsReviewDuplicatesOpen(false);
           setTasksToReview([]);
           setCurrentReviewInsightId(null);
         }}
         onConfirmDelete={handleConfirmDeleteDuplicates}
         tasks={tasksToReview}
         matchType={currentReviewInsightId && Array.isArray(insights) ?
           insights.find(i => i._id === currentReviewInsightId)?.relatedData?.matchType || 'exact'
           : 'exact'}
         isLoading={actionLoading === currentReviewInsightId}
       />

       {/* Render the Merge Tasks Modal */}
       <MergeTasksModal
         isOpen={isMergeTasksOpen}
         onClose={() => setIsMergeTasksOpen(false)}
         onConfirmMerge={handleConfirmMerge}
         tasksToMerge={tasksToMerge}
         categories={availableCategories}
         isLoading={actionLoading === currentMergeInsightId}
       />
    </div>
  );
};
