import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flashtasks_mobile/src/core/services/notification_service.dart';
import 'package:flashtasks_mobile/src/core/services/permission_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

// Mock FirebasePlatform using the method described by Firebase
class MockFirebasePlatform extends Mock
    with MockPlatformInterfaceMixin
    implements FirebasePlatform {
  @override
  FirebaseAppPlatform app([String name = defaultFirebaseAppName]) {
    return MockFirebaseAppPlatform();
  }

  @override
  List<FirebaseAppPlatform> get apps => <FirebaseAppPlatform>[];

  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    return MockFirebaseAppPlatform();
  }
}

class MockFirebaseAppPlatform extends Mock implements FirebaseAppPlatform {}

// Mock PermissionService
class MockPermissionService extends Mock implements PermissionService {
  @override
  Future<bool> isNotificationPermissionGranted() async => true;

  @override
  Future<bool> requestNotificationPermission() async => true;
}

// Mock FirebaseMessagingHandler indirectly by mocking methods it calls
class MockFirebaseMessaging extends Mock implements FirebaseMessaging {
  @override
  Future<String?> getToken({
    String? vapidKey,
  }) async => 'mock-fcm-token';

  @override
  Future<void> subscribeToTopic(String topic) async {}

  @override
  Future<void> unsubscribeFromTopic(String topic) async {}
}

// Mock for FlutterLocalNotificationsPlugin
class MockFlutterLocalNotificationsPlugin extends Mock implements FlutterLocalNotificationsPlugin {
  @override
  Future<bool?> initialize(
    InitializationSettings initializationSettings, {
    DidReceiveNotificationResponseCallback? onDidReceiveNotificationResponse,
    DidReceiveBackgroundNotificationResponseCallback? onDidReceiveBackgroundNotificationResponse,
  }) async => true;

  @override
  Future<void> show(
    int id,
    String? title,
    String? body,
    NotificationDetails? notificationDetails, {
    String? payload,
  }) async {}
}

// Setup Firebase mocks
Future<void> setupFirebaseMocks() async {
  // Setup mock FirebasePlatform instance
  FirebasePlatform.instance = MockFirebasePlatform();

  // Mock SharedPreferences
  SharedPreferences.setMockInitialValues({});
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late NotificationService notificationService;
  late MockPermissionService mockPermissionService;

  setUp(() async {
    // Setup Firebase mocks
    await setupFirebaseMocks();

    // Create mocks
    mockPermissionService = MockPermissionService();

    // Create test instance
    notificationService = NotificationService(permissionService: mockPermissionService);
  });

  group('NotificationService', () {
    testWidgets('initialization completes without errors', (WidgetTester tester) async {
      // Act & Assert - Should complete without throwing
      await expectLater(
        notificationService.initialize(),
        completes,
      );
    });

    testWidgets('getDeviceToken returns token when permission granted', (WidgetTester tester) async {
      // Act & Assert - may be null in test environment but should not throw
      expect(() => notificationService.getDeviceToken(), returnsNormally);
    });

    testWidgets('showLocalNotification works correctly', (WidgetTester tester) async {
      // Act & Assert
      await expectLater(
        notificationService.showLocalNotification(
          title: 'Test',
          body: 'Test Body',
          payload: 'test-payload',
        ),
        completes,
      );
    });

    testWidgets('subscribeToTopic works correctly', (WidgetTester tester) async {
      // Act & Assert
      await expectLater(
        notificationService.subscribeToTopic('test-topic'),
        completes,
      );
    });

    testWidgets('unsubscribeFromTopic works correctly', (WidgetTester tester) async {
      // Act & Assert
      await expectLater(
        notificationService.unsubscribeFromTopic('test-topic'),
        completes,
      );
    });
  });
}
