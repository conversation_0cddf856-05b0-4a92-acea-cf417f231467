import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../models/insight.dart';
import '../providers/insight_provider.dart';
import 'package:go_router/go_router.dart';

/// Widget for displaying an individual insight card
class InsightCardWidget extends ConsumerWidget {
  final Insight insight;
  final bool isLoading;
  final Function(String taskId)? onNavigateToTask;

  const InsightCardWidget({
    super.key,
    required this.insight,
    this.isLoading = false,
    this.onNavigateToTask,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getBorderColor(insight.type, theme),
          width: 1,
        ),
      ),
      color: _getBackgroundColor(insight.type, theme),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon, title, and date
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      _getInsightIcon(insight.type),
                      size: 20,
                      color: _getIconColor(insight.type, theme),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _getTitle(insight.type),
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  DateFormat.yMd().format(insight.createdAt),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          
          // Description
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 4, 16, 8),
            child: Text(
              insight.description,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          
          // Related context (specific to insight type)
          _buildRelatedContext(context, theme),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              alignment: WrapAlignment.end,
              spacing: 8.0,
              runSpacing: 4.0,
              children: [
                ..._buildSpecificActionButtons(context, theme, ref),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: const Size(80, 36),
                  ),
                  onPressed: isLoading ? null : () => _handleDismiss(ref),
                  child: const Text('Dismiss'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: const Size(80, 36),
                  ),
                  onPressed: isLoading ? null : () => _handleAccept(ref),
                  child: const Text('Accept'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Handle accept action
  void _handleAccept(WidgetRef ref) {
    ref.read(insightProvider.notifier).handleAction(insight.id, 'accepted');
  }

  // Handle dismiss action
  void _handleDismiss(WidgetRef ref) {
    ref.read(insightProvider.notifier).handleAction(insight.id, 'dismissed');
  }

  // Handle navigation to task detail
  void _navigateToTaskDetail(BuildContext context, String taskId) {
    if (onNavigateToTask != null) {
      onNavigateToTask!(taskId);
    } else {
      context.pushNamed(
        'taskDetail', 
        pathParameters: {'taskId': taskId},
      );
    }
  }
  
  // Handle mark task as complete
  void _handleMarkComplete(WidgetRef ref, String taskId) {
    ref.read(insightProvider.notifier).markTaskComplete(taskId, insight.id);
  }
  
  // Handle change task deadline
  void _handleChangeDeadline(BuildContext context, WidgetRef ref, String taskId) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (pickedDate != null) {
      ref.read(insightProvider.notifier).changeTaskDeadline(taskId, insight.id, pickedDate);
    }
  }
  
  // Handle delete task
  void _handleDeleteTask(BuildContext context, WidgetRef ref, String taskId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: const Text('Are you sure you want to delete this task?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(insightProvider.notifier).deleteTask(taskId, insight.id);
            },
            style: TextButton.styleFrom(foregroundColor: Theme.of(context).colorScheme.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
  
  // Handle review duplicates
  void _handleReviewDuplicates(BuildContext context, WidgetRef ref, List<String> taskIds) {
    // Convert taskIds to a comma-separated string for URL
    final taskIdsStr = taskIds.join(',');
    
    context.push(
      '/dashboard/insights/review-duplicates?taskIds=$taskIdsStr&insightId=${insight.id}',
    );
  }
  
  // Handle merge tasks
  void _handleMergeTasks(BuildContext context, WidgetRef ref, List<String> taskIds) {
    // Convert taskIds to a comma-separated string for URL
    final taskIdsStr = taskIds.join(',');
    
    context.push(
      '/dashboard/insights/merge-tasks?taskIds=$taskIdsStr&insightId=${insight.id}',
    );
  }
  
  // Handle revert AI correction
  void _handleRevertCorrection(WidgetRef ref, String taskId, String originalText) {
    ref.read(insightProvider.notifier).revertAiCorrection(taskId, originalText, insight.id);
  }
  
  // Handle confirm AI interpretation
  void _handleConfirmInterpretation(WidgetRef ref, String taskId) {
    ref.read(insightProvider.notifier).confirmAiInterpretation(taskId, insight.id);
  }
  
  // Handle dismiss AI interpretation
  void _handleDismissInterpretation(WidgetRef ref, String taskId) {
    ref.read(insightProvider.notifier).dismissAiInterpretation(taskId, insight.id);
  }

  // Build related context based on insight type
  Widget _buildRelatedContext(BuildContext context, ThemeData theme) {
    switch (insight.type) {
      case InsightType.priorityCheck:
        final currentPriority = insight.relatedData['currentPriority'] as String? ?? 'Unknown';
        final suggestedPriority = insight.relatedData['suggestedPriority'] as String? ?? 'Unknown';
        final taskTitle = insight.relatedData['taskTitle'] as String? ?? 'Task';
        
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Task: $taskTitle', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text('Current: ', style: theme.textTheme.bodySmall),
                  _buildPriorityBadge(currentPriority, theme),
                  const SizedBox(width: 16),
                  Text('Suggested: ', style: theme.textTheme.bodySmall),
                  _buildPriorityBadge(suggestedPriority, theme),
                ],
              ),
            ],
          ),
        );
        
      case InsightType.overdueTaskNotification:
        final taskTitle = insight.relatedData['taskTitle'] as String? ?? 'Task';
        final deadline = insight.relatedData['deadline'] as String?;
        
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Task: $taskTitle', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
              if (deadline != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(LucideIcons.calendar, size: 14, color: theme.colorScheme.error),
                    const SizedBox(width: 4),
                    Text(
                      'Due: ${DateFormat.yMd().format(DateTime.parse(deadline))}',
                      style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.error),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
        
      case InsightType.duplicateTaskDetected:
      case InsightType.taskMergeSuggestion:
        final taskIds = (insight.relatedData['taskIds'] as List<dynamic>?)?.cast<String>() ?? [];
        final taskTitles = (insight.relatedData['taskTitles'] as List<dynamic>?)?.cast<String>() ?? [];
        
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Similar Tasks:', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              ...List.generate(
                taskTitles.length.clamp(0, 3), // Show max 3 tasks
                (index) => Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Row(
                    children: [
                      Icon(LucideIcons.checkSquare, size: 14, color: theme.colorScheme.primary.withOpacity(0.7)),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          taskTitles[index],
                          style: theme.textTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (taskTitles.length > 3)
                Text('+ ${taskTitles.length - 3} more...', style: theme.textTheme.bodySmall?.copyWith(fontStyle: FontStyle.italic)),
            ],
          ),
        );
        
      case InsightType.newCategorySuggestion:
        final suggestedName = insight.relatedData['suggestedCategoryName'] as String? ?? 'New Category';
        
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Row(
            children: [
              Icon(LucideIcons.tag, size: 14, color: theme.colorScheme.primary),
              const SizedBox(width: 4),
              Text(
                'Suggested Name: $suggestedName',
                style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        );
        
      case InsightType.aiAutocorrectionReview:
        final taskTitle = insight.relatedData['taskTitle'] as String? ?? 'Task';
        final originalText = insight.relatedData['originalText'] as String? ?? '';
        final correctedText = insight.relatedData['correctedText'] as String? ?? '';
        
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Task: $taskTitle', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('Original Text:', style: theme.textTheme.bodySmall),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(top: 4, bottom: 8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  originalText,
                  style: theme.textTheme.bodyMedium,
                ),
              ),
              Text('Corrected Text:', style: theme.textTheme.bodySmall),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(top: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  correctedText,
                  style: theme.textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
        
      case InsightType.confirmAiInterpretation:
        final taskTitle = insight.relatedData['taskTitle'] as String? ?? 'Task';
        final interpretation = insight.relatedData['interpretation'] as String? ?? '';
        
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Task: $taskTitle', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('AI Interpretation:', style: theme.textTheme.bodySmall),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(top: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  interpretation,
                  style: theme.textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
        
      default:
        return const SizedBox.shrink();
    }
  }

  // Build specific action buttons based on insight type
  List<Widget> _buildSpecificActionButtons(BuildContext context, ThemeData theme, WidgetRef ref) {
    switch (insight.type) {
      case InsightType.overdueTaskNotification:
        final taskId = insight.relatedData['targetTaskId'] as String?;
        if (taskId != null) {
          return [
            TextButton.icon(
              icon: const Icon(LucideIcons.info, size: 16),
              label: const Text('View Task'),
              onPressed: isLoading 
                ? null 
                : () => _navigateToTaskDetail(context, taskId),
            ),
            // New actions for overdue tasks
            TextButton.icon(
              icon: const Icon(LucideIcons.check, size: 16),
              label: const Text('Mark Complete'),
              onPressed: isLoading 
                ? null 
                : () => _handleMarkComplete(ref, taskId),
            ),
            TextButton.icon(
              icon: const Icon(LucideIcons.calendar, size: 16),
              label: const Text('Change Deadline'),
              onPressed: isLoading 
                ? null 
                : () => _handleChangeDeadline(context, ref, taskId),
            ),
            TextButton.icon(
              icon: const Icon(LucideIcons.trash, size: 16),
              label: const Text('Delete Task'),
              onPressed: isLoading 
                ? null 
                : () => _handleDeleteTask(context, ref, taskId),
            ),
          ];
        }
        break;
        
      case InsightType.duplicateTaskDetected:
        final taskIds = (insight.relatedData['taskIds'] as List<dynamic>?)?.cast<String>() ?? [];
        if (taskIds.isNotEmpty) {
          return [
            TextButton.icon(
              icon: const Icon(LucideIcons.info, size: 16),
              label: const Text('View First Task'),
              onPressed: isLoading 
                ? null 
                : () => _navigateToTaskDetail(context, taskIds.first),
            ),
            // New action for duplicate tasks
            TextButton.icon(
              icon: const Icon(LucideIcons.copy, size: 16),
              label: const Text('Review Duplicates'),
              onPressed: isLoading 
                ? null 
                : () => _handleReviewDuplicates(context, ref, taskIds),
            ),
          ];
        }
        break;
      
      case InsightType.taskMergeSuggestion:
        final taskIds = (insight.relatedData['taskIds'] as List<dynamic>?)?.cast<String>() ?? [];
        if (taskIds.isNotEmpty) {
          return [
            TextButton.icon(
              icon: const Icon(LucideIcons.info, size: 16),
              label: const Text('View First Task'),
              onPressed: isLoading 
                ? null 
                : () => _navigateToTaskDetail(context, taskIds.first),
            ),
            // New action for merge task suggestions
            TextButton.icon(
              icon: const Icon(LucideIcons.merge, size: 16),
              label: const Text('Merge Tasks'),
              onPressed: isLoading 
                ? null 
                : () => _handleMergeTasks(context, ref, taskIds),
            ),
          ];
        }
        break;
        
      case InsightType.aiAutocorrectionReview:
        final taskId = insight.relatedData['taskId'] as String?;
        final originalText = insight.relatedData['originalText'] as String?;
        final correctedText = insight.relatedData['correctedText'] as String?;
        
        if (taskId != null && originalText != null) {
          return [
            TextButton.icon(
              icon: const Icon(LucideIcons.info, size: 16),
              label: const Text('View Task'),
              onPressed: isLoading 
                ? null 
                : () => _navigateToTaskDetail(context, taskId),
            ),
            // Action for reverting AI correction
            TextButton.icon(
              icon: const Icon(LucideIcons.undo2, size: 16),
              label: const Text('Revert Correction'),
              onPressed: isLoading 
                ? null 
                : () => _handleRevertCorrection(ref, taskId, originalText),
            ),
          ];
        }
        break;
        
      case InsightType.confirmAiInterpretation:
        final taskId = insight.relatedData['taskId'] as String?;
        
        if (taskId != null) {
          return [
            TextButton.icon(
              icon: const Icon(LucideIcons.info, size: 16),
              label: const Text('View Task'),
              onPressed: isLoading 
                ? null 
                : () => _navigateToTaskDetail(context, taskId),
            ),
            TextButton.icon(
              icon: const Icon(LucideIcons.check, size: 16),
              label: const Text('Confirm'),
              onPressed: isLoading 
                ? null 
                : () => _handleConfirmInterpretation(ref, taskId),
            ),
            TextButton.icon(
              icon: const Icon(LucideIcons.x, size: 16),
              label: const Text('Reject'),
              onPressed: isLoading 
                ? null 
                : () => _handleDismissInterpretation(ref, taskId),
            ),
          ];
        }
        break;
        
      default:
        return [];
    }
    
    return [];
  }

  // Build a priority badge
  Widget _buildPriorityBadge(String priority, ThemeData theme) {
    Color color;
    switch (priority.toLowerCase()) {
      case 'high':
      case 'critical':
        color = Colors.red;
        break;
      case 'medium':
        color = Colors.orange;
        break;
      case 'low':
        color = Colors.green;
        break;
      default:
        color = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        priority,
        style: theme.textTheme.bodySmall?.copyWith(color: color),
      ),
    );
  }

  // Get icon for insight type
  IconData _getInsightIcon(InsightType type) {
    switch (type) {
      case InsightType.newCategorySuggestion:
        return LucideIcons.tag;
      case InsightType.priorityCheck:
        return LucideIcons.alertTriangle;
      case InsightType.recurringTaskSuggestion:
        return LucideIcons.repeat;
      case InsightType.categoryCleanup:
        return LucideIcons.folderArchive;
      case InsightType.overdueTaskNotification:
        return LucideIcons.clock;
      case InsightType.duplicateTaskDetected:
        return LucideIcons.copy;
      case InsightType.taskMergeSuggestion:
        return LucideIcons.merge;
      case InsightType.aiAutocorrectionReview:
        return LucideIcons.edit3;
      case InsightType.confirmAiInterpretation:
        return LucideIcons.brainCircuit;
      default:
        return LucideIcons.lightbulb;
    }
  }

  // Get title for insight type
  String _getTitle(InsightType type) {
    switch (type) {
      case InsightType.newCategorySuggestion:
        return 'New Category Suggestion';
      case InsightType.priorityCheck:
        return 'Priority Suggestion';
      case InsightType.recurringTaskSuggestion:
        return 'Recurring Task Suggestion';
      case InsightType.categoryCleanup:
        return 'Category Cleanup';
      case InsightType.overdueTaskNotification:
        return 'Overdue Task';
      case InsightType.duplicateTaskDetected:
        return 'Potential Duplicates';
      case InsightType.taskMergeSuggestion:
        return 'Merge Suggestion';
      case InsightType.aiAutocorrectionReview:
        return 'AI Autocorrection Review';
      case InsightType.confirmAiInterpretation:
        return 'Confirm AI Interpretation';
      default:
        return 'AI Insight';
    }
  }

  // Get background color for insight type
  Color _getBackgroundColor(InsightType type, ThemeData theme) {
    switch (type) {
      case InsightType.priorityCheck:
      case InsightType.overdueTaskNotification:
        return theme.colorScheme.errorContainer.withOpacity(0.1);
      case InsightType.newCategorySuggestion:
      case InsightType.categoryCleanup:
        return theme.colorScheme.primaryContainer.withOpacity(0.1);
      case InsightType.duplicateTaskDetected:
      case InsightType.taskMergeSuggestion:
        return theme.colorScheme.secondaryContainer.withOpacity(0.1);
      case InsightType.aiAutocorrectionReview:
        return theme.colorScheme.tertiaryContainer.withOpacity(0.1);
      case InsightType.confirmAiInterpretation:
        return theme.colorScheme.surfaceContainerHighest.withOpacity(0.2);
      default:
        return theme.cardColor;
    }
  }

  // Get border color for insight type
  Color _getBorderColor(InsightType type, ThemeData theme) {
    switch (type) {
      case InsightType.priorityCheck:
      case InsightType.overdueTaskNotification:
        return theme.colorScheme.error.withOpacity(0.3);
      case InsightType.newCategorySuggestion:
      case InsightType.categoryCleanup:
        return theme.colorScheme.primary.withOpacity(0.3);
      case InsightType.duplicateTaskDetected:
      case InsightType.taskMergeSuggestion:
        return theme.colorScheme.secondary.withOpacity(0.3);
      case InsightType.aiAutocorrectionReview:
        return theme.colorScheme.tertiary.withOpacity(0.3);
      case InsightType.confirmAiInterpretation:
        return theme.colorScheme.outline.withOpacity(0.3);
      default:
        return theme.dividerColor;
    }
  }

  // Get icon color for insight type
  Color _getIconColor(InsightType type, ThemeData theme) {
    switch (type) {
      case InsightType.priorityCheck:
      case InsightType.overdueTaskNotification:
        return theme.colorScheme.error;
      case InsightType.newCategorySuggestion:
      case InsightType.categoryCleanup:
        return theme.colorScheme.primary;
      case InsightType.duplicateTaskDetected:
      case InsightType.taskMergeSuggestion:
        return theme.colorScheme.secondary;
      case InsightType.aiAutocorrectionReview:
        return theme.colorScheme.tertiary;
      case InsightType.confirmAiInterpretation:
        return Colors.purple;
      default:
        return theme.colorScheme.primary;
    }
  }
}
