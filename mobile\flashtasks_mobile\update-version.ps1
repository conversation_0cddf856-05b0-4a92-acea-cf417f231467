param(
    [string]$version = "",
    [switch]$major,
    [switch]$minor,
    [switch]$patch,
    [switch]$build,
    [switch]$show,
    [switch]$help
)

function Show-Help {
    Write-Output "FlashTasks Version Management Tool"
    Write-Output "Usage: .\update-version.ps1 [options]"
    Write-Output ""
    Write-Output "Options:"
    Write-Output "  -version <version>    Set specific version (e.g., '1.2.3' or '1.2.3+4')"
    Write-Output '  -major                Increment major version (1.0.0 -> 2.0.0)'
    Write-Output '  -minor                Increment minor version (1.0.0 -> 1.1.0)'
    Write-Output '  -patch                Increment patch version (1.0.0 -> 1.0.1)'
    Write-Output '  -build                Increment build number (1.0.0+1 -> 1.0.0+2)'
    Write-Output "  -show                 Show current version information"
    Write-Output "  -help                 Show this help message"
    Write-Output ""
    Write-Output "Examples:"
    Write-Output "  .\update-version.ps1 -show"
    Write-Output "  .\update-version.ps1 -patch"
    Write-Output "  .\update-version.ps1 -version 2.1.0"
    Write-Output "  .\update-version.ps1 -version 2.1.0+5"
}

function Get-CurrentVersion {
    $pubspec = Get-Content -Path "$PSScriptRoot\pubspec.yaml" -Raw
    $versionMatch = [regex]::Match($pubspec, 'version:\s*(\d+\.\d+\.\d+\+\d+)')

    if ($versionMatch.Success) {
        $versionParts = $versionMatch.Groups[1].Value -split '\+'
        return @{
            FullVersion = $versionMatch.Groups[1].Value
            Version = $versionParts[0]
            Build = $versionParts[1]
        }
    }

    return $null
}

function Update-Version($newVersion, $newBuild) {
    $pubspecPath = "$PSScriptRoot\pubspec.yaml"
    $pubspec = Get-Content -Path $pubspecPath -Raw

    # Update pubspec.yaml
    $newVersionString = "$newVersion+$newBuild"
    $pubspec = $pubspec -replace 'version:.*', "version: $newVersionString"
    Set-Content -Path $pubspecPath -Value $pubspec

    # Update Android build.gradle
    $buildGradlePath = "$PSScriptRoot\android\app\build.gradle"
    if (Test-Path $buildGradlePath) {
        $buildGradle = Get-Content -Path $buildGradlePath -Raw
        $versionParts = $newVersion -split '\.'
        $versionCode = $newBuild
        $versionName = $newVersion

        $buildGradle = $buildGradle -replace 'versionCode\s+\d+', "versionCode $versionCode"
        $buildGradle = $buildGradle -replace 'versionName\s+"[^"]+"', "versionName `"$versionName`""

        Set-Content -Path $buildGradlePath -Value $buildGradle -NoNewline
    }

    Write-Output "✓ Version updated to $newVersionString"
    Write-Output "  Version: $newVersion"
    Write-Output "  Build:   $newBuild"
}

# Show help if requested
if ($help) {
    Show-Help
    exit 0
}

# Get current version
$current = Get-CurrentVersion

if (-not $current) {
    Write-Error "Could not determine current version from pubspec.yaml"
    exit 1
}

# Show current version if no operation specified or if -show flag is used
if (($PSBoundParameters.Count -eq 0) -or $show) {
    Write-Output "Current version: $($current.FullVersion)"
    Write-Output "  Version: $($current.Version)"
    Write-Output "  Build:   $($current.Build)"
    exit 0
}

# Process version updates
$versionParts = $current.Version -split '\.'
$majorVer = [int]$versionParts[0]
$minorVer = [int]$versionParts[1]
$patchVer = [int]$versionParts[2]
$buildNum = [int]$current.Build

if ($version) {
    # Parse version string (can be either '1.2.3' or '1.2.3+4')
    if ($version -match '^(\d+\.\d+\.\d+)(\+\d+)?$') {
        $newVersion = $matches[1]
        if ($matches[2]) {
            $buildNum = [int]$matches[2].Substring(1)
        } else {
            $buildNum++
        }
        Update-Version -newVersion $newVersion -newBuild $buildNum
    } else {
        Write-Error "Invalid version format. Use '1.2.3' or '1.2.3+4'"
        exit 1
    }
}
elseif ($major) {
    $majorVer++
    $minorVer = 0
    $patchVer = 0
    $buildNum++
    $newVersionStr = "$majorVer.$minorVer.$patchVer"
    Update-Version -newVersion $newVersionStr -newBuild $buildNum
}
elseif ($minor) {
    $minorVer++
    $patchVer = 0
    $buildNum++
    Update-Version -newVersion "$majorVer.$minorVer.$patchVer" -newBuild $buildNum
}
elseif ($patch) {
    $patchVer++
    $buildNum++
    Update-Version -newVersion "$majorVer.$minorVer.$patchVer" -newBuild $buildNum
}
elseif ($build) {
    $buildNum++
    Update-Version -newVersion "$majorVer.$minorVer.$patchVer" -newBuild $buildNum
}
else {
    Show-Help
    exit 1
}
