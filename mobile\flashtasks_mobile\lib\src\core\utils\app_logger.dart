import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Global logger instance for the FlashTasks app
/// Configured to show appropriate log levels based on build mode
class AppLogger {
  static final Logger _logger = Logger(
    filter: _AppLogFilter(),
    printer: PrettyPrinter(
      methodCount: 2, // Number of method calls to be displayed
      errorMethodCount: 8, // Number of method calls if stacktrace is provided
      lineLength: 120, // Width of the output
      colors: true, // Colorful log messages
      printEmojis: true, // Print an emoji for each log message
      printTime: false, // Should each log print contain a timestamp
    ),
  );

  /// Debug level logging - only shown in debug mode
  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Info level logging - shown in debug and profile modes
  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Warning level logging - shown in all modes
  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Error level logging - shown in all modes
  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Verbose level logging - only shown in debug mode
  static void v(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.t(message, error: error, stackTrace: stackTrace);
  }
}

/// Custom log filter that respects build modes
class _AppLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    if (kReleaseMode) {
      // In release mode, only show warnings and errors
      return event.level.index >= Level.warning.index;
    } else if (kProfileMode) {
      // In profile mode, show info, warnings, and errors
      return event.level.index >= Level.info.index;
    } else {
      // In debug mode, show all logs
      return true;
    }
  }
}
