import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/grocery_provider.dart';
import '../models/grocery_list.dart';
import '../models/grocery_invitation.dart';

/// Panel for managing grocery list collaboration
class CollaborationPanel extends ConsumerStatefulWidget {
  final ScrollController scrollController;

  const CollaborationPanel({
    super.key,
    required this.scrollController,
  });

  @override
  ConsumerState<CollaborationPanel> createState() => _CollaborationPanelState();
}

class _CollaborationPanelState extends ConsumerState<CollaborationPanel>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _emailController = TextEditingController();
  CollaboratorRole _selectedRole = CollaboratorRole.editor;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final groceryState = ref.watch(groceryProvider);

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                const Icon(Icons.people),
                const SizedBox(width: 8),
                const Text(
                  'Collaboration',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: [
              Tab(
                text: 'Invitations',
                icon: Stack(
                  children: [
                    const Icon(Icons.mail_outline),
                    if (groceryState.pendingInvitations.isNotEmpty)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 12,
                            minHeight: 12,
                          ),
                          child: Text(
                            '${groceryState.pendingInvitations.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const Tab(text: 'Manage', icon: Icon(Icons.settings)),
              const Tab(text: 'Share', icon: Icon(Icons.share)),
            ],
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildInvitationsTab(context, groceryState),
                _buildManageTab(context, groceryState),
                _buildShareTab(context, groceryState),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationsTab(BuildContext context, GroceryState state) {
    if (state.isLoadingInvitations) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.pendingInvitations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.mail_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No Pending Invitations',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'You don\'t have any pending grocery list invitations.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: widget.scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.pendingInvitations.length,
      itemBuilder: (context, index) {
        final invitation = state.pendingInvitations[index];
        return _buildInvitationCard(context, invitation);
      },
    );
  }

  Widget _buildInvitationCard(BuildContext context, GroceryListInvitation invitation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Text(
                    invitation.inviterUserId.name.isNotEmpty
                        ? invitation.inviterUserId.name[0].toUpperCase()
                        : '?',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitation.groceryList?.name ?? 'Grocery List',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        'Invited by ${invitation.inviterUserId.name}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        'Role: ${invitation.role.name.toUpperCase()}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineInvitation(invitation.token),
                    child: const Text('Decline'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptInvitation(invitation.token),
                    child: const Text('Accept'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManageTab(BuildContext context, GroceryState state) {
    return SingleChildScrollView(
      controller: widget.scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'List Settings',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Enable/Disable sharing
          Card(
            child: SwitchListTile(
              title: const Text('Enable Sharing'),
              subtitle: const Text('Allow others to collaborate on your grocery list'),
              value: state.groceryList?.isShared ?? false,
              onChanged: (value) => _toggleSharing(value),
            ),
          ),
          
          if (state.groceryList?.isShared == true) ...[
            const SizedBox(height: 16),
            Text(
              'Collaborators',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            // Collaborators list
            if (state.groceryList?.collaborators.isNotEmpty == true)
              ...state.groceryList!.collaborators.map((collaborator) {
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      child: Text(collaborator.userId.name[0].toUpperCase()),
                    ),
                    title: Text(collaborator.userId.name),
                    subtitle: Text(collaborator.userId.email),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Chip(
                          label: Text(collaborator.role.name.toUpperCase()),
                          backgroundColor: _getRoleColor(collaborator.role),
                        ),
                        IconButton(
                          icon: const Icon(Icons.remove_circle_outline),
                          onPressed: () => _removeCollaborator(collaborator.userId.id),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList()
            else
              const Card(
                child: ListTile(
                  leading: Icon(Icons.people_outline),
                  title: Text('No collaborators yet'),
                  subtitle: Text('Invite people to collaborate on your list'),
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildShareTab(BuildContext context, GroceryState state) {
    return SingleChildScrollView(
      controller: widget.scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Invite Collaborators',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Email input
          TextField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'Email Address',
              hintText: 'Enter collaborator\'s email',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.email),
            ),
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),
          
          // Role selection
          DropdownButtonFormField<CollaboratorRole>(
            value: _selectedRole,
            decoration: const InputDecoration(
              labelText: 'Role',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.person),
            ),
            items: CollaboratorRole.values.map((role) {
              return DropdownMenuItem(
                value: role,
                child: Text(role.name.toUpperCase()),
              );
            }).toList(),
            onChanged: (role) {
              if (role != null) {
                setState(() {
                  _selectedRole = role;
                });
              }
            },
          ),
          const SizedBox(height: 24),
          
          // Send invitation button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _emailController.text.isNotEmpty ? _sendInvitation : null,
              child: const Text('Send Invitation'),
            ),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(CollaboratorRole role) {
    switch (role) {
      case CollaboratorRole.editor:
        return Colors.blue;
      case CollaboratorRole.viewer:
        return Colors.grey;
    }
  }

  void _acceptInvitation(String token) async {
    try {
      await ref.read(groceryProvider.notifier).acceptInvitation(token);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invitation accepted!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _declineInvitation(String token) async {
    try {
      await ref.read(groceryProvider.notifier).declineInvitation(token);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invitation declined')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _toggleSharing(bool enabled) async {
    try {
      if (enabled) {
        final shareSettings = GroceryShareSettings(
          allowCollaboratorInvites: true,
          requireApprovalForEdits: false,
          notifyOnChanges: true,
        );
        await ref.read(groceryProvider.notifier).enableSharing(shareSettings);
      } else {
        await ref.read(groceryProvider.notifier).disableSharing();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _sendInvitation() async {
    if (_emailController.text.trim().isEmpty) return;

    try {
      await ref.read(groceryProvider.notifier).addCollaborator(
        _emailController.text.trim(),
        _selectedRole,
      );
      _emailController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invitation sent!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _removeCollaborator(String collaboratorId) async {
    try {
      await ref.read(groceryProvider.notifier).removeCollaborator(collaboratorId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Collaborator removed')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }
}
