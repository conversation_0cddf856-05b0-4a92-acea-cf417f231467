import React from 'react';
import { Filter } from 'lucide-react';

interface FilterPanelProps {
  filters: {
    categories: string[];
    priority: string;
    status: string;
  };
  onFiltersChange: (filters: {
    categories: string[];
    priority: string;
    status: string;
  }) => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({ filters, onFiltersChange }) => {
  // Mock categories - replace with actual categories from your system
  const availableCategories = ['Work', 'Personal', 'Shopping', 'Health'];
  const priorities = ['all', 'high', 'medium', 'low'];
  const statuses = ['all', 'pending', 'completed'];

  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];

    onFiltersChange({
      ...filters,
      categories: newCategories,
    });
  };

  return (
    <div className="p-4 border-t border-gray-200">
      <div className="flex items-center mb-4">
        <Filter className="w-4 h-4 mr-2" />
        <h3 className="text-sm font-semibold text-gray-900">Filters</h3>
      </div>

      {/* Categories */}
      <div className="mb-4">
        <h4 className="text-xs font-medium text-gray-500 mb-2">Categories</h4>
        <div className="space-y-2">
          {availableCategories.map(category => (
            <label
              key={category}
              className="flex items-center text-sm text-gray-700"
            >
              <input
                type="checkbox"
                checked={filters.categories.includes(category)}
                onChange={() => handleCategoryToggle(category)}
                className="rounded border-gray-300 text-blue-600 mr-2"
              />
              {category}
            </label>
          ))}
        </div>
      </div>

      {/* Priority */}
      <div className="mb-4">
        <h4 className="text-xs font-medium text-gray-500 mb-2">Priority</h4>
        <select
          value={filters.priority}
          onChange={e =>
            onFiltersChange({ ...filters, priority: e.target.value })
          }
          className="w-full text-sm border-gray-300 rounded-md"
        >
          {priorities.map(priority => (
            <option key={priority} value={priority}>
              {priority.charAt(0).toUpperCase() + priority.slice(1)}
            </option>
          ))}
        </select>
      </div>

      {/* Status */}
      <div className="mb-4">
        <h4 className="text-xs font-medium text-gray-500 mb-2">Status</h4>
        <select
          value={filters.status}
          onChange={e => onFiltersChange({ ...filters, status: e.target.value })}
          className="w-full text-sm border-gray-300 rounded-md"
        >
          {statuses.map(status => (
            <option key={status} value={status}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </option>
          ))}
        </select>
      </div>

      {/* Clear filters */}
      <button
        onClick={() =>
          onFiltersChange({
            categories: [],
            priority: 'all',
            status: 'all',
          })
        }
        className="w-full text-sm text-blue-600 hover:text-blue-800"
      >
        Clear all filters
      </button>
    </div>
  );
};

export default FilterPanel;
