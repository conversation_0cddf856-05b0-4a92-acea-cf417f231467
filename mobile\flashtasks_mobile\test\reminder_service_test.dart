import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flashtasks_mobile/src/core/services/reminder_service.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

// Mock for FlutterLocalNotificationsPlugin
class MockFlutterLocalNotificationsPlugin extends Mo<PERSON> implements FlutterLocalNotificationsPlugin {
  @override
  Future<bool?> initialize(
    InitializationSettings initializationSettings, {
    DidReceiveNotificationResponseCallback? onDidReceiveNotificationResponse,
    DidReceiveBackgroundNotificationResponseCallback? onDidReceiveBackgroundNotificationResponse,
  }) async => true;
  
  @override
  Future<void> zonedSchedule(
    int id,
    String? title,
    String? body,
    tz.TZDateTime scheduledDate,
    NotificationDetails notificationDetails, {
    bool androidAllowWhileIdle = false,
    AndroidScheduleMode? androidScheduleMode,
    String? payload,
    DateTimeComponents? matchDateTimeComponents,
    required UILocalNotificationDateInterpretation uiLocalNotificationDateInterpretation,
  }) async {}
  
  @override
  Future<void> cancel(int id, {String? tag}) async {}
  
  @override
  Future<void> cancelAll() async {}
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  // Initialize timezone data
  tz_data.initializeTimeZones();
  
  late ReminderService reminderService;
  late MockFlutterLocalNotificationsPlugin mockLocalNotifications;
  
  setUp(() {
    // Create a test instance by accessing the singleton
    reminderService = ReminderService();
    
    // You may need to add a way to inject mocks in ReminderService for better testability
    // For now, test the behavior without mocking internal dependencies
  });
  
  group('ReminderService', () {
    testWidgets('initialize completes successfully', (WidgetTester tester) async {
      // Act & Assert
      await expectLater(reminderService.initialize(), completes);
    });
    
    testWidgets('scheduleReminder returns a valid reminder ID', (WidgetTester tester) async {
      // Arrange - initialize the service first
      await reminderService.initialize();
      
      // Act
      final reminderId = await reminderService.scheduleReminder(
        title: 'Test Reminder',
        body: 'This is a test reminder',
        scheduledDate: DateTime.now().add(const Duration(minutes: 30)),
        taskId: 'test-task-id',
      );
      
      // Assert
      expect(reminderId, isA<int>());
      expect(reminderId, isNotNull);
    });
    
    testWidgets('cancelReminder completes without errors', (WidgetTester tester) async {
      // Arrange - initialize the service first
      await reminderService.initialize();
      
      // Act & Assert
      await expectLater(reminderService.cancelReminder(123), completes);
    });
    
    testWidgets('cancelAllReminders completes without errors', (WidgetTester tester) async {
      // Arrange - initialize the service first
      await reminderService.initialize();
      
      // Act & Assert
      await expectLater(reminderService.cancelAllReminders(), completes);
    });
    
    testWidgets('checkPermissions returns a boolean value', (WidgetTester tester) async {
      // Arrange - initialize the service first
      await reminderService.initialize();
      
      // Act
      final result = await reminderService.checkPermissions();
      
      // Assert - in test environment, this might always return false
      expect(result, isA<bool>());
    });
  });
} 