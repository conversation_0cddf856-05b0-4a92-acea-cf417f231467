// mobile/flashtasks_mobile/lib/src/features/settings/screens/keytag_mapping_settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../providers/settings_providers.dart';
import '../models/keytag_mapping_settings.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/error_display.dart';
import '../../categories/providers/category_provider.dart';

/// Screen for managing keytag mapping settings
class KeytagMappingSettingsScreen extends ConsumerStatefulWidget {
  const KeytagMappingSettingsScreen({super.key});

  @override
  ConsumerState<KeytagMappingSettingsScreen> createState() => _KeytagMappingSettingsScreenState();
}

class _KeytagMappingSettingsScreenState extends ConsumerState<KeytagMappingSettingsScreen> {
  final _newKeywordController = TextEditingController();
  String? _newSelectedCategoryId;

  @override
  void dispose() {
    _newKeywordController.dispose();
    super.dispose();
  }

  /// Add a new keytag mapping
  void _addMapping(KeytagMappingSettings currentSettings) {
    final keyword = _newKeywordController.text.trim();
    if (keyword.isEmpty || _newSelectedCategoryId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Keyword and category are required.')),
      );
      return;
    }

    final newExactMatches = Map<String, String>.from(currentSettings.exactMatches);
    newExactMatches[keyword.toLowerCase()] = _newSelectedCategoryId!;

    final updatedSettings = currentSettings.copyWith(exactMatches: newExactMatches);
    ref.read(userSettingsProvider.notifier).updateKeytagMappingSettings(updatedSettings);

    _newKeywordController.clear();
    setState(() {
      _newSelectedCategoryId = null;
    });
    FocusScope.of(context).unfocus();
  }

  /// Remove a keytag mapping
  void _removeMapping(KeytagMappingSettings currentSettings, String keyword) {
    final newExactMatches = Map<String, String>.from(currentSettings.exactMatches);
    newExactMatches.remove(keyword);

    final updatedSettings = currentSettings.copyWith(exactMatches: newExactMatches);
    ref.read(userSettingsProvider.notifier).updateKeytagMappingSettings(updatedSettings);
  }

  @override
  Widget build(BuildContext context) {
    final settingsState = ref.watch(userSettingsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);
    final theme = Theme.of(context);
    final formKey = GlobalKey<FormState>();

    // Show loading indicator if loading and no settings available yet
    if (settingsState.isLoading && settingsState.settings == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Keytag Mappings')),
        body: const Center(
          child: LoadingIndicator(),
        ),
      );
    }

    // Show error if there's an error and no settings available
    if (settingsState.error != null && settingsState.settings == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Keytag Mappings')),
        body: Center(
          child: ErrorDisplay(
            message: 'Failed to load settings: ${settingsState.error}',
            onRetry: () => ref.read(userSettingsProvider.notifier).refreshSettings(),
          ),
        ),
      );
    }

    // Get keytag mapping settings from state
    final keytagSettings = settingsState.settings?.keytagMapping ??
        KeytagMappingSettings(
          exactMatches: {},
          enableFuzzyMatching: true,
          fuzzyMatchThreshold: 0.7,
          excludedKeywords: [],
          applyToManualTasks: false,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Keytag Mappings'),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Info banner
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.info,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Automatically assign categories to tasks based on keywords found in their title or description.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Add new mapping
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Add New Mapping',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Icon(
                          LucideIcons.plus,
                          size: 18,
                          color: theme.colorScheme.primary,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _newKeywordController,
                      decoration: InputDecoration(
                        labelText: 'Keyword (e.g., "report", "gym")',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surface,
                        prefixIcon: const Icon(LucideIcons.tag),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a keyword';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 12),
                    categoriesAsync.when(
                      data: (categories) => DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Assign to Category',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                          prefixIcon: const Icon(LucideIcons.folder),
                        ),
                        value: _newSelectedCategoryId,
                        items: categories.map((cat) => DropdownMenuItem(
                          value: cat.id,
                          child: Text(cat.name),
                        )).toList(),
                        onChanged: (value) => setState(() => _newSelectedCategoryId = value),
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a category';
                          }
                          return null;
                        },
                      ),
                      loading: () => const Center(child: CircularProgressIndicator()),
                      error: (err, stack) => Text('Error loading categories: $err'),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        icon: const Icon(LucideIcons.plus),
                        label: const Text('Add Mapping'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () {
                          if (formKey.currentState?.validate() ?? false) {
                            _addMapping(keytagSettings);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Existing mappings
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Keyword Mappings',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    keytagSettings.exactMatches.isEmpty
                        ? Center(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                children: [
                                  Icon(
                                    LucideIcons.tag,
                                    size: 48,
                                    color: theme.colorScheme.onSurface.withOpacity(0.2),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'No mappings yet',
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                                    ),
                                  ),
                                  Text(
                                    'Add your first one above',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurface.withOpacity(0.4),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: keytagSettings.exactMatches.length,
                            itemBuilder: (context, index) {
                              final keyword = keytagSettings.exactMatches.keys.elementAt(index);
                              final categoryId = keytagSettings.exactMatches[keyword]!;

                              // Find category name
                              String categoryName = "Loading...";
                              if (categoriesAsync.hasValue) {
                                try {
                                  final category = categoriesAsync.value!.firstWhere(
                                    (c) => c.id == categoryId,
                                  );
                                  categoryName = category.name;
                                } catch (_) {
                                  categoryName = "Unknown Category";
                                }
                              }

                              return Container(
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.surface,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                  leading: Icon(
                                    LucideIcons.tag,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                                  title: Text(
                                    keyword,
                                    style: theme.textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  subtitle: Row(
                                    children: [
                                      Icon(
                                        LucideIcons.arrowRight,
                                        size: 14,
                                        color: theme.colorScheme.onSurfaceVariant,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        categoryName,
                                        style: theme.textTheme.bodySmall?.copyWith(
                                          color: theme.colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: Icon(
                                          LucideIcons.trash2,
                                          color: theme.colorScheme.error,
                                          size: 20,
                                        ),
                                        onPressed: () => _removeMapping(keytagSettings, keyword),
                                        tooltip: 'Remove Mapping',
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            separatorBuilder: (context, index) => const Divider(height: 1),
                          ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // General Settings Card
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                    child: Text(
                      'General Settings',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Divider(),
                  // Fuzzy matching toggle
                  SwitchListTile(
                    title: Text(
                      'Enable Fuzzy Matching',
                      style: theme.textTheme.titleSmall,
                    ),
                    subtitle: Text(
                      'Match keywords that are similar but not exact',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    value: keytagSettings.enableFuzzyMatching,
                    activeColor: theme.colorScheme.primary,
                    onChanged: (value) {
                      final updatedSettings = keytagSettings.copyWith(enableFuzzyMatching: value);
                      ref.read(userSettingsProvider.notifier).updateKeytagMappingSettings(updatedSettings);
                    },
                  ),
                  // Fuzzy match threshold slider
                  if (keytagSettings.enableFuzzyMatching)
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Fuzzy Match Threshold',
                                style: theme.textTheme.bodyMedium,
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${(keytagSettings.fuzzyMatchThreshold * 100).round()}%',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Slider(
                            value: keytagSettings.fuzzyMatchThreshold,
                            min: 0.5,
                            max: 0.9,
                            divisions: 8,
                            activeColor: theme.colorScheme.primary,
                            inactiveColor: theme.colorScheme.primary.withOpacity(0.2),
                            label: '${(keytagSettings.fuzzyMatchThreshold * 100).round()}%',
                            onChanged: (value) {
                              final updatedSettings = keytagSettings.copyWith(fuzzyMatchThreshold: value);
                              ref.read(userSettingsProvider.notifier).updateKeytagMappingSettings(updatedSettings);
                            },
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'More Lenient',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              Text(
                                'More Strict',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: theme.colorScheme.outline.withOpacity(0.1),
                              ),
                            ),
                            child: Text(
                              'Higher values require closer matches. Lower values are more lenient but may cause false matches.',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const Divider(),
                  // Apply to manual tasks toggle
                  SwitchListTile(
                    title: Text(
                      'Apply to Manual Tasks',
                      style: theme.textTheme.titleSmall,
                    ),
                    subtitle: Text(
                      'Apply keytag mappings to tasks created manually',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    value: keytagSettings.applyToManualTasks,
                    activeColor: theme.colorScheme.primary,
                    onChanged: (value) {
                      final updatedSettings = keytagSettings.copyWith(applyToManualTasks: value);
                      ref.read(userSettingsProvider.notifier).updateKeytagMappingSettings(updatedSettings);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
