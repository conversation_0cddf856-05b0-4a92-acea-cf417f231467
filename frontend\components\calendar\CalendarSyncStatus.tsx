import React from 'react';
import { SyncResult } from '../../lib/calendar-sync-service';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { Loader2, CheckCircle2, AlertCircle, Clock, CalendarOff } from 'lucide-react';
import { useSyncStatusPolling } from '../../hooks/useSyncStatusPolling';

interface CalendarSyncStatusProps {
  status: SyncResult['status'];
  lastSyncedAt?: string;
  provider?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const CalendarSyncStatus: React.FC<CalendarSyncStatusProps> = ({
  status,
  lastSyncedAt,
  provider = 'Google',
  className = '',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const statusConfig = {
    synced: {
      icon: CheckCircle2,
      color: 'text-green-500',
      tooltip: `Synced with ${provider} Calendar`,
    },
    pending: {
      icon: Loader2,
      color: 'text-yellow-500 animate-spin',
      tooltip: 'Syncing with calendar...',
    },
    failed: {
      icon: AlertCircle,
      color: 'text-red-500',
      tooltip: 'Failed to sync with calendar',
    },
    not_synced: {
      icon: CalendarOff,
      color: 'text-gray-400',
      tooltip: 'Not synced with calendar',
    },
  };

  const { icon: Icon, color, tooltip } = statusConfig[status] || statusConfig.not_synced;

  // Format last synced time if available
  const formattedTime = lastSyncedAt
    ? new Date(lastSyncedAt).toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : null;

  const fullTooltip = formattedTime
    ? `${tooltip}${status === 'synced' ? ` (Last sync: ${formattedTime})` : ''}`
    : tooltip;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`inline-flex items-center ${className}`}>
            <Icon className={`${sizeClasses[size]} ${color}`} />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{fullTooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Hook version for use with useSyncStatusPolling
export const useCalendarSyncStatus = (taskId?: string, initialStatus?: string) => {
  const { syncStatus, isPolling } = useSyncStatusPolling({
    taskId,
    initialStatus,
  });

  return {
    SyncStatus: () => (
      <CalendarSyncStatus
        status={syncStatus.status}
      />
    ),
    syncStatus,
    isPolling,
  };
};
