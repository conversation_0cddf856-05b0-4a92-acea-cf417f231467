{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.5.3\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.2.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_compass", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_compass-0.8.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-17.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.13\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_ios-5.9.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "integration_test", "path": "C:\\\\Flutter\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "pointer_interceptor_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pointer_interceptor_ios-0.10.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\speech_to_text-7.0.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.5.3\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.2.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_compass", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_compass-0.8.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-17.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_android-4.6.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_android-6.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "integration_test", "path": "C:\\\\Flutter\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\speech_to_text-7.0.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.5.3\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.2.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_macos-1.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-17.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_macos-3.1.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.13\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_ios-5.9.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\speech_to_text-7.0.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "flutter_keyboard_visibility_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_linux-1.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications_linux-4.0.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_linux-1.2.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.5.3\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_windows-1.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_windows-3.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_windows-0.2.5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "firebase_auth_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth_web-5.14.2\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.22.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_web-2.0.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_web-1.2.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_web-3.0.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_web-0.12.4+4\\\\", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "pointer_interceptor_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pointer_interceptor_web-0.10.2+1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\speech_to_text-7.0.0\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core"]}, {"name": "flutter_compass", "dependencies": []}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "integration_test", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "pointer_interceptor", "dependencies": ["pointer_interceptor_ios", "pointer_interceptor_web"]}, {"name": "pointer_interceptor_ios", "dependencies": []}, {"name": "pointer_interceptor_web", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "speech_to_text", "dependencies": []}], "date_created": "2025-05-29 09:29:48.844628", "version": "3.29.3", "swift_package_manager_enabled": {"ios": false, "macos": false}}