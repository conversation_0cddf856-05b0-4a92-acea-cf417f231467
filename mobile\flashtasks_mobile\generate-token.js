const { GoogleAuth } = require('google-auth-library'); async function getAccessToken() { try { const auth = new GoogleAuth({ scopes: ['https://www.googleapis.com/auth/firebase.app.distribution'], keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS }); const client = await auth.getClient(); const token = await client.getAccessToken(); console.log(token.token); } catch (error) { console.error('Error getting access token:', error); process.exit(1); } } getAccessToken();
