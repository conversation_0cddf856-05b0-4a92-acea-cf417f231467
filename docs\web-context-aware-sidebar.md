Okay, let's create a detailed implementation plan for the Context-Aware Sidebar. This plan will be structured for clarity and can be fed to an AI IDE agent for development assistance.

**Project Goal: Unified & Context-Aware Sidebar Navigation**

**Context:**
The application currently has multiple sidebars or navigation panels that appear in different sections (Dashboard/Tasks, Settings, Admin). The `CategorySidebar` is the most prominent one, primarily focused on task categories, but it's rendered via a shared `DashboardLayout`, making it appear on pages like Groceries and Locations where its task-centric content isn't fully relevant.

We aim to refactor this into a single, intelligent, persistent sidebar component (`ContextualSidebar`, an evolution of `CategorySidebar`) that dynamically adapts its content and actions based on the main section of the application currently being viewed (Tasks, Groceries, Locations, Settings, Admin). This will be managed through an enhanced `DashboardContext`.

**Benefits:**
*   **Improved User Experience:** Consistent sidebar presence with relevant options for each app section.
*   **Simplified Codebase:** Reduces redundant sidebar logic and promotes a single source of truth for navigation context.
*   **Enhanced Maintainability:** Easier to update and manage sidebar behavior in one place.
*   **Scalability:** Simpler to add new main sections to the app with their own contextual sidebar views.

---

**Implementation Plan**

**Phase 1: Enhance `DashboardContext`**

**Objective:** Modify `DashboardContext` to manage a more generic `activeViewContext` that can represent the current view type (Tasks, Groceries, etc.) and any specific filters within that view.

**File:** `frontend/lib/dashboard-context.tsx`

**Steps:**

1.  **Define `ActiveViewContext` Interface:**
    ```typescript
    // frontend/lib/dashboard-context.tsx
    export type MainViewType = 'tasks' | 'groceries' | 'locations' | 'settings' | 'admin' | 'profile' | 'account' | 'notifications' | 'datetime' | 'keytags' | 'task_locations' | 'appearance' | 'general_settings'; // Added settings sub-pages
    export type TaskSpecialView = 'all' | 'completed' | 'uncategorized' | null;

    export interface ActiveViewContext {
      type: MainViewType;          // Type of main view (e.g., 'tasks', 'groceries')
      filterId?: string | null;      // ID for specific filter (e.g., task categoryId, grocery listId)
      filterName?: string;       // Display name for the current filter
      specialView?: TaskSpecialView; // For task-specific views like 'completed'
    }
    ```

2.  **Update `DashboardContextProps` Interface:**
    *   Replace `filters: TaskFilters` and `setFilters: Dispatch<SetStateAction<TaskFilters>>` with:
        ```typescript
        // frontend/lib/dashboard-context.tsx
        activeViewContext: ActiveViewContext;
        setActiveViewContext: (newContext: Partial<ActiveViewContext>) => void; // Allow partial updates
        ```
    *   The existing `categories`, `isLoadingCategories`, `completedTaskCount`, `uncategorizedTaskCount`, `categoriesMap`, `sort`, `setSort`, `refreshKey`, `triggerRefresh`, `handleTaskAddedOrUpdated`, `focusedLocationId`, `focusedCoordinates`, `setFocusedLocation`, `isNewTaskDialogOpen`, `newTaskDefaultValues`, `openNewTaskDialog`, `closeNewTaskDialog` can remain as they are useful across different views or for specific actions.
    *   Remove `handleShowUncategorized` from props if its logic will be handled by `setActiveViewContext`.

3.  **Update `DashboardProvider` State:**
    *   Initialize `activeViewContext` state:
        ```typescript
        // frontend/lib/dashboard-context.tsx
        const [activeViewContext, setActiveViewContextInternal] = useState<ActiveViewContext>({
          type: 'tasks', // Default view
          filterId: null,
          specialView: 'incomplete', // Default to incomplete tasks
        });
        ```
    *   Implement the `setActiveViewContext` setter function:
        ```typescript
        // frontend/lib/dashboard-context.tsx
        const setActiveViewContext = useCallback((newContextUpdate: Partial<ActiveViewContext>) => {
          setActiveViewContextInternal(prevContext => {
            const updatedContext = { ...prevContext, ...newContextUpdate };

            // Logic to ensure consistency, e.g., if filterId is set, clear specialView for tasks
            if (newContextUpdate.filterId !== undefined && updatedContext.type === 'tasks') {
              updatedContext.specialView = null;
            }
            if (newContextUpdate.specialView !== undefined && updatedContext.type === 'tasks') {
              updatedContext.filterId = null;
            }
            // If type changes, reset filterId and specialView unless they are part of the newContextUpdate
            if (newContextUpdate.type && newContextUpdate.type !== prevContext.type) {
                updatedContext.filterId = newContextUpdate.filterId === undefined ? null : newContextUpdate.filterId;
                updatedContext.specialView = newContextUpdate.specialView === undefined ? null : newContextUpdate.specialView;
            }

            console.log('[DashboardContext] ActiveViewContext updated:', updatedContext);
            return updatedContext;
          });
        }, []);
        ```

4.  **Update `fetchDashboardData` (if it fetches task counts):**
    *   The existing logic for `completedTaskCount` and `uncategorizedTaskCount` based on `taskService.getTasks({ status: 'all' })` is fine. Ensure these counts are correctly updated and provided in the context value.

5.  **Update Context Value:**
    *   Provide `activeViewContext` and `setActiveViewContext` in the `value` object of `DashboardContext.Provider`.
    *   Remove old `filters` and `setFilters` if they are fully replaced.

**Phase 2: Update Main Layouts to Use New Context**

**Objective:** Modify `DashboardLayout.tsx`, `SettingsLayout.tsx`, and `AdminLayout.tsx` to set the `activeViewContext.type` based on the current route.

**File:** `frontend/app/dashboard/layout.tsx`
**File:** `frontend/app/settings/layout.tsx`
**File:** `frontend/app/admin/layout.tsx`

**Steps (similar for each layout):**

1.  **Consume `DashboardContext`:**
    ```typescript
    // Example for frontend/app/dashboard/layout.tsx
    // ...
    function DashboardLayoutContent({ children }: DashboardLayoutProps) {
      const { isAuthenticated, isLoading } // ... from useAuth()
      const { setActiveViewContext, activeViewContext } = useDashboardContext(); // Get new context items
      const pathname = usePathname();
      // ...
    ```

2.  **`useEffect` to Set `activeViewContext.type`:**
    ```typescript
    // Example for frontend/app/dashboard/layout.tsx -> DashboardLayoutContent
    useEffect(() => {
      let currentType: MainViewType = 'tasks'; // Default for dashboard
      if (pathname.startsWith('/dashboard/groceries')) {
        currentType = 'groceries';
      } else if (pathname.startsWith('/dashboard/locations')) {
        currentType = 'locations';
      }
      // Add more else if for other main sections like /calendar, /meal-plan if they exist under /dashboard

      if (activeViewContext.type !== currentType || activeViewContext.filterId !== null || activeViewContext.specialView !== (currentType === 'tasks' ? 'incomplete' : null) ) {
         // When changing main view type, reset specific filters unless they are changing too
        setActiveViewContext({
          type: currentType,
          filterId: null, // Reset specific filter when changing main type
          specialView: currentType === 'tasks' ? 'incomplete' : null, // Default to incomplete for tasks view
        });
      }
    }, [pathname, setActiveViewContext, activeViewContext.type, activeViewContext.filterId, activeViewContext.specialView]);
    ```
    *   **For `SettingsLayout.tsx`:** `currentType` would be `'settings'`. You might also want to set `filterId` based on the specific settings sub-page (e.g., `filterId: 'profile'` if `pathname === '/settings/profile'`).
        ```typescript
        // frontend/app/settings/layout.tsx -> SettingsLayoutContent
        useEffect(() => {
            let currentMainType: MainViewType = 'settings';
            let specificSettingsPage: MainViewType | null = null;

            if (pathname === '/settings') specificSettingsPage = 'general_settings';
            else if (pathname === '/settings/profile') specificSettingsPage = 'profile';
            else if (pathname === '/settings/account') specificSettingsPage = 'account';
            else if (pathname === '/settings/tasks') specificSettingsPage = 'tasks'; // This means 'taskSettings'
            else if (pathname === '/settings/notifications') specificSettingsPage = 'notifications';
            else if (pathname === '/settings/datetime') specificSettingsPage = 'datetime';
            else if (pathname === '/settings/keytags') specificSettingsPage = 'keytags';
            else if (pathname === '/settings/task-locations') specificSettingsPage = 'task_locations';
            else if (pathname === '/settings/appearance') specificSettingsPage = 'appearance';
            // ... add more specific settings pages

            // If the main type OR the specific settings page (filterId) has changed
            if (activeViewContext.type !== currentMainType || activeViewContext.filterId !== (specificSettingsPage || null)) {
                setActiveViewContext({
                    type: currentMainType,
                    filterId: specificSettingsPage || null, // Use specific page as filterId
                    specialView: null, // No special views for settings
                });
            }
        }, [pathname, setActiveViewContext, activeViewContext.type, activeViewContext.filterId]);
        ```
    *   **For `AdminLayout.tsx`:** `currentType` would be `'admin'`. Similarly, `filterId` could represent the admin sub-page.

3.  **Pass Callbacks to Sidebar:**
    *   The layout will pass a unified callback to the `ContextualSidebar` (new name for `CategorySidebar`) to update the `activeViewContext`.
    ```typescript
    // Example for frontend/app/dashboard/layout.tsx -> DashboardLayoutContent
    const handleSidebarFilterChange = (
        newFilterId: string | null,
        newSpecialView?: TaskSpecialView // Only relevant for tasks
    ) => {
        setActiveViewContext({
            // type remains unchanged when filtering within a view
            filterId: newFilterId,
            specialView: activeViewContext.type === 'tasks' ? newSpecialView : null,
        });
    };

    // ... inside return:
    // <ContextualSidebar
    //   onSetFilter={handleSidebarFilterChange}
    //   onAddNew={ /* ... logic to call openNewTaskDialog or other context add functions ... */ }
    //   ... other props
    // />
    ```

**Phase 3: Refactor `CategorySidebar` to `ContextualSidebar`**

**Objective:** Transform `CategorySidebar.tsx` into a versatile `ContextualSidebar.tsx` that renders different content based on `activeViewContext.type`.

**File:** `frontend/components/category-sidebar.tsx` (rename to `ContextualSidebar.tsx`)

**Steps:**

1.  **Rename Component:** `CategorySidebar` -> `ContextualSidebar`.
2.  **Update Props:**
    ```typescript
    // frontend/components/ContextualSidebar.tsx (formerly CategorySidebar.tsx)
    import { ActiveViewContext, MainViewType, TaskSpecialView, useDashboardContext } from '@/lib/dashboard-context';
    // ...

    interface ContextualSidebarProps {
      // Data specific to different modes - can be fetched in context or passed if simpler
      taskCategories: HierarchicalCategory[]; // Still needed for task mode
      isLoadingTaskCategories: boolean;
      errorTaskCategories: string | null;
      completedTaskCount: number;
      uncategorizedTaskCount: number;

      // groceryLists?: GroceryListType[]; // Example for grocery mode
      // locationGroups?: LocationGroupType[]; // Example for location mode

      // Callback to update the main context
      onSetFilter: (filterId: string | null, specialView?: TaskSpecialView) => void;

      // Generic "Add New" callback; specific action determined by context type
      onAddNew: () => void;

      onCategoryUpdated?: () => void; // If category management stays here
      className?: string;
    }
    ```
3.  **Consume `activeViewContext`:**
    ```typescript
    // frontend/components/ContextualSidebar.tsx
    export default function ContextualSidebar({
      taskCategories, isLoadingTaskCategories, errorTaskCategories,
      completedTaskCount, uncategorizedTaskCount,
      onSetFilter, onAddNew, onCategoryUpdated, className
    }: ContextualSidebarProps) {
      const { activeViewContext, openNewTaskDialog /*, openNewGroceryDialog etc. */ } = useDashboardContext();
      // ... existing states for category expansion, add/edit dialogs ...
    ```
4.  **Implement `onAddNew` handler:**
    ```typescript
    // frontend/components/ContextualSidebar.tsx
    const handleAddNewClick = () => {
        // The onAddNew prop could be more specific based on context type,
        // or the sidebar can call context functions directly.
        // For simplicity, let's assume the parent layout passes a pre-configured onAddNew.
        // If not, the sidebar can use activeViewContext.type:
        switch (activeViewContext.type) {
            case 'tasks':
                openNewTaskDialog({}); // Open with default values
                break;
            case 'groceries':
                // openNewGroceryDialog?.({}); // If you have this
                console.log("Add new grocery item/list");
                break;
            case 'locations':
                // openNewLocationDialog?.({});
                console.log("Add new location");
                break;
            default:
                console.log("Add new for:", activeViewContext.type);
        }
    };
    ```
5.  **Conditional Rendering Logic:**
    *   Create separate render functions for each mode: `renderTaskModeContent()`, `renderGroceryModeContent()`, `renderLocationModeContent()`, `renderSettingsModeContent()`, `renderAdminModeContent()`.
    *   In the main `return` statement, use a `switch (activeViewContext.type)` to call the appropriate render function.

    ```typescript
    // frontend/components/ContextualSidebar.tsx

    const renderTaskModeContent = () => {
      // ... (Your existing CategorySidebar logic for rendering "New Task" button,
      //      "All Tasks", "Completed", "Uncategorized", and the task category tree)
      //      Callbacks like onCategorySelect will now call `props.onSetFilter(categoryId, null)`
      //      onShowAll -> `props.onSetFilter(null, 'all')`
      //      onShowCompleted -> `props.onSetFilter(null, 'completed')`
      //      onShowUncategorized -> `props.onSetFilter(null, 'uncategorized')`
      //      The "New Task" button should call `handleAddNewClick` or `props.onAddNew` directly.
      //      Category editing/adding can remain here for task categories.
    };

    const renderGroceryModeContent = () => (
      <div>
        <div className="p-4">
          <Button onClick={handleAddNewClick} className="w-full ...">New Grocery List/Item</Button>
        </div>
        {/* Example: List of shopping lists or grocery categories */}
        <div className="px-3 py-2">
          <h3 className="mb-2 px-4 text-lg font-semibold tracking-tight">Grocery Lists</h3>
          {/* ... render grocery lists ... */}
          <Button variant="ghost" className="w-full justify-start" onClick={() => onSetFilter(null)}>All Groceries</Button>
          <Button variant="ghost" className="w-full justify-start" onClick={() => onSetFilter('favorites')}>Favorite Recipes</Button>
        </div>
      </div>
    );

    const renderLocationModeContent = () => (
      <div>
        <div className="p-4">
           <Button onClick={handleAddNewClick} className="w-full ...">New Location</Button>
        </div>
        {/* Example: Location groups/filters */}
        <div className="px-3 py-2">
          <h3 className="mb-2 px-4 text-lg font-semibold tracking-tight">Location Groups</h3>
          <Button variant="ghost" className="w-full justify-start" onClick={() => onSetFilter(null)}>All Locations</Button>
          <Button variant="ghost" className="w-full justify-start" onClick={() => onSetFilter('work_locations')}>Work Locations</Button>
        </div>
      </div>
    );

    // Simplified renderSettingsModeContent
    const renderSettingsModeContent = () => {
        const settingsLinks = [ // This should ideally come from a config or be passed as props
            { href: "/settings", label: "General", type: "general_settings" as MainViewType },
            { href: "/settings/profile", label: "Profile", type: "profile" as MainViewType },
            { href: "/settings/account", label: "Account", type: "account" as MainViewType },
            // ... other settings links
        ];
        return (
            <div className="px-3 py-2">
                <h3 className="mb-2 px-4 text-lg font-semibold tracking-tight">Settings Navigation</h3>
                {settingsLinks.map(link => (
                    <Button
                        key={link.href}
                        variant={activeViewContext.filterId === link.type ? "secondary" : "ghost"}
                        className="w-full justify-start"
                        onClick={() => onSetFilter(link.type, null)} // Updates filterId
                    >
                        {link.label}
                    </Button>
                ))}
            </div>
        );
    };
     const renderAdminModeContent = () => { /* Similar to settings */ };


    let currentModeContent;
    switch (activeViewContext.type) {
      case 'tasks': currentModeContent = renderTaskModeContent(); break;
      case 'groceries': currentModeContent = renderGroceryModeContent(); break;
      case 'locations': currentModeContent = renderLocationModeContent(); break;
      case 'settings': // Fallthrough for general settings and specific sub-pages
      case 'profile':
      case 'account':
      // ... other MainViewType for settings
        currentModeContent = renderSettingsModeContent();
        break;
      case 'admin': currentModeContent = renderAdminModeContent(); break;
      default: currentModeContent = <p className="p-4">Sidebar not configured for {activeViewContext.type}</p>;
    }

    return (
      <div className={/* ... base styles ... */ className}>
        <ScrollArea className="flex-1 px-3">
          {currentModeContent}
        </ScrollArea>
        {/* ... (Fixed bottom part if any, e.g., settings link) ... */}
      </div>
    );
    }
    ```

6.  **Update Callsites:**
    *   In `DashboardLayout.tsx`, `SettingsLayout.tsx`, `AdminLayout.tsx`, replace `<CategorySidebar ... />` with `<ContextualSidebar ... />`.
    *   Pass the new `onSetFilter` callback and appropriate data props (e.g., `taskCategories` for task mode).
    *   The `onAddNew` prop passed to `ContextualSidebar` from these layouts should be tailored. For example, `DashboardLayout` might pass `onAddNew={openNewTaskDialog}`, while `GroceryLayout` (if you had one) would pass `onAddNew={openNewGroceryDialog}`.

**Phase 4: Update Content Pages**

**Objective:** Modify `TaskList.tsx`, `GroceriesPage.tsx`, `LocationsPage.tsx` to use `activeViewContext` from `DashboardContext` for filtering.

1.  **`frontend/components/task-list.tsx`:**
    *   Remove its internal `filters` and `sort` state if they are now fully managed by `DashboardContext`.
    *   Props: Change `filters?: TaskFilters` and `sort?: TaskSort` to `activeViewContext: ActiveViewContext` (or just consume it directly from context).
    *   In `useEffect` for fetching tasks:
        *   Use `activeViewContext.type` to confirm it's in 'tasks' mode.
        *   Use `activeViewContext.filterId` as `categoryContextId` or for `selectedCategoryIds` (if you adapt the context to store an array here for multi-select).
        *   Use `activeViewContext.specialView` for "all", "completed", "uncategorized".
        *   The `sort` prop can still be passed directly or come from context too.
    *   The `TaskFilterMenu` and `TaskSortDropdown` will now get their `currentFilters`/`currentSort` from `DashboardContext` and call `setActiveViewContext` (or a more specific context setter like `setTaskSort`) on change.

2.  **`frontend/app/dashboard/groceries/page.tsx` & `locations/page.tsx`:**
    *   Consume `activeViewContext` from `DashboardContext`.
    *   Filter their respective data (groceries, locations) based on `activeViewContext.filterId` if the `activeViewContext.type` matches their page type.

**Phase 5: Testing**

1.  **Context Propagation:** Verify `activeViewContext` updates correctly when navigating between main app sections (Tasks, Groceries, Locations, Settings, Admin) via `TopNavigationBar`.
2.  **Sidebar Adaptation:** Test that `ContextualSidebar` renders the correct UI and actions for each `activeViewContext.type`.
3.  **Filtering:**
    *   Ensure selecting a task category in the sidebar (in 'tasks' mode) updates `activeViewContext` and `TaskList` filters correctly.
    *   Test similar filtering for groceries and locations if implemented in their respective sidebar modes.
4.  **"Add New" Functionality:** Confirm the "New..." button in the sidebar triggers the correct dialog (New Task, New Grocery, etc.) based on the current `activeViewContext.type`.
5.  **Regression Testing:** Ensure existing functionalities (task display, editing, insights) are not broken.

This plan provides a structured approach. The key is to make `DashboardContext` the central hub for the application's current viewing state, and have the `ContextualSidebar` and main content pages react to changes in this context.