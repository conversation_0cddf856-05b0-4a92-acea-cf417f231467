'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useGrocery } from '@/lib/grocery-context';
import {
  Crown,
  Edit,
  Eye,
  Users,
  ArrowLeft,
  Share2
} from 'lucide-react';

interface GroceryListHeaderProps {
  className?: string;
  onBackToMyList?: () => void;
}

// Utility function to get initials from name or email
const getInitials = (name?: string, email?: string): string => {
  if (name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }
  if (email) {
    return email.slice(0, 2).toUpperCase();
  }
  return 'U';
};

export function GroceryListHeader({ className, onBackToMyList }: GroceryListHeaderProps) {
  const {
    groceryList,
    isShared,
    collaborators,
    userRole,
    switchToMyList
  } = useGrocery();

  const handleBackToMyList = async () => {
    try {
      await switchToMyList();
      onBackToMyList?.();
    } catch (error) {
      console.error('Failed to switch back to own list:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'editor':
        return <Edit className="h-4 w-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'editor':
        return 'secondary';
      case 'viewer':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // If this is the user's own list
  if (!groceryList || userRole === 'owner') {
    return (
      <div className={`flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}>
        <div className="flex items-center gap-3">
          <div>
            <h1 className="text-lg font-semibold">My Grocery List</h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Crown className="h-4 w-4 text-yellow-500" />
              <span>Your personal grocery list</span>
              {isShared && (
                <>
                  <span>•</span>
                  <Share2 className="h-4 w-4 text-green-500" />
                  <span>Shared with {collaborators.length} collaborator{collaborators.length !== 1 ? 's' : ''}</span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={getRoleBadgeVariant('owner')}>
            Owner
          </Badge>
        </div>
      </div>
    );
  }

  // If this is a shared list the user is collaborating on
  return (
    <div className={`flex items-center justify-between p-4 border-b bg-muted/50 ${className}`}>
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBackToMyList}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Avatar className="h-8 w-8">
          <AvatarFallback>
            {getInitials(
              typeof groceryList?.userId === 'object' ? groceryList.userId.name : undefined,
              typeof groceryList?.userId === 'object' ? groceryList.userId.email : undefined
            )}
          </AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-lg font-semibold">
            {groceryList?.name || `${
              typeof groceryList?.userId === 'object'
                ? (groceryList.userId.name || groceryList.userId.email || 'Unknown')
                : 'Unknown'
            }'s Grocery List`}
          </h1>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Shared by {
              typeof groceryList?.userId === 'object'
                ? (groceryList.userId.name || groceryList.userId.email || 'Unknown')
                : 'Unknown'
            }</span>
            <span>•</span>
            <Users className="h-4 w-4" />
            <span>{(collaborators?.length || 0) + 1} member{((collaborators?.length || 0) + 1) !== 1 ? 's' : ''}</span>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Badge variant={getRoleBadgeVariant(userRole || 'viewer')}>
          <div className="flex items-center gap-1">
            {getRoleIcon(userRole || 'viewer')}
            <span className="capitalize">{userRole || 'viewer'}</span>
          </div>
        </Badge>
      </div>
    </div>
  );
}
