import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../../../core/config/api_config.dart';
import '../../../core/storage/local_storage.dart';
import '../../../core/widgets/app_bar_widget.dart';

class ServerSettingsScreen extends ConsumerStatefulWidget {
  static const routeName = '/settings/server';

  const ServerSettingsScreen({super.key});

  @override
  ConsumerState<ServerSettingsScreen> createState() => _ServerSettingsScreenState();
}

class _ServerSettingsScreenState extends ConsumerState<ServerSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _ipAddressController = TextEditingController();
  final _portController = TextEditingController(text: '3001');
  late String _currentBaseUrl;
  bool _isLoading = false;
  String? _testResult;
  bool _isTesting = false;
  bool _isProduction = false;  // Track if app is in production mode

  @override
  void initState() {
    super.initState();
    _currentBaseUrl = ApiConfig.baseUrl;
    _loadSavedUrl();
    // Using a future to check production status
    Future.microtask(() {
      setState(() {
        _isProduction = ApiConfig.isProduction; // Use the public getter
      });
    });
  }

  Future<void> _loadSavedUrl() async {
    final localStorage = LocalStorageService();
    final savedBaseUrl = await localStorage.getString('custom_server_url');
    
    if (savedBaseUrl != null && savedBaseUrl.isNotEmpty) {
      setState(() {
        _currentBaseUrl = savedBaseUrl;
      });
      
      // Parse saved URL to extract IP and port
      try {
        final uri = Uri.parse(savedBaseUrl);
        _ipAddressController.text = uri.host;
        if (uri.port != 0) {
          _portController.text = uri.port.toString();
        }
      } catch (e) {
        // If parsing fails, keep default values
        print('Failed to parse saved URL: $e');
      }
    }
  }

  Future<void> _saveServerUrl() async {
    // In production mode, don't allow changes
    if (_isProduction) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Server URL cannot be changed in production mode'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isLoading = true;
      _testResult = null;
    });
    
    try {
      final ipAddress = _ipAddressController.text.trim();
      final port = _portController.text.trim();
      final newUrl = 'http://$ipAddress:$port';
      
      // Update the API config - this will also save to SharedPreferences
      ApiConfig.updateBaseUrl(newUrl);
      
      // Wait a moment to ensure the update has propagated
      await Future.delayed(const Duration(milliseconds: 300));
      
      setState(() {
        _currentBaseUrl = newUrl;
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Server URL updated successfully')),
      );
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating server URL: ${e.toString()}')),
      );
    }
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTesting = true;
      _testResult = null;
    });

    try {
      // Create a fresh Dio instance with the current base URL
      final dio = Dio(BaseOptions(
        baseUrl: _currentBaseUrl,
        connectTimeout: const Duration(seconds: 5),
      ));

      // Try to connect to the server's ping endpoint
      final response = await dio.get(ApiConfig.pingEndpoint);
      
      setState(() {
        _isTesting = false;
        _testResult = 'SUCCESS: Connected to server! Server responded with status ${response.statusCode}';
      });
    } catch (e) {
      setState(() {
        _isTesting = false;
        _testResult = 'ERROR: Failed to connect to server!\n${e.toString()}';
      });
    }
  }

  void _resetToDefault() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset to Default'),
        content: const Text('Are you sure you want to reset to the default server URL?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              setState(() => _isLoading = true);
              
              ApiConfig.updateBaseUrl(ApiConfig.getBaseUrl());
              
              final localStorage = LocalStorageService();
              await localStorage.remove('custom_server_url');
              
              setState(() {
                _currentBaseUrl = ApiConfig.baseUrl;
                _ipAddressController.clear();
                _portController.text = '3001';
                _isLoading = false;
                _testResult = null;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Reset to default server URL')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'Server Settings',
        actions: _isProduction ? [] : [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetToDefault,
            tooltip: 'Reset to default',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Production mode warning
                  if (_isProduction)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.amber.shade800),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline, color: Colors.amber.shade800),
                              const SizedBox(width: 8),
                              Text(
                                'Production Mode',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber.shade800,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'The app is running in production mode. Server settings cannot be modified in this mode.',
                          ),
                        ],
                      ),
                    ),

                  const Text(
                    'Current Server URL:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(_currentBaseUrl),
                  const SizedBox(height: 20),
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Configure Server Connection',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 10),
                            TextFormField(
                              controller: _ipAddressController,
                              decoration: const InputDecoration(
                                labelText: 'Server IP Address',
                                hintText: 'e.g., ************',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.text,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter the server IP address';
                                }
                                return null;
                              },
                              enabled: !_isProduction,
                            ),
                            const SizedBox(height: 10),
                            TextFormField(
                              controller: _portController,
                              decoration: const InputDecoration(
                                labelText: 'Port',
                                hintText: 'e.g., 3001',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter the server port';
                                }
                                final port = int.tryParse(value);
                                if (port == null || port <= 0 || port > 65535) {
                                  return 'Please enter a valid port number (1-65535)';
                                }
                                return null;
                              },
                              enabled: !_isProduction,
                            ),
                            const SizedBox(height: 20),
                            Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: ElevatedButton(
                                    onPressed: _isProduction ? null : _saveServerUrl,
                                    child: const Text('Update Server URL'),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  flex: 1,
                                  child: ElevatedButton(
                                    onPressed: _testConnection,
                                    child: _isTesting
                                        ? const SizedBox(
                                            height: 16,
                                            width: 16,
                                            child: CircularProgressIndicator(strokeWidth: 2),
                                          )
                                        : const Text('Test'),
                                  ),
                                ),
                              ],
                            ),
                            if (_testResult != null) ...[
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: _testResult!.startsWith('SUCCESS')
                                      ? Colors.green.withOpacity(0.1)
                                      : Colors.red.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                  border: Border.all(
                                    color: _testResult!.startsWith('SUCCESS')
                                        ? Colors.green.withOpacity(0.3)
                                        : Colors.red.withOpacity(0.3),
                                  ),
                                ),
                                child: Text(
                                  _testResult!,
                                  style: TextStyle(
                                    color: _testResult!.startsWith('SUCCESS')
                                        ? Colors.green
                                        : Colors.red,
                                  ),
                                ),
                              ),
                            ],
                            const SizedBox(height: 10),
                            const Text(
                              'Note: If you are connecting to a server on the same network, use your computer\'s local IP address (e.g., ************)',
                              style: TextStyle(
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  @override
  void dispose() {
    _ipAddressController.dispose();
    _portController.dispose();
    super.dispose();
  }
} 