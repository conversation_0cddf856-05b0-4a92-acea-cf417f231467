/// Model for grocery preferences
class GroceryPreferences {
  final String defaultListType; // 'personal' or 'shared'
  final String? defaultSharedListOwnerId;
  final bool autoSwitchToDefault;
  final bool showPersonalListInSidebar;

  const GroceryPreferences({
    this.defaultListType = 'personal',
    this.defaultSharedListOwnerId,
    this.autoSwitchToDefault = false,
    this.showPersonalListInSidebar = true,
  });

  /// Factory constructor for creating from JSON
  factory GroceryPreferences.fromJson(Map<String, dynamic> json) {
    return GroceryPreferences(
      defaultListType: json['defaultListType'] as String? ?? 'personal',
      defaultSharedListOwnerId: json['defaultSharedListOwnerId'] as String?,
      autoSwitchToDefault: json['autoSwitchToDefault'] as bool? ?? false,
      showPersonalListInSidebar: json['showPersonalListInSidebar'] as bool? ?? true,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'defaultListType': defaultListType,
      'defaultSharedListOwnerId': defaultSharedListOwnerId,
      'autoSwitchToDefault': autoSwitchToDefault,
      'showPersonalListInSidebar': showPersonalListInSidebar,
    };
  }

  /// Create a copy with some fields replaced
  GroceryPreferences copyWith({
    String? defaultListType,
    String? defaultSharedListOwnerId,
    bool? autoSwitchToDefault,
    bool? showPersonalListInSidebar,
  }) {
    return GroceryPreferences(
      defaultListType: defaultListType ?? this.defaultListType,
      defaultSharedListOwnerId: defaultSharedListOwnerId ?? this.defaultSharedListOwnerId,
      autoSwitchToDefault: autoSwitchToDefault ?? this.autoSwitchToDefault,
      showPersonalListInSidebar: showPersonalListInSidebar ?? this.showPersonalListInSidebar,
    );
  }

  /// Check if this is a shared list preference
  bool get isSharedDefault => defaultListType == 'shared';

  /// Check if this is a personal list preference
  bool get isPersonalDefault => defaultListType == 'personal';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroceryPreferences &&
        other.defaultListType == defaultListType &&
        other.defaultSharedListOwnerId == defaultSharedListOwnerId &&
        other.autoSwitchToDefault == autoSwitchToDefault &&
        other.showPersonalListInSidebar == showPersonalListInSidebar;
  }

  @override
  int get hashCode {
    return Object.hash(
      defaultListType,
      defaultSharedListOwnerId,
      autoSwitchToDefault,
      showPersonalListInSidebar,
    );
  }

  @override
  String toString() {
    return 'GroceryPreferences('
        'defaultListType: $defaultListType, '
        'defaultSharedListOwnerId: $defaultSharedListOwnerId, '
        'autoSwitchToDefault: $autoSwitchToDefault, '
        'showPersonalListInSidebar: $showPersonalListInSidebar'
        ')';
  }
}

/// Model for updating grocery preferences
class UpdateGroceryPreferencesData {
  final String? defaultListType;
  final String? defaultSharedListOwnerId;
  final bool? autoSwitchToDefault;
  final bool? showPersonalListInSidebar;

  const UpdateGroceryPreferencesData({
    this.defaultListType,
    this.defaultSharedListOwnerId,
    this.autoSwitchToDefault,
    this.showPersonalListInSidebar,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    
    if (defaultListType != null) {
      json['defaultListType'] = defaultListType;
    }
    if (defaultSharedListOwnerId != null) {
      json['defaultSharedListOwnerId'] = defaultSharedListOwnerId;
    }
    if (autoSwitchToDefault != null) {
      json['autoSwitchToDefault'] = autoSwitchToDefault;
    }
    if (showPersonalListInSidebar != null) {
      json['showPersonalListInSidebar'] = showPersonalListInSidebar;
    }
    
    return json;
  }

  @override
  String toString() {
    return 'UpdateGroceryPreferencesData('
        'defaultListType: $defaultListType, '
        'defaultSharedListOwnerId: $defaultSharedListOwnerId, '
        'autoSwitchToDefault: $autoSwitchToDefault, '
        'showPersonalListInSidebar: $showPersonalListInSidebar'
        ')';
  }
}
