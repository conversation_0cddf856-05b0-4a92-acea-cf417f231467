import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../models/grocery_item.dart';

/// Widget for displaying a grocery item in the list
class GroceryListItem extends StatelessWidget {
  final GroceryItem item;
  final Function(String) onToggleCheck;
  final Function(String) onDelete;
  final Function(String)? onEdit;
  final bool compact;
  final bool showCollaborationInfo;

  const GroceryListItem({
    super.key,
    required this.item,
    required this.onToggleCheck,
    required this.onDelete,
    this.onEdit,
    this.compact = false,
    this.showCollaborationInfo = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get category color or use default
    Color categoryColor = item.category?.getColorValue() ?? Colors.grey;

    return Card(
      margin: EdgeInsets.symmetric(
        vertical: compact ? 2 : 4,
        horizontal: compact ? 2 : 4,
      ),
      elevation: 1,
      child: InkWell(
        onTap: () => onToggleCheck(item.id),
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: compact ? 8 : 12,
            horizontal: compact ? 8 : 12,
          ),
          child: Row(
            children: [
              // Checkbox
              SizedBox(
                width: compact ? 24 : 32,
                height: compact ? 24 : 32,
                child: Checkbox(
                  value: item.isChecked,
                  onChanged: (_) => onToggleCheck(item.id),
                  activeColor: Colors.green,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),

              // Category indicator
              Container(
                width: 4,
                height: compact ? 24 : 32,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: categoryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Item name
                    Text(
                      item.name,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        decoration: item.isChecked ? TextDecoration.lineThrough : null,
                        color: item.isChecked ? theme.textTheme.bodySmall?.color : null,
                        fontWeight: item.isChecked ? FontWeight.normal : FontWeight.bold,
                      ),
                    ),

                    // Item details (quantity, notes, category)
                    if ((item.quantity != null || item.category != null) && !compact)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Row(
                          children: [
                            // Quantity
                            if (item.quantity != null)
                              Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: Text(
                                  'Qty: ${item.quantity}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: item.isChecked
                                        ? theme.textTheme.bodySmall?.color?.withOpacity(0.7)
                                        : theme.textTheme.bodySmall?.color,
                                  ),
                                ),
                              ),

                            // Category
                            if (item.category != null)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: categoryColor.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  item.category!.name,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: categoryColor.withOpacity(0.8),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                    // Compact mode quantity
                    if (item.quantity != null && compact)
                      Text(
                        item.quantity!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                        ),
                      ),

                    // Collaboration info
                    if (showCollaborationInfo && !compact)
                      _buildCollaborationInfo(theme),
                  ],
                ),
              ),

              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Edit button
                  if (onEdit != null)
                    IconButton(
                      icon: Icon(
                        LucideIcons.edit,
                        size: compact ? 16 : 20,
                      ),
                      onPressed: () => onEdit!(item.id),
                      visualDensity: VisualDensity.compact,
                      splashRadius: compact ? 20 : 24,
                    ),

                  // Delete button
                  IconButton(
                    icon: Icon(
                      LucideIcons.trash2,
                      size: compact ? 16 : 20,
                    ),
                    onPressed: () => onDelete(item.id),
                    visualDensity: VisualDensity.compact,
                    splashRadius: compact ? 20 : 24,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCollaborationInfo(ThemeData theme) {
    final collaborationWidgets = <Widget>[];

    // Show who added the item
    if (item.addedBy != null) {
      collaborationWidgets.add(
        Row(
          children: [
            const Icon(
              LucideIcons.userPlus,
              size: 12,
              color: Colors.blue,
            ),
            const SizedBox(width: 4),
            Text(
              'Added by ${item.addedBy!.name}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.blue,
                fontSize: 10,
              ),
            ),
          ],
        ),
      );
    }

    // Show who checked the item and when
    if (item.isChecked && item.checkedBy != null) {
      collaborationWidgets.add(
        Row(
          children: [
            const Icon(
              LucideIcons.check,
              size: 12,
              color: Colors.green,
            ),
            const SizedBox(width: 4),
            Text(
              'Checked by ${item.checkedBy!.name}${item.checkedAt != null ? ' ${_formatTime(item.checkedAt!)}' : ''}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.green,
                fontSize: 10,
              ),
            ),
          ],
        ),
      );
    }

    // Show who last modified the item
    if (item.lastModifiedBy != null && item.lastModifiedBy!.id != item.addedBy?.id) {
      collaborationWidgets.add(
        Row(
          children: [
            const Icon(
              LucideIcons.edit,
              size: 12,
              color: Colors.orange,
            ),
            const SizedBox(width: 4),
            Text(
              'Modified by ${item.lastModifiedBy!.name}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.orange,
                fontSize: 10,
              ),
            ),
          ],
        ),
      );
    }

    if (collaborationWidgets.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 8,
        runSpacing: 2,
        children: collaborationWidgets,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }
}
