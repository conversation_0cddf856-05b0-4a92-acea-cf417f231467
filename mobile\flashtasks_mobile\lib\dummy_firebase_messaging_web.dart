/// Dummy implementation of firebase_messaging_web package to avoid js dependency conflict
/// This file is only used to override the firebase_messaging_web package in dependency_overrides
library firebase_messaging_web;

import 'dart:async';

class FirebaseMessagingWeb {
  static final FirebaseMessagingWeb _instance = FirebaseMessagingWeb._();
  FirebaseMessagingWeb._();
  
  factory FirebaseMessagingWeb() => _instance;
  
  Future<bool> isSupported() async => false;
  Future<String?> getToken() async => null;
  Future<void> requestPermission() async {}
  Future<bool> deleteToken() async => true;
  Stream<dynamic> onMessage() => const Stream.empty();
  Future<void> setBackgroundMessageHandler(Function handler) async {}
}

// Declare plugin class to satisfy Flutter's plugin registration
class FirebaseMessagingPlugin {
  static void registerWith(dynamic registrar) {
    // No-op implementation
  }
} 