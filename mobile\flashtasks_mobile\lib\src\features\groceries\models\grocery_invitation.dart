import 'grocery_list.dart';

/// Enum for invitation status
enum InvitationStatus { pending, accepted, declined, expired }

/// Model class for grocery list information in invitations
class GroceryListInfo {
  final String id;
  final String? name;
  final String userId;

  GroceryListInfo({
    required this.id,
    this.name,
    required this.userId,
  });

  factory GroceryListInfo.fromJson(Map<String, dynamic> json) {
    return GroceryListInfo(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] as String?,
      userId: json['userId'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      if (name != null) 'name': name,
      'userId': userId,
    };
  }
}

/// Model class for grocery list invitations
class GroceryListInvitation {
  final String id;
  final String groceryListId;
  final CollaboratorUser inviterUserId;
  final String inviteeEmail;
  final CollaboratorRole role;
  final InvitationStatus status;
  final String token;
  final DateTime expiresAt;
  final DateTime createdAt;
  final GroceryListInfo? groceryList;

  GroceryListInvitation({
    required this.id,
    required this.groceryListId,
    required this.inviterUserId,
    required this.inviteeEmail,
    required this.role,
    required this.status,
    required this.token,
    required this.expiresAt,
    required this.createdAt,
    this.groceryList,
  });

  factory GroceryListInvitation.fromJson(Map<String, dynamic> json) {
    return GroceryListInvitation(
      id: json['_id'] ?? json['id'] ?? '',
      groceryListId: json['groceryListId'] ?? '',
      inviterUserId: CollaboratorUser.fromJson(json['inviterUserId'] as Map<String, dynamic>),
      inviteeEmail: json['inviteeEmail'] ?? '',
      role: CollaboratorRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => CollaboratorRole.viewer,
      ),
      status: InvitationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => InvitationStatus.pending,
      ),
      token: json['token'] ?? '',
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      groceryList: json['groceryList'] != null
          ? GroceryListInfo.fromJson(json['groceryList'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'groceryListId': groceryListId,
      'inviterUserId': inviterUserId.toJson(),
      'inviteeEmail': inviteeEmail,
      'role': role.name,
      'status': status.name,
      'token': token,
      'expiresAt': expiresAt.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      if (groceryList != null) 'groceryList': groceryList!.toJson(),
    };
  }

  GroceryListInvitation copyWith({
    String? id,
    String? groceryListId,
    CollaboratorUser? inviterUserId,
    String? inviteeEmail,
    CollaboratorRole? role,
    InvitationStatus? status,
    String? token,
    DateTime? expiresAt,
    DateTime? createdAt,
    GroceryListInfo? groceryList,
  }) {
    return GroceryListInvitation(
      id: id ?? this.id,
      groceryListId: groceryListId ?? this.groceryListId,
      inviterUserId: inviterUserId ?? this.inviterUserId,
      inviteeEmail: inviteeEmail ?? this.inviteeEmail,
      role: role ?? this.role,
      status: status ?? this.status,
      token: token ?? this.token,
      expiresAt: expiresAt ?? this.expiresAt,
      createdAt: createdAt ?? this.createdAt,
      groceryList: groceryList ?? this.groceryList,
    );
  }

  /// Check if the invitation is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if the invitation is still pending
  bool get isPending => status == InvitationStatus.pending && !isExpired;

  /// Check if the invitation can be acted upon
  bool get canBeActedUpon => isPending;
}
