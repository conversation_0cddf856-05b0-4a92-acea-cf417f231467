// Alias for GroceryListItem to maintain compatibility
export 'grocery_list_item.dart';

import 'package:flutter/material.dart';
import '../models/grocery_item.dart';
import 'grocery_list_item.dart';

/// Alias for GroceryListItem with enhanced functionality
class GroceryItemWidget extends StatelessWidget {
  final GroceryItem item;
  final VoidCallback onToggleCheck;
  final VoidCallback onDelete;
  final Function(Map<String, dynamic> updates)? onUpdate;
  final bool showCollaborationInfo;

  const GroceryItemWidget({
    super.key,
    required this.item,
    required this.onToggleCheck,
    required this.onDelete,
    this.onUpdate,
    this.showCollaborationInfo = false,
  });

  @override
  Widget build(BuildContext context) {
    return GroceryListItem(
      item: item,
      onToggleCheck: (_) => onToggleCheck(),
      onDelete: (_) => onDelete(),
      onEdit: onUpdate != null ? (_) => _showEditDialog(context) : null,
      showCollaborationInfo: showCollaborationInfo,
    );
  }

  void _showEditDialog(BuildContext context) {
    // TODO: Implement edit dialog or use existing one
    // For now, just show a simple dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Item'),
        content: const Text('Edit functionality not yet implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
