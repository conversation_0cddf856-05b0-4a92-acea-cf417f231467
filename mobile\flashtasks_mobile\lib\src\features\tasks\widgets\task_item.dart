import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

import '../../../core/config/app_theme.dart';
import '../models/task.dart';
import '../models/category.dart';

/// A card widget that displays a task's information
/// Updated version 1.1 with reminder text and multiple category fixes
class TaskItem extends StatelessWidget {
  /// The task to display
  final TaskListItem task;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the task completion is toggled
  final VoidCallback? onToggleCompletion;

  /// Callback when the task is deleted
  final VoidCallback? onDelete;

  /// Callback when the task is edited
  final Function(TaskListItem)? onEdit;

  const TaskItem({
    super.key,
    required this.task,
    this.onTap,
    this.onToggleCompletion,
    this.onDelete,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get the priority color for the left border
    final priorityColor = AppTheme.getPriorityColor(
      task.priority?.toString(),
      theme.brightness == Brightness.dark,
    );
    
    // Debug priority rendering
    print('🎨 Task Item Border: Task ID=${task.id}');
    print('🎨 Task Priority: ${task.priority}');
    print('🎨 Priority Color: $priorityColor');

    return Dismissible(
      key: Key('task-${task.id}'),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20.0),
        color: colorScheme.error,
        child: Icon(
          LucideIcons.trash,
          color: colorScheme.onError,
        ),
      ),
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Task'),
            content: Text('Are you sure you want to delete "${task.title}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
        ) ?? false;
      },
      onDismissed: (direction) {
        onDelete?.call();
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 0),
        clipBehavior: Clip.hardEdge,
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Priority indicator
              Container(
                width: 4,
                color: priorityColor,
              ),

              // Checkbox
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: InkWell(
                  onTap: () {
                    print('🔄 Checkbox tapped for task: ${task.id}');
                    // Use a callback-based approach for more reliable state updates
                    onToggleCompletion?.call();
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: SizedBox(
                    width: 24, 
                    height: 24,
                    child: Checkbox(
                      value: task.completed,
                      onChanged: (_) {
                        // Use a callback-based approach for more reliable state updates
                        onToggleCompletion?.call();
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ),

              // Task content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0, 14, 8, 14),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Task title
                      Text(
                        task.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          decoration: task.completed
                              ? TextDecoration.lineThrough
                              : null,
                          color: task.completed
                              ? theme.colorScheme.onSurface.withAlpha(128) // 0.5 opacity
                              : theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                          fontSize: 15, // Slightly smaller for mobile
                        ),
                        maxLines: 2, // Allow wrapping for longer titles
                        overflow: TextOverflow.ellipsis,
                      ),

                      // Content preview
                      if (task.content != null && task.content!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            task.content!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 opacity
                              decoration: task.completed ? TextDecoration.lineThrough : null,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                      // Metadata row
                      if (_hasMetadata)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: _buildMetadataRow(context),
                        ),
                    ],
                  ),
                ),
              ),

              // Action menu
              IconButton(
                icon: Icon(
                  LucideIcons.moreVertical,
                  size: 18,
                  color: theme.colorScheme.onSurface.withAlpha(128), // 0.5 opacity
                ),
                onPressed: () => _showActionMenu(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                splashRadius: 20,
              ),

              const SizedBox(width: 8),
            ],
          ),
        ),
      ),
    );
  }

  bool get _hasMetadata =>
      task.dueDate != null ||
      task.priority != null ||
      task.category != null ||
      task.location != null ||
      task.hasReminder ||
      task.isAiGenerated ||
      task.dependsOnCount > 0 ||
      task.blocksCount > 0 ||
      task.metadata.aiWasAutoCorrected == true ||
      task.metadata.aiConfidence == 'Low' ||
      task.metadata.aiClarificationNeeded != null;

  Widget _buildMetadataRow(BuildContext context) {
    final theme = Theme.of(context);
    final metadataColor = task.completed
        ? theme.colorScheme.onSurface.withAlpha(102) // 0.4 opacity
        : theme.colorScheme.onSurface.withAlpha(178); // 0.7 opacity

    // Debug log the categories
    print('🏷️ Building metadata row for task ${task.id}');
    print('🏷️ Has reminder flag: ${task.hasReminder}');
    print('🏷️ Categories count: ${task.categories.length}');
    
    // Log each category in detail
    if (task.categories.isNotEmpty) {
      print('🏷️ All categories for task ${task.id}:');
      for (var cat in task.categories) {
        print('🏷️ Category: ${cat.id}, ${cat.name}, ${cat.color}');
      }
    }
    if (task.category != null) {
      print('🏷️ Primary category: ${task.category!.id}, ${task.category!.name}, ${task.category!.color}');
    }

    // Create an explicit list of widgets that we'll add to the Wrap
    final List<Widget> metadataWidgets = [];
    
    // Due date
    if (task.dueDate != null) {
      metadataWidgets.add(
        _buildMetaInfo(
          context,
          LucideIcons.calendar,
          _formatDueDate(task.dueDate!),
          metadataColor,
          _isOverdue(task.dueDate!) && !task.completed
              ? theme.colorScheme.error
              : null,
        ),
      );
    }

    // Priority badge
    if (task.priority != null && !task.completed) {
      metadataWidgets.add(_buildPriorityBadge(context, task.priority!));
    }

    // Category badges - show all categories if available
    if (task.categories.isNotEmpty) {
      print('🏷️ About to render ${task.categories.length} categories in UI');
      
      // Create a set to track category IDs we've already added to prevent duplication
      final addedCategoryIds = <String>{};
      
      for (var category in task.categories) {
        // Only add the category if we haven't already processed one with this ID
        if (!addedCategoryIds.contains(category.id)) {
          print('🏷️ Adding category to UI: ${category.id}, ${category.name}');
          metadataWidgets.add(_buildCategoryBadge(context, category));
          addedCategoryIds.add(category.id);
        }
      }
    } else if (task.category != null) {
      print('🏷️ No categories list, falling back to primary category: ${task.category!.id}');
      metadataWidgets.add(_buildCategoryBadge(context, task.category!));
    }

    // Location
    if (task.location != null) {
      metadataWidgets.add(
        _buildMetaInfo(
          context,
          LucideIcons.mapPin,
          task.location!.name,
          metadataColor,
        ),
      );
    }

    // Reminder indicator - Now with improved visibility
    if (task.hasReminder) {
      metadataWidgets.add(
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.amber.withAlpha(task.completed ? 38 : 51), // Lower opacity when completed
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                LucideIcons.bell,
                size: 12,
                color: task.completed
                    ? Colors.amber.withAlpha(178) // 0.7 opacity yellow when completed
                    : Colors.amber, // Bright yellow when active
              ),
              const Gap(2),
              Text(
                'Reminder',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: task.completed
                      ? Colors.amber.withAlpha(178) // 0.7 opacity yellow when completed
                      : Colors.amber, // Bright yellow when active
                ),
              ),
            ],
          ),
        ),
      );
    }

    // AI Generated badge
    if (task.isAiGenerated) {
      metadataWidgets.add(_buildAiBadge(context));
    }
    
    // AI Autocorrection badge
    if (task.metadata.aiWasAutoCorrected == true) {
      metadataWidgets.add(_buildAutocorrectionBadge(context));
    }
    
    // AI Low Confidence badge
    if (task.metadata.aiConfidence == 'Low') {
      metadataWidgets.add(_buildLowConfidenceBadge(context));
    }
    
    // AI Needs Clarification badge
    if (task.metadata.aiClarificationNeeded != null) {
      metadataWidgets.add(_buildClarificationBadge(context));
    }

    // Dependencies
    if (task.dependsOnCount > 0) {
      metadataWidgets.add(
        Icon(
          LucideIcons.link2,
          size: 14,
          color: metadataColor,
        ),
      );
    }

    // Blockers
    if (task.blocksCount > 0) {
      metadataWidgets.add(
        Icon(
          LucideIcons.lock,
          size: 14,
          color: theme.colorScheme.error.withAlpha(204), // 0.8 opacity
        ),
      );
    }

    return Wrap(
      spacing: 8, // Tighter spacing for mobile
      runSpacing: 4, // Vertical spacing between wrapped rows
      children: metadataWidgets,
    );
  }

  Widget _buildPriorityBadge(BuildContext context, String priority) {
    final theme = Theme.of(context);
    final priorityColor = AppTheme.getPriorityColor(
      priority,
      theme.brightness == Brightness.dark,
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: priorityColor.withAlpha(51), // 0.2 opacity
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        priority,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: priorityColor,
        ),
      ),
    );
  }

  Widget _buildCategoryBadge(BuildContext context, Category category) {
    // Debug print to see what's coming from the API
    print('📊 Category in _buildCategoryBadge: ${category.id}, ${category.name}, ${category.color}');

    // Parse the color from the category
    Color backgroundColor = Colors.blue; // Default color
    if (category.color != null && category.color!.isNotEmpty) {
      try {
        String colorStr = category.color!.trim();

        // Handle different color formats
        if (colorStr.startsWith('#')) {
          // Handle hex color with # prefix
          if (colorStr.length == 7) { // #RRGGBB format
            backgroundColor = Color(int.parse('0xFF${colorStr.substring(1)}'));
          } else if (colorStr.length == 9) { // #AARRGGBB format
            backgroundColor = Color(int.parse('0x${colorStr.substring(1)}'));
          } else {
            print('⚠️ Invalid hex color format: $colorStr');
          }
        } else if (colorStr.startsWith('0x') || colorStr.startsWith('0X')) {
          // Handle hex color with 0x prefix
          backgroundColor = Color(int.parse(colorStr));
        } else if (colorStr.length == 6) {
          // Handle hex color without # prefix (RRGGBB)
          backgroundColor = Color(int.parse('0xFF$colorStr'));
        } else {
          // Try to parse as integer directly
          backgroundColor = Color(int.parse(colorStr));
        }
      } catch (e) {
        print('⚠️ Error parsing category color: ${category.color} - $e');
        // Use default color if parsing fails
      }
    } else {
      print('⚠️ Category has no color: ${category.id}, ${category.name}');
    }

    // Calculate text color based on background brightness
    final luminance = backgroundColor.computeLuminance();
    final textColor = luminance > 0.5 ? Colors.black : Colors.white;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: task.completed
            ? backgroundColor.withAlpha(102) // 0.4 opacity
            : backgroundColor.withAlpha(204), // 0.8 opacity
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        category.name,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildAiBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
      decoration: BoxDecoration(
        color: Colors.cyan.withAlpha(38), // 0.15 opacity
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Text(
        'AI',
        style: TextStyle(
          fontSize: 9,
          fontWeight: FontWeight.w600,
          color: Colors.cyan,
        ),
      ),
    );
  }
  
  // Badge for autocorrected text
  Widget _buildAutocorrectionBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.lightBlue.withAlpha(38), // 0.15 opacity
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.edit3,
            size: 10,
            color: Colors.lightBlue,
          ),
          Gap(2),
          Text(
            'Auto-corrected',
            style: TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.w500,
              color: Colors.lightBlue,
            ),
          ),
        ],
      ),
    );
  }
  
  // Badge for low confidence AI interpretation
  Widget _buildLowConfidenceBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.amber.withAlpha(38), // 0.15 opacity
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.sparkles,
            size: 10,
            color: Colors.amber,
          ),
          Gap(2),
          Text(
            'Low confidence',
            style: TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.w500,
              color: Colors.amber,
            ),
          ),
        ],
      ),
    );
  }
  
  // Badge for AI needing clarification
  Widget _buildClarificationBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange.withAlpha(38), // 0.15 opacity
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.helpCircle,
            size: 10,
            color: Colors.orange,
          ),
          Gap(2),
          Text(
            'Needs clarification',
            style: TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.w500,
              color: Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  void _showActionMenu(BuildContext context) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(LucideIcons.edit, color: theme.colorScheme.primary),
              title: Text('Edit Task', style: theme.textTheme.titleMedium),
              onTap: () {
                Navigator.pop(context);
                onEdit?.call(task);
              },
            ),
            ListTile(
              leading: Icon(LucideIcons.trash, color: theme.colorScheme.error),
              title: Text('Delete Task',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _confirmDelete(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDelete(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: Text('Are you sure you want to delete "${task.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onDelete?.call();
            },
            style: TextButton.styleFrom(
              foregroundColor: theme.colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildMetaInfo(
    BuildContext context,
    IconData icon,
    String text,
    Color color, [
    Color? iconColor,
  ]) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: iconColor ?? color,
        ),
        const Gap(4),
        Text(
          text,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
          ),
        ),
      ],
    );
  }

  String _formatDueDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final taskDate = DateTime(date.year, date.month, date.day);

    if (taskDate == today) {
      return 'Today';
    } else if (taskDate == tomorrow) {
      return 'Tomorrow';
    } else {
      return DateFormat.MMMd().format(date);
    }
  }

  bool _isOverdue(DateTime date) {
    // Compare date part only, like the web frontend
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDate = DateTime(date.year, date.month, date.day);
    return dueDate.isBefore(today);
  }
}