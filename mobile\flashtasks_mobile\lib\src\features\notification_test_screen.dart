import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/services/reminder_service.dart';
import '../core/services/notification_service.dart';
import '../core/services/permission_service.dart';

class NotificationTestScreen extends ConsumerWidget {
  const NotificationTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationService = ref.watch(notificationServiceProvider);
    final reminderService = ReminderService();
    final permissionService = ref.watch(permissionServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Notification Permissions',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final hasPermission = await permissionService.isNotificationPermissionGranted();

                            if (!context.mounted) return;

                            if (hasPermission) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Notification permission is already granted'),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            } else {
                              final result = await permissionService.requestNotificationPermission();

                              if (!context.mounted) return;

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Notification permission ${result ? 'granted' : 'denied'}'),
                                  backgroundColor: result ? Colors.green : Colors.orange,
                                ),
                              );
                            }
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error checking permissions: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Check/Request Notification Permission'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final hasExactAlarmPermission = await reminderService.checkExactAlarmPermissions();

                            if (!context.mounted) return;

                            if (hasExactAlarmPermission) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Exact alarm permission is granted'),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            } else {
                              final result = await reminderService.requestExactAlarmPermissions();

                              if (!context.mounted) return;

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Exact alarm permission ${result ? 'granted' : 'denied'}'),
                                  backgroundColor: result ? Colors.green : Colors.orange,
                                ),
                              );
                            }
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error checking exact alarm permissions: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Check/Request Exact Alarm Permission'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final pending = await reminderService.getPendingNotifications();

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Pending notifications: ${pending.length}'),
                                backgroundColor: Colors.blue,
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error getting pending notifications: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Check Pending Notifications'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final active = await reminderService.getActiveNotifications();

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Active notifications: ${active.length}'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error getting active notifications: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Check Active Notifications'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final success = await reminderService.testImmediateNotification();

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Test notification ${success ? 'successful' : 'failed'}'),
                                backgroundColor: success ? Colors.green : Colors.orange,
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error testing notification: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Test Immediate Notification'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final status = await reminderService.checkBatteryOptimizationStatus();

                            if (!context.mounted) return;

                            final canScheduleExact = status['canScheduleExactAlarms'] as bool;
                            final recommendations = status['recommendations'] as List<String>;

                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Battery Optimization Status'),
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Can schedule exact alarms: ${canScheduleExact ? 'Yes' : 'No'}'),
                                    const SizedBox(height: 16),
                                    if (recommendations.isNotEmpty) ...[
                                      const Text('Recommendations:', style: TextStyle(fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 8),
                                      ...recommendations.map((rec) => Padding(
                                        padding: const EdgeInsets.only(bottom: 4),
                                        child: Text('• $rec', style: const TextStyle(fontSize: 12)),
                                      )),
                                    ],
                                  ],
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    child: const Text('OK'),
                                  ),
                                ],
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error checking battery status: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Check Battery Optimization'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            final notificationId = await reminderService.testScheduledNotification(
                              delaySeconds: 30,
                              customTitle: 'Enhanced 30s Test',
                              customBody: 'This is an enhanced test of 30-second scheduling with better debugging',
                            );

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Enhanced 30s test scheduled (ID: $notificationId)'),
                                backgroundColor: Colors.blue,
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error scheduling enhanced test: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Enhanced 30s Test'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            // Test with alarmClock mode specifically
                            final notificationId = await reminderService.scheduleReminder(
                              title: 'AlarmClock Mode Test',
                              body: 'Testing with alarmClock mode for 15 seconds',
                              scheduledDate: DateTime.now().add(const Duration(seconds: 15)),
                              taskId: 'alarm-test-${DateTime.now().millisecondsSinceEpoch}',
                            );

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('AlarmClock test scheduled (ID: $notificationId)'),
                                backgroundColor: Colors.orange,
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error scheduling alarm test: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('15s AlarmClock Test'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            // Test with a very short delay that should work
                            final notificationId = await reminderService.scheduleReminder(
                              title: '3 Second Test',
                              body: 'This should work with Future.delayed',
                              scheduledDate: DateTime.now().add(const Duration(seconds: 3)),
                              taskId: 'short-test-${DateTime.now().millisecondsSinceEpoch}',
                            );

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('3s test scheduled (ID: $notificationId)'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error scheduling 3s test: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('3s Future.delayed Test'),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Local Notifications',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            await notificationService.showLocalNotification(
                              title: 'Test Notification',
                              body: 'This is a test local notification',
                            );

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Immediate notification sent'),
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error showing notification: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Show Immediate Notification'),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Task Reminders',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          final now = DateTime.now();
                          final scheduledTime = now.add(const Duration(seconds: 5));

                          try {
                            await reminderService.scheduleReminder(
                              title: '5 Second Reminder',
                              body: 'This reminder appeared 5 seconds after scheduling',
                              scheduledDate: scheduledTime,
                              taskId: 'test-task-1',
                            );

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Reminder scheduled for 5 seconds from now'),
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error scheduling reminder: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Schedule 5 Second Reminder'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          final now = DateTime.now();
                          final scheduledTime = now.add(const Duration(seconds: 30));

                          try {
                            await reminderService.scheduleReminder(
                              title: '30 Second Reminder',
                              body: 'This reminder appeared 30 seconds after scheduling',
                              scheduledDate: scheduledTime,
                              taskId: 'test-task-2',
                            );

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Reminder scheduled for 30 seconds from now'),
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error scheduling reminder: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Schedule 30 Second Reminder'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            await reminderService.cancelAllReminders();

                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('All reminders cancelled'),
                              ),
                            );
                          } catch (e) {
                            if (!context.mounted) return;

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error cancelling reminders: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: const Text('Cancel All Reminders'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}