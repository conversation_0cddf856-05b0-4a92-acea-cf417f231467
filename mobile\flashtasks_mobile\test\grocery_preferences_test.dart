import 'package:flutter_test/flutter_test.dart';
import 'package:flashtasks_mobile/src/features/settings/models/grocery_preferences.dart';

void main() {
  group('Grocery Preferences Models', () {
    test('GroceryPreferences should have correct default values', () {
      const preferences = GroceryPreferences();

      expect(preferences.defaultListType, 'personal');
      expect(preferences.defaultSharedListOwnerId, null);
      expect(preferences.autoSwitchToDefault, false);
      expect(preferences.showPersonalListInSidebar, true);
      expect(preferences.isPersonalDefault, true);
      expect(preferences.isSharedDefault, false);
    });

    test('GroceryPreferences should parse from JSON correctly', () {
      final json = {
        'defaultListType': 'shared',
        'defaultSharedListOwnerId': 'user123',
        'autoSwitchToDefault': true,
        'showPersonalListInSidebar': false,
      };

      final preferences = GroceryPreferences.fromJson(json);

      expect(preferences.defaultListType, 'shared');
      expect(preferences.defaultSharedListOwnerId, 'user123');
      expect(preferences.autoSwitchToDefault, true);
      expect(preferences.showPersonalListInSidebar, false);
      expect(preferences.isSharedDefault, true);
      expect(preferences.isPersonalDefault, false);
    });

    test('GroceryPreferences should convert to JSON correctly', () {
      const preferences = GroceryPreferences(
        defaultListType: 'shared',
        defaultSharedListOwnerId: 'user456',
        autoSwitchToDefault: true,
        showPersonalListInSidebar: false,
      );

      final json = preferences.toJson();

      expect(json['defaultListType'], 'shared');
      expect(json['defaultSharedListOwnerId'], 'user456');
      expect(json['autoSwitchToDefault'], true);
      expect(json['showPersonalListInSidebar'], false);
    });

    test('GroceryPreferences copyWith should work correctly', () {
      const original = GroceryPreferences(
        defaultListType: 'personal',
        autoSwitchToDefault: false,
      );

      final updated = original.copyWith(
        defaultListType: 'shared',
        defaultSharedListOwnerId: 'user789',
        autoSwitchToDefault: true,
      );

      expect(updated.defaultListType, 'shared');
      expect(updated.defaultSharedListOwnerId, 'user789');
      expect(updated.autoSwitchToDefault, true);
      expect(updated.showPersonalListInSidebar, true); // Should keep original value
    });

    test('UpdateGroceryPreferencesData should convert to JSON correctly', () {
      const updateData = UpdateGroceryPreferencesData(
        defaultListType: 'shared',
        defaultSharedListOwnerId: 'user123',
        autoSwitchToDefault: true,
      );

      final json = updateData.toJson();

      expect(json['defaultListType'], 'shared');
      expect(json['defaultSharedListOwnerId'], 'user123');
      expect(json['autoSwitchToDefault'], true);
      expect(json.containsKey('showPersonalListInSidebar'), false);
    });

    test('UpdateGroceryPreferencesData should only include non-null values', () {
      const updateData = UpdateGroceryPreferencesData(
        defaultListType: 'personal',
        // Other fields are null
      );

      final json = updateData.toJson();

      expect(json.length, 1);
      expect(json['defaultListType'], 'personal');
      expect(json.containsKey('defaultSharedListOwnerId'), false);
      expect(json.containsKey('autoSwitchToDefault'), false);
      expect(json.containsKey('showPersonalListInSidebar'), false);
    });

    test('GroceryPreferences equality should work correctly', () {
      const preferences1 = GroceryPreferences(
        defaultListType: 'shared',
        defaultSharedListOwnerId: 'user123',
        autoSwitchToDefault: true,
        showPersonalListInSidebar: false,
      );

      const preferences2 = GroceryPreferences(
        defaultListType: 'shared',
        defaultSharedListOwnerId: 'user123',
        autoSwitchToDefault: true,
        showPersonalListInSidebar: false,
      );

      const preferences3 = GroceryPreferences(
        defaultListType: 'personal',
        defaultSharedListOwnerId: 'user123',
        autoSwitchToDefault: true,
        showPersonalListInSidebar: false,
      );

      expect(preferences1, equals(preferences2));
      expect(preferences1, isNot(equals(preferences3)));
    });

    test('GroceryPreferences should handle null values in JSON', () {
      final json = {
        'defaultListType': null,
        'defaultSharedListOwnerId': null,
        'autoSwitchToDefault': null,
        'showPersonalListInSidebar': null,
      };

      final preferences = GroceryPreferences.fromJson(json);

      expect(preferences.defaultListType, 'personal'); // Default value
      expect(preferences.defaultSharedListOwnerId, null);
      expect(preferences.autoSwitchToDefault, false); // Default value
      expect(preferences.showPersonalListInSidebar, true); // Default value
    });
  });
}
