// File generated manually based on existing Firebase configuration
// Ideally, this should be generated using the FlutterFire CLI with:
// flutterfire configure

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default FirebaseOptions for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // Configuration for Android - extracted from google-services.json
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD8e6BVUqEq0SAA6kdmDHKRc3kc6bejNz4',
    appId: '1:759215079698:android:7caa716ac28a4c28ac73cb',
    messagingSenderId: '759215079698',
    projectId: 'flashtasks-8451f',
    storageBucket: 'flashtasks-8451f.firebasestorage.app',
  );

  // Configuration for iOS - needs to be updated with actual values
  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'YOUR_IOS_API_KEY',
    appId: 'YOUR_IOS_APP_ID',
    messagingSenderId: '759215079698',
    projectId: 'flashtasks-8451f',
    storageBucket: 'flashtasks-8451f.firebasestorage.app',
    iosClientId: 'YOUR_IOS_CLIENT_ID',
    iosBundleId: 'com.flashtasks.flashtasksMobile',
  );

  // Configuration for Web - needs to be updated with actual values if web support is needed
  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'YOUR_WEB_API_KEY',
    appId: 'YOUR_WEB_APP_ID',
    messagingSenderId: '759215079698',
    projectId: 'flashtasks-8451f',
    storageBucket: 'flashtasks-8451f.firebasestorage.app',
    authDomain: 'flashtasks-8451f.firebaseapp.com',
  );
}
