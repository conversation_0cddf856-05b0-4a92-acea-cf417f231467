import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../platform/platform_helper.dart';
import 'firebase_messaging_handler.dart';
import 'permission_service.dart';

/// Service for handling push notifications
class NotificationService {
  final PermissionService _permissionService;
  final FirebaseMessagingHandler _firebaseMessagingHandler = FirebaseMessagingHandler();
  
  // Key for storing FCM token in SharedPreferences
  static const String _tokenKey = 'fcm_token';
  
  NotificationService({required PermissionService permissionService})
      : _permissionService = permissionService;
  
  /// Initialize notification services
  Future<void> initialize() async {
    if (PlatformHelper.isWeb) {
      if (kDebugMode) {
        print('Web platform detected, skipping native notification setup');
      }
      return;
    }
    
    try {
      await _firebaseMessagingHandler.initialize();
      if (kDebugMode) {
        print('Firebase Messaging initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Firebase Messaging: $e');
      }
    }
  }
  
  /// Get the FCM token
  Future<String?> getDeviceToken() async {
    try {
      // Check if notification permission is granted first
      bool hasPermission = await _permissionService.isNotificationPermissionGranted();
      if (!hasPermission) {
        hasPermission = await _permissionService.requestNotificationPermission();
        if (!hasPermission) return null;
      }
      
      String? token = await _firebaseMessagingHandler.getToken();
      if (kDebugMode) {
        print('FCM Token: $token');
      }
      
      // Save token to SharedPreferences for later use (e.g., when unregistering)
      final prefs = await SharedPreferences.getInstance();
      if (token != null) {
        await prefs.setString(_tokenKey, token);
      }
          
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }
  
  /// Subscribe to a topic for receiving targeted notifications
  Future<void> subscribeToTopic(String topic) async {
    if (PlatformHelper.isWeb) return;
    
    try {
      await _firebaseMessagingHandler.subscribeToTopic(topic);
      if (kDebugMode) {
        print('Subscribed to topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error subscribing to topic $topic: $e');
      }
    }
  }
  
  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (PlatformHelper.isWeb) return;
    
    try {
      await _firebaseMessagingHandler.unsubscribeFromTopic(topic);
      if (kDebugMode) {
        print('Unsubscribed from topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from topic $topic: $e');
      }
    }
  }
  
  /// Show a local notification
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (PlatformHelper.isWeb) return;
    
    try {
      final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
          FlutterLocalNotificationsPlugin();
      
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'high_importance_channel',
        'High Importance Notifications',
        channelDescription: 'This channel is used for important notifications.',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );
      
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);
      
      await flutterLocalNotificationsPlugin.show(
        0, // Notification ID
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );
      
      if (kDebugMode) {
        print('Local notification shown: $title - $body');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error showing local notification: $e');
      }
    }
  }
}

/// Provider for the notification service
final notificationServiceProvider = Provider<NotificationService>((ref) {
  final permissionService = ref.watch(permissionServiceProvider);
  return NotificationService(permissionService: permissionService);
});

/// Provider for the FCM token
final fcmTokenProvider = FutureProvider<String?>((ref) async {
  final notificationService = ref.watch(notificationServiceProvider);
  return await notificationService.getDeviceToken();
});
