import 'package:flutter/foundation.dart' show kIsWeb;

// Conditionally import Platform class
import 'io_platform.dart' if (dart.library.html) 'web_platform.dart';

/// A helper class that safely provides platform information
/// whether the app is running on web or mobile
class PlatformHelper {
  // Private constructor to prevent instantiation
  PlatformHelper._();

  /// Check if the app is running on the web platform
  static bool get isWeb => kIsWeb;
  
  /// Get a descriptive string of the current platform
  static String getPlatformDescription() {
    if (kIsWeb) return 'Web';
    
    if (isAndroid) {
      return 'Android $operatingSystemVersion';
    } else if (isIOS) {
      return 'iOS $operatingSystemVersion';
    } else {
      return operatingSystem;
    }
  }
  
  /// Check if the app is running on Android
  static bool get isAndroid => !kIsWeb && isAndroidPlatform();
  
  /// Check if the app is running on iOS
  static bool get isIOS => !kIsWeb && isIOSPlatform();
} 