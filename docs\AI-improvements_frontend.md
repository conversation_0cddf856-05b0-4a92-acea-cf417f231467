Okay, this is a great challenge! To make the AI component more robust, intuitive, and capture the "flash" essence for task creation, we'll focus on a few key areas:

1.  **Enhanced Prompt Engineering:** Making the AI understand varied inputs better and extract richer, more structured information.
2.  **Contextual Post-Processing:** Using the AI's output and other available context (like user settings, time) in the backend to refine task details.
3.  **Smarter Priority & Naming Heuristics:** Implementing more nuanced logic for determining priority and generating intuitive task names/descriptions.
4.  **Handling Typos/Voice Errors with Review:** Adding a mechanism for AI to correct obvious input errors and for the user to review these corrections.

Let's break down the proposed changes.

**Phase 1: Backend - AI Prompt & Model Updates**

**1. Update `aiPrompts.yaml` (Backend)**

The `processQuickAdd` prompt is central. We'll instruct the AI to:
*   Identify and correct obvious spelling/typos and report these corrections.
*   Generate more nuanced titles (concise vs. summary) and descriptions.
*   Provide a confidence score and an optional clarification question for its parsing.
*   Follow more detailed priority logic.

```yaml
// backend/src/prompts/aiPrompts.yaml
# AI Prompts for Task OrganAIzer

system_message: "You are a helpful assistant integrated into the Task OrganAIzer application."

prompts:
  analyzeContent:
    description: "Analyzes task content to suggest relevant categories from a predefined list."
    system_message: "You are an AI assistant that analyzes task content and suggests relevant categories from a predefined list. Respond ONLY with a comma-separated list of suggested category names. Do not include any other text, explanations, or formatting."
    template: |
      Existing Categories: {{categoryNamesList}}
      Task Content: "{{taskContent}}"
      Suggested Categories:

  processQuickAdd:
    description: "Processes natural language input for quick task or grocery creation. Includes spelling/typo correction with reporting. Focuses on intuitive naming, description, priority, and capturing user intent accurately."
    system_message: |
      You are an expert task organizing AI. Your goal is to meticulously parse user input into structured task objects or grocery items.
      IMPORTANT INSTRUCTIONS:

      1.  SPELLING & TYPO CORRECTION (Perform this FIRST for each segment):
          Before parsing, review the `original_utterance_segment` for obvious spelling mistakes, typos, or common voice recognition errors (e.g., "remind me too call" -> "remind me to call", "tomorow" -> "tomorrow").
          If you make a correction:
          - Set `was_auto_corrected` to `true`.
          - Provide the `original_text_before_correction` (the segment with the error).
          - Provide the `corrected_text_segment` (the segment after your correction).
          Use the `corrected_text_segment` for all subsequent parsing steps (title, content generation, etc.).
          If no correction was made, set `was_auto_corrected` to `false`, and `original_text_before_correction` and `corrected_text_segment` to `null`.
          Focus on clear, unambiguous errors. Do NOT change phrasing or meaning beyond correcting obvious mistakes.

      2.  MULTI-TASK SEGMENTATION:
          Carefully check for multiple distinct tasks in a single input. Split input into separate tasks based on sequential indicators ("then", "after"), conjunctions ("and", "also"), lists, or clear contextual shifts.
          For EACH task identified, provide its `original_utterance_segment`.

      3.  PER-TASK ATTRIBUTE EXTRACTION & INTUITIVE NAMING (using corrected text if applicable):
          For EACH task, extract/generate:
          - `intent` (string): "task" or "grocery".
          - `confidence_level` (string: "High" | "Medium" | "Low"): Your confidence in parsing this specific task accurately. "Low" if significant assumptions were made.
          - `original_utterance_segment` (string): The part of the user's input corresponding to this task (before any correction).
          - `was_auto_corrected` (boolean): True if you corrected spelling/typos in this segment.
          - `original_text_before_correction` (string or null): The text segment *before* your correction.
          - `corrected_text_segment` (string or null): The text segment *after* your correction.
          - `title` (string): A CONCISE, ACTIONABLE title (max 5-7 words, from corrected text if applicable). Capitalize only the first word unless a proper noun. If input is verbose, summarize effectively.
          - `summary_title` (string or null): A slightly more descriptive title (max 10-12 words, from corrected text) if the main `title` had to be very short for conciseness, or if it adds key context (like a project name). Otherwise null.
          - `content` (string or null): The `corrected_text_segment` IF it was significantly rephrased for the title and offers more detail, OR the `original_utterance_segment` (if no correction or if it provides necessary full context). If the title captures everything, this can be null.
          - `parsed_description` (string or null): If you generated a specific, structured description (from corrected text), provide it here. Often, this can be the `corrected_text_segment` itself if it's well-formed after correction. Otherwise null.
          - `categoryNames` (array of strings or empty array): Categories from `User's existing categories`.
          - `locationName` (string or null): Location from `User's existing locations`.
          - `deadline` (string: "YYYY-MM-DD" or null): Use `{{currentDateTime}}` as reference.
          - `time` (string: "HH:MM" 24-hour format or null).
          - `priority` (string: "Low" | "Medium" | "High" | "Critical" | null):
              Infer priority based on:
              - Urgency keywords: "urgent", "ASAP", "immediately", "critical", "important by [time/date]".
              - Deadline proximity: "Critical" for same-day/very soon deadlines (e.g., "by tonight", "in 2 hours"). "High" for next-day deadlines or strong urgency cues.
              - Task nature if obvious (e.g., "Pay bills" often implies importance).
              - Default to "Medium" if unclear. If "tonight" or "this evening" is mentioned and `{{currentDateTime}}` indicates it's already afternoon/evening, lean towards "High" or "Critical".
          - `reminders` (array of objects or empty array): Each object: `{"offsetValue": number, "offsetUnit": "minutes"|"hours"|"days"|"weeks"}`.
          - `clarification_needed` (string or null): If a key detail is missing or ambiguous for THIS task (e.g., user says "schedule meeting" -> clarification: "What is the meeting about?"), pose a concise question.

      4.  IMPLICIT PROJECT SUGGESTION:
          If tasks collectively suggest a larger project, populate `suggestedProjectTitle` (string or null).

      5.  OUTPUT JSON STRUCTURE:
          Respond ONLY with a single valid JSON object: `{"suggestedProjectTitle": "string_or_null", "tasks": [task_object_or_grocery_object]}`.

      6.  GROCERY HANDLING:
          If input is for groceries, the `tasks` array should contain a SINGLE object:
          `{ "intent": "grocery", "confidence_level": "High", "original_utterance_segment": "full_grocery_input", "was_auto_corrected": boolean, "original_text_before_correction": "string_or_null", "corrected_text_segment": "string_or_null", "items": [{ "name": "item name (corrected if applicable)", "quantity": "quantity_or_null" }, ...] }`
          `suggestedProjectTitle` MUST be `null`.

      7.  TIME & DATE INTERPRETATION (use `{{currentDateTime}}` as reference): (Same as previous version)
      8.  AMBIGUITY & SHORT INPUTS: (Same as previous version)

    template: |
      User Input: "{{quickInputText}}"
      Current date and time: {{currentDateTime}}
      User's existing categories: [{{#if categoryNamesList}}{{categoryNamesList}}{{/if}}]
      User's existing locations: [{{#if locationNamesList}}{{locationNamesList}}{{/if}}]

      Analyze the input and respond ONLY with a single valid JSON object.

      Example - Task with Autocorrection:
      {
        "suggestedProjectTitle": null,
        "tasks": [
          {
            "intent": "task",
            "confidence_level": "High",
            "original_utterance_segment": "Shedule meeting with marketing team for tomorow at 2pm",
            "was_auto_corrected": true,
            "original_text_before_correction": "Shedule meeting with marketing team for tomorow at 2pm",
            "corrected_text_segment": "Schedule meeting with marketing team for tomorrow at 2pm",
            "title": "Meeting (marketing team)",
            "summary_title": "Schedule meeting with marketing team for tomorrow at 2pm",
            "content": "Schedule meeting with marketing team for tomorrow at 2pm",
            "parsed_description": null,
            "categoryNames": ["Work"],
            "locationName": "Office",
            "deadline": "{{resolved_tomorrow_date}}",
            "time": "14:00",
            "priority": "Medium",
            "reminders": [],
            "clarification_needed": null
          }
        ]
      }
  suggestTaskPriority: # ... (no changes needed here for autocorrect, but review if priority logic needs update)
    # ...
  askChatbot_Initial: # ...
  askChatbot_WebSearch: # ...
  insight_NewCategorySuggestion: # ...
  groceryCategorization: # ...
```

**2. Update Task Model (`Task.ts` - Backend)**
   Ensure these fields are present in your `ITask` interface, specifically within the `metadata` object:
   *   `aiWasAutoCorrected?: boolean;`
   *   `aiOriginalTextSegment?: string;`
   *   `aiCorrectedTextSegment?: string;`

   (You've already done this based on the previous step's `Task.ts` file).

**3. Update Insight Model (`Insight.ts` - Backend)**
   Ensure the `AI_AUTOCORRECTION_REVIEW` type and its specific `relatedData` fields (`originalText`, `correctedText`, `targetTaskId`, `taskTitle`) are present.

   (You've already done this based on the previous step's `Insight.ts` file).

**4. Update `aiController.ts` (Backend)**
   The logic provided in the previous step for `processQuickAdd` already includes:
    *   Extracting `aiWasAutoCorrected`, `aiOriginalTextBeforeCorrection`, `aiCorrectedTextSegment` from the AI's JSON.
    *   Storing these in `task.metadata`.
    *   Using the `aiCorrectedTextSegment` (or `aiContent` if it's based on corrected text) for `finalTitle` and `finalContent`.
    *   Creating the `AI_AUTOCORRECTION_REVIEW` insight if `aiWasAutoCorrected` is true.
    *   Creating the `CONFIRM_AI_INTERPRETATION` insight for low/medium confidence or if clarification is needed.

   **No major structural changes are needed here from the last iteration, but double-check:**
    *   **Content Source:** Ensure `finalContent` preferentially uses `aiCorrectedTextSegment` or `aiParsedDescription` (if AI made corrections/structured it) over `aiOriginalUtterance` when `aiWasAutoCorrected` is true. The prompt guides the AI to use corrected text for content, so `aiContent` or `aiParsedDescription` should already reflect this.
    *   The existing `selectBestTitle` and `selectBestDescription` helpers should work with the AI's output (which is supposed to be based on corrected text).

**II. Flutter Frontend Implementation Plan (Integrating, Not Replacing)**

The goal is to add new UI elements and logic for the new insight types without breaking the existing UI for other insight types.

**1. Data Model Updates (Flutter)**

*   **`lib/src/features/tasks/models/task.dart`**:
    *   Ensure `TaskMetadata` class includes:
        ```dart
        // lib/src/features/tasks/models/task.dart -> TaskMetadata
        final bool? aiWasAutoCorrected;
        final String? aiOriginalTextSegment;
        final String? aiCorrectedTextSegment;
        // ... other AI metadata fields
        ```
    *   Update `TaskMetadata.fromJson` to parse these new fields.
    *   Ensure `TaskListItem` and `TaskDetail` correctly instantiate and pass `TaskMetadata`.

*   **`lib/src/features/insights/models/insight.dart`**:
    *   Add `aiAutocorrectionReview` and `confirmAiInterpretation` to the `InsightType` enum.
    *   Update `Insight.fromJson`'s `_parseInsightType` to handle these new string values from the backend.
    *   Ensure the `relatedData` dynamic map in the `Insight` class can be accessed for `originalText`, `correctedText`, `taskTitle`, `targetTaskId`, `originalInput`, `aiConfidence`, `aiClarificationNeeded`. Add type-safe getters if preferred (as shown in the previous response).

**2. API Service Layer Updates (Flutter)**

*   **`lib/src/core/services/task_service.dart`**:
    *   The `updateTask` method will be used to revert corrections. It needs to accept `title` and/or `content` in its `taskData` map. The current implementation is generic enough.

*   **`lib/src/features/insights/providers/insight_provider.dart`**:
    *   The `InsightNotifier` needs a new method to handle reverting AI corrections.
        ```dart
        // lib/src/features/insights/providers/insight_provider.dart
        // ... (imports, make sure TaskService is imported)

        class InsightNotifier extends StateNotifier<InsightState> {
          final AIService _aiService;
          final TaskService _taskService; // Ensure TaskService is injected

          InsightNotifier({required AIService aiService, required TaskService taskService})
              : _aiService = aiService,
                _taskService = taskService, // Store it
                super(InsightState.initial());

          // ... (existing methods: fetchInsights, handleAction, etc.)

          // New method to revert AI autocorrection
          Future<void> revertAutocorrection({
            required String insightId,
            required String taskId,
            required String originalText,
            required String correctedText, // The text AI put into the task
            required String currentTaskTitle, // For context in notifications
            String? currentTaskContent, // For deciding which field to revert
          }) async {
            // Optimistically remove the insight, or mark it as loading
            final originalInsights = List<Insight>.from(state.insights);
            state = state.copyWith(
                insights: state.insights.where((i) => i.id != insightId).toList(),
                isLoading: true // Or a specific loading state for this insight
            );

            try {
              final Map<String, dynamic> updates = {};

              // Heuristic to decide which field to update
              // If the backend guarantees which field was corrected, use that info.
              // Otherwise, a simple heuristic:
              if (correctedText == currentTaskTitle) {
                updates['title'] = originalText;
                // If originalText is long, it might have been content that AI summarized into title
                if (originalText.length > 70 && (currentTaskContent == null || currentTaskContent.isEmpty || currentTaskContent == correctedText)) {
                   updates['content'] = originalText; // Also set content if it seems appropriate
                }
              } else if (currentTaskContent != null && correctedText == currentTaskContent) {
                updates['content'] = originalText;
              } else {
                // Fallback: Assume title was the primary target of correction
                updates['title'] = originalText;
                // If original text is long, set it as content as well
                if (originalText.length > 70) {
                  updates['content'] = originalText;
                }
              }
              
              if (updates.isEmpty) {
                print('[InsightProvider] No specific field identified for reverting correction. Defaulting to dismissing insight.');
              } else {
                 await _taskService.updateTask(taskId, updates);
                 print('[InsightProvider] Task $taskId updated with original text.');
              }


              // Dismiss the insight on the backend IF the update was successful
              // (handleAction already removes it from local state optimistically if success)
              await _aiService.handleInsightAction(insightId, 'dismissed');

              // Refresh insights to get the latest list after dismissal
              // This also sets isLoading to false.
              await fetchInsights();

            } catch (e) {
              print('[InsightProvider] Error reverting autocorrection for insight $insightId: $e');
              state = state.copyWith(
                insights: originalInsights, // Revert optimistic removal
                isLoading: false,
                error: 'Failed to revert correction: ${e.toString()}',
              );
              // Rethrow or show a toast to the user
            }
          }
        }

        // Update the provider instantiation
        final insightProvider = StateNotifierProvider<InsightNotifier, InsightState>((ref) {
          final aiService = ref.watch(aiServiceProvider);
          final taskService = ref.watch(taskServiceProvider); // Add this
          return InsightNotifier(aiService: aiService, taskService: taskService); // Pass it
        });
        ```

**3. UI Implementation (Flutter)**

*   **`lib/src/features/tasks/widgets/task_list_item_widget.dart`**:
    *   **Visual Cues:**
        *   If `task.metadata.aiWasAutoCorrected == true`: Add a small icon (e.g., `LucideIcons.edit3` or `LucideIcons.sparkles` with `Colors.blueAccent.withOpacity(0.7)`). On hover/long-press, show a tooltip: "AI corrected this text. Original: '${task.metadata.aiOriginalTextSegment}'. Corrected: '${task.metadata.aiCorrectedTextSegment}'".
        *   If `task.metadata.aiClarificationNeeded != null`: Add `Icon(LucideIcons.helpCircle, size: 12, color: Colors.orangeAccent)`. Tooltip: `AI needs clarification: "${task.metadata.aiClarificationNeeded}"`.
        *   If `task.metadata.aiConfidence == 'Low'`: Consider a subtle visual cue (e.g., slightly different border, or another small icon). This might be too noisy if many tasks are low confidence.

*   **`lib/src/features/insights/widgets/insight_card_widget.dart`**:
    *   **New Prop:** Add `final Function(String taskId, String insightId, String originalText, String correctedText, String currentTaskTitle, String? currentTaskContent)? onRevertCorrection;`
    *   **Modify `_getInsightIcon`, `_getTitle`, `_getBackgroundColor`, `_getBorderColor`**:
        *   Add cases for `InsightType.aiAutocorrectionReview` (e.g., Icon: `LucideIcons.edit3`, Title: "AI Text Correction", Colors: Sky blue theme).
        *   Add cases for `InsightType.confirmAiInterpretation` (e.g., Icon: `LucideIcons.brain`, Title: "Review AI-Generated Task", Colors: Teal theme).
    *   **Modify `_buildRelatedContext`**:
        *   Render the `originalText` and `correctedText` for `AI_AUTOCORRECTION_REVIEW`.
        *   Render `originalInput`, `aiConfidence`, and `aiClarificationNeeded` for `CONFIRM_AI_INTERPRETATION`.
    *   **Modify `_buildSpecificActionButtons` (Crucial Part):**
        *   This function needs to be the central place for deciding which buttons appear for each insight type.
        *   **For `InsightType.aiAutocorrectionReview`**:
            *   **Keep Correction Button:** Label "Keep". Action: `_handleDismiss(ref)` (because the task already has the corrected text, we just dismiss the review insight). Style as primary positive action (e.g., green).
            *   **Revert Button:** Label "Revert". Action: Calls `widget.onRevertCorrection!(insight.id, insight.targetTaskId!, insight.originalText!, insight.correctedText!, insight.taskTitle!, task.content);` (You might need to fetch `task.content` if not readily available).
            *   **Edit Manually Button:** Label "Edit". Action: `if (widget.onEditTask != null && insight.targetTaskId != null) { widget.onEditTask!(insight.targetTaskId!); _handleDismiss(ref); }`.
        *   **For `InsightType.confirmAiInterpretation`**:
            *   **Looks Good Button:** Label "Looks Good". Action: `_handleAccept(ref)` (Accept here means dismiss, as task is deemed correct). Style as primary positive action.
            *   **Edit Task Button:** Label "Edit". Action: `if (widget.onEditTask != null && insight.targetTaskId != null) { widget.onEditTask!(insight.targetTaskId!); _handleDismiss(ref); }`.
            *   **Delete Task Button:** Label "Delete Task". Action: `_handleDeleteTask(context, ref, insight.targetTaskId!)`. Style as destructive.
        *   **Existing Insight Types:**
            *   The existing `ElevatedButton` for "Accept" and `TextButton` for "Dismiss" should *only* render if the insight type is one of the simpler ones (NEW\_CATEGORY\_SUGGESTION, PRIORITY\_CHECK, etc.) and *not* for the new `AI_AUTOCORRECTION_REVIEW` or `CONFIRM_AI_INTERPRETATION` or `OVERDUE_TASK_NOTIFICATION` etc., as these have their own specific button sets.
            *   Ensure the `onMarkComplete`, `onChangeDeadline`, `onDeleteTask` (for Overdue), `onReviewDuplicates`, `onMergeTasks` buttons still render correctly for their respective insight types.

        **Refined Logic for Action Buttons in `InsightCardWidget`:**

        ```dart
        // In InsightCardWidget's build method, replace the simple Accept/Dismiss buttons in the footer with:
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Wrap( // Use Wrap for better responsiveness if many buttons
            alignment: WrapAlignment.end,
            spacing: 8.0,
            runSpacing: 4.0,
            children: _buildActionButtonsForInsightType(context, theme, ref),
          ),
        ),

        // New method in InsightCardWidget:
        List<Widget> _buildActionButtonsForInsightType(BuildContext context, ThemeData theme, WidgetRef ref) {
          List<Widget> actions = [];

          switch (insight.type) {
            case InsightType.aiAutocorRECTION_REVIEW:
              final targetTaskId = insight.targetTaskId;
              final originalText = insight.originalText;
              final correctedText = insight.correctedText;
              final taskTitle = insight.taskTitle;
              // Note: currentTaskContent would ideally be passed or fetched if needed for smart revert.
              // For simplicity, we'll let the provider handle fetching if necessary.
              if (targetTaskId != null && originalText != null && correctedText != null) {
                actions.add(
                  ElevatedButton.icon(
                    icon: const Icon(LucideIcons.check, size: 16),
                    label: const Text('Keep'),
                    onPressed: isLoading ? null : () => _handleDismiss(ref), // Keep correction = dismiss review
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green[700]),
                  )
                );
                actions.add(
                  TextButton.icon(
                    icon: const Icon(LucideIcons.rotateCcw, size: 16),
                    label: const Text('Revert'),
                    onPressed: isLoading ? null : () {
                      // Assuming onRevertCorrection prop is added to InsightCardWidget
                      if (widget.onRevertCorrection != null) {
                         widget.onRevertCorrection!(targetTaskId, insight.id, originalText, correctedText, taskTitle ?? "Task", null /* pass content if available */);
                      } else { // Fallback if prop not passed, call provider directly
                         ref.read(insightProvider.notifier).revertAutocorrection(
                            insightId: insight.id,
                            taskId: targetTaskId,
                            originalText: originalText,
                            correctedText: correctedText,
                            currentTaskTitle: taskTitle ?? "Task",
                            currentTaskContent: null // Or fetch if necessary
                         );
                      }
                      if (widget.onActionComplete != null) widget.onActionComplete!();
                    },
                  )
                );
                actions.add(
                  TextButton.icon(
                    icon: const Icon(LucideIcons.edit3, size: 16),
                    label: const Text('Edit'),
                    onPressed: isLoading ? null : () {
                      if (widget.onEditTask != null) widget.onEditTask!(targetTaskId);
                      _handleDismiss(ref);
                    },
                  )
                );
              }
              break;

            case InsightType.confirmAiInterpretation:
              final targetTaskId = insight.targetTaskId;
              if (targetTaskId != null) {
                actions.add(
                  ElevatedButton.icon(
                    icon: const Icon(LucideIcons.check, size: 16),
                    label: const Text('Looks Good'),
                    onPressed: isLoading ? null : () => _handleAccept(ref),
                  )
                );
                actions.add(
                  TextButton.icon(
                    icon: const Icon(LucideIcons.edit3, size: 16),
                    label: const Text('Edit Task'),
                    onPressed: isLoading ? null : () {
                      if (widget.onEditTask != null) widget.onEditTask!(targetTaskId);
                      _handleDismiss(ref);
                    },
                  )
                );
                actions.add(
                  TextButton.icon(
                    icon: const Icon(LucideIcons.trash2, size: 16),
                    label: Text('Delete Task', style: TextStyle(color: theme.colorScheme.error)),
                    onPressed: isLoading ? null : () => _handleDeleteTask(context, ref, targetTaskId),
                    style: TextButton.styleFrom(foregroundColor: theme.colorScheme.error),
                  )
                );
              }
              break;

            // --- Cases for EXISTING insight types ---
            case InsightType.overdueTaskNotification:
              // ... (your existing buttons for overdue tasks: Mark Complete, Change Deadline, Delete Task)
              // Make sure to include a general "Dismiss" if those actions don't inherently dismiss.
              // For example, "Delete Task" should probably also dismiss the insight.
              final targetTaskId = insight.targetTaskId;
               if (targetTaskId != null) {
                   actions.addAll([
                       TextButton.icon(icon: const Icon(LucideIcons.checkSquare), label: const Text('Mark Complete'), onPressed: isLoading ? null : () => _handleMarkComplete(ref, targetTaskId)),
                       TextButton.icon(icon: const Icon(LucideIcons.calendar), label: const Text('Change Due'), onPressed: isLoading ? null : () => _handleChangeDeadline(context, ref, targetTaskId)),
                       TextButton(icon: const Icon(LucideIcons.trash2, color: Colors.red), label: const Text('Delete Task', style: TextStyle(color: Colors.red)), onPressed: isLoading ? null : () => _handleDeleteTask(context, ref, targetTaskId)),
                   ]);
               }
              // Add a general dismiss if actions above don't dismiss
              actions.add(TextButton(onPressed: isLoading ? null : () => _handleDismiss(ref), child: const Text('Dismiss Only')));
              break;

            case InsightType.duplicateTaskDetected:
              final taskIds = (insight.relatedData['taskIds'] as List<dynamic>?)?.cast<String>() ?? [];
              if (taskIds.isNotEmpty) {
                  actions.add(
                      TextButton.icon(
                          icon: const Icon(LucideIcons.copy),
                          label: const Text('Review Duplicates'),
                          onPressed: isLoading ? null : () => _handleReviewDuplicates(context, ref, taskIds),
                      )
                  );
              }
              // Add a general dismiss
              actions.add(TextButton(onPressed: isLoading ? null : () => _handleDismiss(ref), child: const Text('Dismiss')));
              break;

            case InsightType.taskMergeSuggestion:
              final taskIdsMerge = (insight.relatedData['taskIds'] as List<dynamic>?)?.cast<String>() ?? [];
              if (taskIdsMerge.isNotEmpty) {
                  actions.add(
                      TextButton.icon(
                          icon: const Icon(LucideIcons.merge),
                          label: const Text('Merge Tasks'),
                          onPressed: isLoading ? null : () => _handleMergeTasks(context, ref, taskIdsMerge),
                      )
                  );
              }
              // Add a general dismiss
              actions.add(TextButton(onPressed: isLoading ? null : () => _handleDismiss(ref), child: const Text('Dismiss')));
              break;

            // Default "Accept Suggestion" and "Dismiss" for simpler insights
            case InsightType.newCategorySuggestion:
            case InsightType.priorityCheck:
            case InsightType.recurringTaskSuggestion:
            case InsightType.categoryCleanup:
              actions.add(TextButton(onPressed: isLoading ? null : () => _handleDismiss(ref), child: const Text('Dismiss')));
              actions.add(ElevatedButton.icon(
                icon: const Icon(LucideIcons.check, size: 16),
                label: const Text('Accept Suggestion'),
                onPressed: isLoading ? null : () => _handleAccept(ref),
              ));
              break;

            default: // For 'unknown' or any other types not explicitly handled
              actions.add(TextButton(onPressed: isLoading ? null : () => _handleDismiss(ref), child: const Text('Dismiss')));
          }
          return actions;
        }
        ```

4.  **`lib/src/features/insights/screens/insights_screen.dart`**
    *   Ensure the `onEditTask` prop is passed to `AiInsightsListWidget`.
    *   The `_handleInsightActionComplete` method should call `ref.read(taskProvider.notifier).refresh()` if an insight action (like revert) modified a task.

5.  **`lib/src/features/tasks/screens/task_form_screen.dart`**
    *   When editing (`widget.isEditing == true` and `taskDetailAsync.when(data: (task) ...)`):
        *   Inside the `data` callback, before setting form controllers:
            ```dart
            // lib/src/features/tasks/screens/task_form_screen.dart
            // In build() method, inside taskDetailAsync.when(data: (task) { ... })
            final metadata = task.metadata; // Assuming TaskDetail has metadata
            if (metadata.aiWasAutoCorrected == true && metadata.aiOriginalTextSegment != null && metadata.aiCorrectedTextSegment != null) {
              // Use WidgetsBinding.instance.addPostFrameCallback to show SnackBar after build
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) { // Check if widget is still mounted
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('AI applied a correction to this task\'s text:'),
                          Text('Original: "${metadata.aiOriginalTextSegment}"', style: const TextStyle(fontStyle: FontStyle.italic, fontSize: 12)),
                          Text('Corrected: "${metadata.aiCorrectedTextSegment}"', style: const TextStyle(fontStyle: FontStyle.italic, fontSize: 12)),
                        ],
                      ),
                      action: SnackBarAction(
                        label: 'Use Original',
                        onPressed: () {
                          setState(() {
                            // Heuristic: if original is much shorter, assume it was title
                            if (metadata.aiOriginalTextSegment!.length < 50 && metadata.aiCorrectedTextSegment == _titleController.text) {
                               _titleController.text = metadata.aiOriginalTextSegment!;
                            } else {
                               _descriptionController.text = metadata.aiOriginalTextSegment!;
                            }
                            // Clear the metadata flag locally to prevent re-showing this
                            // This doesn't save to backend yet, just for UI state
                            task.metadata.aiWasAutoCorrected = false;
                          });
                        },
                      ),
                      duration: const Duration(seconds: 10), // Longer duration for review
                    ),
                  );
                }
              });
            }
            if (metadata.aiClarificationNeeded != null) {
               WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('AI needs clarification: "${metadata.aiClarificationNeeded}"'),
                      duration: const Duration(seconds: 8),
                    ),
                  );
                }
               });
            }
            // ... rest of _initializeForm logic using task
            ```

This approach keeps the core action-handling logic within the `InsightCardWidget` and the state updates within the `InsightNotifier`. The `AiInsightsListWidget` and main screens act as containers passing down necessary callbacks. Remember to thoroughly test the UI flows for each insight type to ensure the correct buttons appear and actions behave as expected.