import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
// Explicitly import all needed components from record package
import 'package:record/record.dart' show AudioRecorder, RecordConfig, AudioEncoder, Amplitude;

/// A service for recording audio with amplitude tracking for waveform visualization
class AudioRecorderService {
  late final AudioRecorder _audioRecorder; // Create AudioRecorder in initialize method
  final _amplitudeStreamController = StreamController<double>.broadcast();
  Timer? _amplitudeTimer;
  String? _recordedFilePath;
  bool _isRecording = false;
  
  AudioRecorderService() {
    // Constructor for any initial setup
  }

  /// Stream of amplitude values that can be used for waveform visualization
  Stream<double> get amplitudeStream => _amplitudeStreamController.stream;

  /// Path to the recorded audio file
  String? get recordedFilePath => _recordedFilePath;

  /// Whether the service is currently recording
  bool get isRecording => _isRecording;

  /// Initialize the audio recorder and check for permissions
  Future<bool> initialize() async {
    try {
      // Create the recorder instance
      _audioRecorder = AudioRecorder();
      
      final hasPermission = await _audioRecorder.hasPermission();
      if (!hasPermission) {
        debugPrint('Audio recorder: Microphone permission denied');
        return false;
      }
      return true;
    } catch (e) {
      debugPrint('Audio recorder: Error initializing - $e');
      return false;
    }
  }

  /// Start recording audio
  Future<bool> startRecording() async {
    if (_isRecording) {
      debugPrint('Audio recorder: Already recording');
      return true;
    }

    try {
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/audio_recording_${DateTime.now().millisecondsSinceEpoch}.m4a';
      
      debugPrint('Audio recorder: Starting recording to $filePath');
      
      // Configure recording options
      const recorderConfig = RecordConfig(
        encoder: AudioEncoder.aacLc, // Most compatible
        bitRate: 128000,
        sampleRate: 44100,
      );
      
      // Start recording with the configuration
      await _audioRecorder.start(recorderConfig, path: filePath);
      
      _recordedFilePath = filePath;
      _isRecording = true;
      
      // Start amplitude monitoring for waveform visualization
      _startAmplitudeMonitoring();
      
      return true;
    } catch (e) {
      debugPrint('Audio recorder: Failed to start recording - $e');
      return false;
    }
  }

  /// Stop recording and return the file path
  Future<String?> stopRecording() async {
    if (!_isRecording) {
      debugPrint('Audio recorder: Not recording');
      return _recordedFilePath;
    }
    
    _stopAmplitudeMonitoring();
    
    try {
      await _audioRecorder.stop();
      _isRecording = false;
      debugPrint('Audio recorder: Recording stopped - $_recordedFilePath');
      return _recordedFilePath;
    } catch (e) {
      debugPrint('Audio recorder: Error stopping recording - $e');
      _isRecording = false;
      return null;
    }
  }

  /// Cancel recording and delete the file
  Future<void> cancelRecording() async {
    if (!_isRecording) {
      return;
    }
    
    _stopAmplitudeMonitoring();
    
    try {
      await _audioRecorder.stop();
      
      // Delete the recording file
      if (_recordedFilePath != null) {
        final file = File(_recordedFilePath!);
        if (await file.exists()) {
          await file.delete();
          debugPrint('Audio recorder: Deleted recording file $_recordedFilePath');
        }
      }
      
      _recordedFilePath = null;
      _isRecording = false;
    } catch (e) {
      debugPrint('Audio recorder: Error canceling recording - $e');
    }
  }

  /// Start monitoring the audio amplitude for waveform visualization
  void _startAmplitudeMonitoring() {
    _amplitudeTimer?.cancel();
    _amplitudeTimer = Timer.periodic(const Duration(milliseconds: 100), (_) async {
      if (_isRecording) {
        try {
          // In the latest record package, getAmplitude() returns an Amplitude object with max and current values
          final amplitude = await _audioRecorder.getAmplitude();
          final double currentAmplitude = amplitude.current ?? -60.0; // Default to -60 dB if null
          
          // Normalize amplitude between 0.0 and 1.0
          // dB values are typically negative, with 0 being max and -60 being silence
          // Typically values range from -60 dB (silence) to 0 dB (loudest)
          final double normalizedValue = math.max(0.0, 1.0 + (currentAmplitude / 60.0));
          
          // Add some random noise for more visual interest when volume is very low
          final double displayValue = normalizedValue < 0.1 
              ? normalizedValue + (0.05 * math.Random().nextDouble())
              : normalizedValue;
          
          _amplitudeStreamController.add(displayValue);
        } catch (e) {
          // Just ignore amplitude errors, they're not critical
          if (kDebugMode) {
            print('Error getting amplitude: $e');
          }
          
          // Send a small random value to keep the visualization active
          final double fallbackValue = 0.05 + (0.05 * math.Random().nextDouble());
          _amplitudeStreamController.add(fallbackValue);
        }
      }
    });
  }

  /// Stop amplitude monitoring
  void _stopAmplitudeMonitoring() {
    _amplitudeTimer?.cancel();
    _amplitudeTimer = null;
  }

  /// Dispose of all resources
  void dispose() {
    _stopAmplitudeMonitoring();
    _amplitudeStreamController.close();
    _audioRecorder.dispose();
  }
}
