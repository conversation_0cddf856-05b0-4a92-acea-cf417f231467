import 'package:flutter_test/flutter_test.dart';
import 'package:flashtasks_mobile/src/features/groceries/models/grocery_list.dart';
import 'package:flashtasks_mobile/src/features/groceries/models/grocery_invitation.dart';
import 'package:flashtasks_mobile/src/features/groceries/models/grocery_item.dart';

void main() {
  group('Shared Groceries Models', () {
    test('GroceryList should parse collaboration data correctly', () {
      final json = {
        '_id': 'list123',
        'userId': 'user123',
        'name': 'Family Groceries',
        'isShared': true,
        'collaborators': [
          {
            'userId': {
              '_id': 'user456',
              'name': '<PERSON>',
              'email': '<EMAIL>',
            },
            'role': 'editor',
            'joinedAt': '2024-01-01T00:00:00.000Z',
            'invitedBy': {
              '_id': 'user123',
              'name': '<PERSON>',
              'email': '<EMAIL>',
            },
          }
        ],
        'shareSettings': {
          'allowCollaboratorInvites': true,
          'requireApprovalForEdits': false,
          'notifyOnChanges': true,
        },
        'lastCollaborativeActivity': '2024-01-01T12:00:00.000Z',
        'collaboratorCount': 2,
        'userRole': 'owner',
        'itemCounts': {
          'total': 10,
          'checked': 3,
          'unchecked': 7,
        },
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T12:00:00.000Z',
      };

      final groceryList = GroceryList.fromJson(json);

      expect(groceryList.id, 'list123');
      expect(groceryList.name, 'Family Groceries');
      expect(groceryList.isShared, true);
      expect(groceryList.collaborators.length, 1);
      expect(groceryList.collaborators.first.userId.name, 'John Doe');
      expect(groceryList.collaborators.first.role, CollaboratorRole.editor);
      expect(groceryList.shareSettings.allowCollaboratorInvites, true);
      expect(groceryList.userRole, UserRole.owner);
      expect(groceryList.itemCounts?.total, 10);
    });

    test('GroceryListInvitation should parse correctly', () {
      final json = {
        '_id': 'invitation123',
        'groceryListId': 'list123',
        'inviterUserId': {
          '_id': 'user123',
          'name': 'Jane Doe',
          'email': '<EMAIL>',
        },
        'inviteeEmail': '<EMAIL>',
        'role': 'editor',
        'status': 'pending',
        'token': 'token123',
        'expiresAt': '2025-12-31T23:59:59.000Z',
        'createdAt': '2024-01-01T00:00:00.000Z',
        'groceryList': {
          '_id': 'list123',
          'name': 'Family Groceries',
          'userId': 'user123',
        },
      };

      final invitation = GroceryListInvitation.fromJson(json);

      expect(invitation.id, 'invitation123');
      expect(invitation.inviterUserId.name, 'Jane Doe');
      expect(invitation.inviteeEmail, '<EMAIL>');
      expect(invitation.role, CollaboratorRole.editor);
      expect(invitation.status, InvitationStatus.pending);
      expect(invitation.groceryList?.name, 'Family Groceries');
      expect(invitation.isPending, true);
      expect(invitation.canBeActedUpon, true);
    });

    test('GroceryItem should include collaboration info', () {
      final json = {
        '_id': 'item123',
        'name': 'Milk',
        'isChecked': true,
        'category': {
          'name': 'Dairy',
          'color': '#FF0000',
        },
        'quantity': '1 gallon',
        'notes': 'Organic',
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T12:00:00.000Z',
        'addedBy': {
          '_id': 'user123',
          'name': 'Jane Doe',
          'email': '<EMAIL>',
        },
        'checkedBy': {
          '_id': 'user456',
          'name': 'John Doe',
          'email': '<EMAIL>',
        },
        'checkedAt': '2024-01-01T10:00:00.000Z',
        'lastModifiedBy': {
          '_id': 'user456',
          'name': 'John Doe',
          'email': '<EMAIL>',
        },
      };

      final item = GroceryItem.fromJson(json);

      expect(item.id, 'item123');
      expect(item.name, 'Milk');
      expect(item.isChecked, true);
      expect(item.addedBy?.name, 'Jane Doe');
      expect(item.checkedBy?.name, 'John Doe');
      expect(item.lastModifiedBy?.name, 'John Doe');
      expect(item.checkedAt, isNotNull);
    });

    test('CollaboratorInfo should parse correctly', () {
      final json = {
        '_id': 'user123',
        'name': 'John Doe',
        'email': '<EMAIL>',
      };

      final collaborator = CollaboratorInfo.fromJson(json);

      expect(collaborator.id, 'user123');
      expect(collaborator.name, 'John Doe');
      expect(collaborator.email, '<EMAIL>');
    });

    test('GroceryShareSettings should have correct defaults', () {
      final settings = GroceryShareSettings(
        allowCollaboratorInvites: true,
        requireApprovalForEdits: false,
        notifyOnChanges: true,
      );

      expect(settings.allowCollaboratorInvites, true);
      expect(settings.requireApprovalForEdits, false);
      expect(settings.notifyOnChanges, true);

      final updated = settings.copyWith(requireApprovalForEdits: true);
      expect(updated.allowCollaboratorInvites, true);
      expect(updated.requireApprovalForEdits, true);
      expect(updated.notifyOnChanges, true);
    });
  });

  group('Collaboration Enums', () {
    test('CollaboratorRole should have correct values', () {
      expect(CollaboratorRole.editor.name, 'editor');
      expect(CollaboratorRole.viewer.name, 'viewer');
    });

    test('UserRole should have correct values', () {
      expect(UserRole.owner.name, 'owner');
      expect(UserRole.editor.name, 'editor');
      expect(UserRole.viewer.name, 'viewer');
    });

    test('InvitationStatus should have correct values', () {
      expect(InvitationStatus.pending.name, 'pending');
      expect(InvitationStatus.accepted.name, 'accepted');
      expect(InvitationStatus.declined.name, 'declined');
      expect(InvitationStatus.expired.name, 'expired');
    });
  });
}
