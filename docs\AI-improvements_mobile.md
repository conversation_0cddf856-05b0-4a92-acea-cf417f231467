Okay, let's detail the plan for integrating these AI enhancements into your Flutter mobile app. This will involve model updates, service layer adjustments, and UI changes to support the new features, particularly the `AI_AUTOCORRECTION_REVIEW` and `CONFIRM_AI_INTERPRETATION` insights.

**I. Flutter Data Model Updates**

1.  **`lib/src/features/tasks/models/task.dart`**
    *   Update `TaskListItem` and `TaskDetail` to include the new AI-driven metadata. It's often cleanest to nest these under a `metadata` map if your backend does so.

    ```dart
    // lib/src/features/tasks/models/task.dart

    // ... (existing imports)

    class TaskMetadata {
      final int? sequence;
      final List<String>? dependsOn; // Assuming these are task IDs or titles
      final bool? isBlocker;
      final String? aiSuggestedDeadlineFailed;
      final String? aiConfidence; // 'High', 'Medium', 'Low'
      final String? aiClarificationNeeded;
      final String? aiOriginalUtterance;
      final String? aiSummaryTitle;
      final String? aiParsedDescription;
      final bool? aiWasAutoCorrected;
      final String? aiOriginalTextSegment;
      final String? aiCorrectedTextSegment;

      TaskMetadata({
        this.sequence,
        this.dependsOn,
        this.isBlocker,
        this.aiSuggestedDeadlineFailed,
        this.aiConfidence,
        this.aiClarificationNeeded,
        this.aiOriginalUtterance,
        this.aiSummaryTitle,
        this.aiParsedDescription,
        this.aiWasAutoCorrected,
        this.aiOriginalTextSegment,
        this.aiCorrectedTextSegment,
      });

      factory TaskMetadata.fromJson(Map<String, dynamic>? json) {
        if (json == null) return TaskMetadata();
        return TaskMetadata(
          sequence: json['sequence'] as int?,
          dependsOn: (json['dependsOn'] as List<dynamic>?)?.map((e) => e as String).toList(),
          isBlocker: json['isBlocker'] as bool?,
          aiSuggestedDeadlineFailed: json['aiSuggestedDeadlineFailed'] as String?,
          aiConfidence: json['aiConfidence'] as String?,
          aiClarificationNeeded: json['aiClarificationNeeded'] as String?,
          aiOriginalUtterance: json['aiOriginalUtterance'] as String?,
          aiSummaryTitle: json['aiSummaryTitle'] as String?,
          aiParsedDescription: json['aiParsedDescription'] as String?,
          aiWasAutoCorrected: json['aiWasAutoCorrected'] as bool?,
          aiOriginalTextSegment: json['aiOriginalTextSegment'] as String?,
          aiCorrectedTextSegment: json['aiCorrectedTextSegment'] as String?,
        );
      }

      Map<String, dynamic> toJson() => {
        if (sequence != null) 'sequence': sequence,
        // ... other fields
        if (aiWasAutoCorrected != null) 'aiWasAutoCorrected': aiWasAutoCorrected,
        if (aiOriginalTextSegment != null) 'aiOriginalTextSegment': aiOriginalTextSegment,
        if (aiCorrectedTextSegment != null) 'aiCorrectedTextSegment': aiCorrectedTextSegment,
        // ...
      };
    }

    class TaskListItem {
      final String id;
      final String title;
      final String? content;
      final bool completed;
      final DateTime? dueDate;
      final String? priority;
      final Category? category;
      final List<Category> categories;
      final Location? location;
      final bool hasReminder;
      final bool isAiGenerated;
      final int dependsOnCount;
      final int blocksCount;
      final TaskMetadata metadata; // Added metadata

      TaskListItem({
        required this.id,
        required this.title,
        this.content,
        required this.completed,
        this.dueDate,
        this.priority,
        this.category,
        this.categories = const [],
        this.location,
        this.hasReminder = false,
        this.isAiGenerated = false,
        this.dependsOnCount = 0,
        this.blocksCount = 0,
        this.metadata = const TaskMetadata(), // Initialize with default
      });

      factory TaskListItem.fromJson(Map<String, dynamic> json) {
        // ... (existing TaskListItem.fromJson logic) ...
        // Ensure to parse the 'metadata' field from the JSON:
        // print('Parsing TaskListItem metadata from JSON: ${json['metadata']}');
        return TaskListItem(
          // ... other fields
          id: json['id'] ?? json['_id'] as String,
          title: json['title'] as String,
          completed: json['completed'] as bool? ?? false,
          metadata: TaskMetadata.fromJson(json['metadata'] as Map<String, dynamic>?),
          // ... (rest of the parsing logic from your existing file)
        );
      }

      // ... (copyWith method needs to include metadata)
      TaskListItem copyWith({
        // ... other fields
        TaskMetadata? metadata,
      }) {
        return TaskListItem(
          // ... other fields
          metadata: metadata ?? this.metadata,
          // ...
        );
      }
    }

    class TaskDetail {
      // ... (existing TaskDetail fields)
      final TaskMetadata metadata; // Added metadata

      TaskDetail({
        // ... (existing constructor params)
        this.metadata = const TaskMetadata(), // Initialize with default
      });

      factory TaskDetail.fromJson(Map<String, dynamic> json) {
        // ... (existing TaskDetail.fromJson logic) ...
        // Ensure to parse the 'metadata' field from the JSON:
        // print('Parsing TaskDetail metadata from JSON: ${json['metadata']}');
        return TaskDetail(
          // ... other fields
          id: json['id'] ?? json['_id'] as String,
          title: json['title'] as String,
          createdAt: DateTime.parse(json['createdAt'] as String), // Example
          metadata: TaskMetadata.fromJson(json['metadata'] as Map<String, dynamic>?),
          // ... (rest of the parsing logic from your existing file)
        );
      }
    }
    ```
    **Note:** You'll need to complete the `fromJson` and `copyWith` for `TaskListItem` and `TaskDetail` to include all existing fields plus the new `metadata`.

2.  **`lib/src/features/insights/models/insight.dart`**
    *   Add `confirmAiInterpretation` and `aiAutocorrectionReview` to `InsightType` enum.
    *   Expand `relatedData` in the `Insight` class to accommodate fields for these new types.

    ```dart
    // lib/src/features/insights/models/insight.dart

    enum InsightType {
      newCategorySuggestion,
      priorityCheck,
      recurringTaskSuggestion,
      categoryCleanup,
      duplicateTaskDetected,
      taskMergeSuggestion,
      overdueTaskNotification,
      confirmAiInterpretation, // New
      aiAutocorrectionReview, // New
      unknown
    }

    // ... (InsightStatus enum remains the same)

    class Insight {
      final String id;
      final String userId;
      final InsightType type;
      final String description;
      final Map<String, dynamic> relatedData;
      final InsightStatus status;
      final DateTime createdAt;
      final DateTime updatedAt;

      Insight({
        required this.id,
        required this.userId,
        required this.type,
        required this.description,
        required this.relatedData,
        required this.status,
        required this.createdAt,
        required this.updatedAt,
      });

      factory Insight.fromJson(Map<String, dynamic> json) {
        // ... (existing parsing for id, userId, description, status, createdAt, updatedAt)
        return Insight(
          id: json['_id'] as String,
          userId: json['userId'] as String,
          type: _parseInsightType(json['type'] as String?),
          description: json['description'] as String? ?? 'No description',
          relatedData: json['relatedData'] as Map<String, dynamic>? ?? {}, // Keep as dynamic map
          status: _parseInsightStatus(json['status'] as String?),
          createdAt: DateTime.parse(json['createdAt'] as String),
          updatedAt: DateTime.parse(json['updatedAt'] as String),
        );
      }

      static InsightType _parseInsightType(String? typeString) {
        switch (typeString) {
          // ... (existing cases)
          case 'CONFIRM_AI_INTERPRETATION': return InsightType.confirmAiInterpretation; // New
          case 'AI_AUTOCORRECTION_REVIEW': return InsightType.aiAutocorrectionReview; // New
          default: return InsightType.unknown;
        }
      }

      // ... (_parseInsightStatus and toString remain the same)

      // Add getters for new relatedData fields for type safety in UI
      String? get taskTitle => relatedData['taskTitle'] as String?;
      String? get originalInput => relatedData['originalInput'] as String?;
      String? get aiConfidence => relatedData['aiConfidence'] as String?;
      String? get aiClarificationNeeded => relatedData['aiClarificationNeeded'] as String?;
      String? get originalText => relatedData['originalText'] as String?;
      String? get correctedText => relatedData['correctedText'] as String?;
      String? get targetTaskId => relatedData['targetTaskId'] as String?;
    }
    ```

**II. API Service Layer Updates (Flutter)**

1.  **`lib/src/core/services/ai_service.dart`**
    *   **`processQuickAdd`:** The `QuickAddResponse` will return tasks that might contain the new metadata. Ensure the task models (`TaskListItem`, `TaskDetail`) used for parsing the `response.data` in `AIService` are updated to deserialize these new metadata fields. The current parsing within `QuickAddResponse.fromJson` might need adjustment if tasks are deeply nested in `response.data['data']`.

2.  **`lib/src/core/services/task_service.dart`**
    *   **`updateTask(String taskId, Map<String, dynamic> taskData)`:** This method is already generic. Ensure that when you call it to revert an AI correction, you are passing the correct field (`title` or `content`) with the `originalText`.

3.  **`lib/src/features/insights/providers/insight_provider.dart`**
    *   This provider will need new methods or enhanced existing methods to handle the actions for `AI_AUTOCORRECTION_REVIEW` and `CONFIRM_AI_INTERPRETATION`.

    ```dart
    // lib/src/features/insights/providers/insight_provider.dart
    // ... (imports)
    import '../../../core/services/task_service.dart'; // Import TaskService for updating tasks

    class InsightNotifier extends StateNotifier<InsightState> {
      final AIService _aiService;
      final TaskService _taskService; // Add TaskService

      InsightNotifier({required AIService aiService, required TaskService taskService}) // Add TaskService
          : _aiService = aiService,
            _taskService = taskService, // Initialize TaskService
            super(InsightState.initial());

      // ... (existing fetchInsights, refreshInsights, handleAction)

      // Method to revert an AI autocorrection
      Future<void> revertAutocorrection(String insightId, String taskId, String originalText, String correctedText, String currentTaskTitle, String? currentTaskContent) async {
        state = state.copyWith(isLoading: true);
        try {
          // Determine which field to update. This is a heuristic.
          // A more robust solution might involve the AI indicating which field was corrected.
          final Map<String, dynamic> updates = {};
          bool titleUpdated = false;
          bool contentUpdated = false;

          // Check if the corrected text matches the current title or content
          if (correctedText == currentTaskTitle) {
            updates['title'] = originalText;
            titleUpdated = true;
          }
          // If title didn't match, or if original text is substantially different from title, assume content was corrected.
          // Also, if content itself matches the corrected text.
          if ((!titleUpdated && originalText.length > currentTaskTitle.length + 10) || (currentTaskContent != null && correctedText == currentTaskContent) ) {
             updates['content'] = originalText;
             contentUpdated = true;
          }
          
          // If neither matched well, update the title as a default.
          if (!titleUpdated && !contentUpdated) {
            updates['title'] = originalText;
          }

          if (updates.isEmpty) {
            print('[InsightProvider] No specific field identified for reverting correction. Defaulting to dismissing insight.');
          } else {
            await _taskService.updateTask(taskId, updates);
            print('[InsightProvider] Task $taskId updated with original text.');
          }

          // After successfully updating the task, dismiss the insight
          await handleAction(insightId, 'dismissed'); // This already refreshes insights
        } catch (e) {
          state = state.copyWith(isLoading: false, error: e.toString());
        }
        // isLoading will be set to false by the handleAction or fetchInsights call
      }

      // Method to "accept" a CONFIRM_AI_INTERPRETATION insight (which just dismisses it)
      Future<void> confirmAiInterpretation(String insightId) async {
        await handleAction(insightId, 'dismissed');
      }

      // Existing handleAction, markTaskComplete, changeTaskDeadline, deleteTask methods are fine
      // ...
    }

    // Update the provider
    final insightProvider = StateNotifierProvider<InsightNotifier, InsightState>((ref) {
      final aiService = ref.watch(aiServiceProvider);
      final taskService = ref.watch(taskServiceProvider); // Get TaskService
      return InsightNotifier(aiService: aiService, taskService: taskService); // Pass it
    });
    ```

**III. UI Implementation (Flutter)**

1.  **Task Display Widgets:**
    *   **`lib/src/features/tasks/widgets/task_list_item_widget.dart`**:
        *   Access `task.metadata.aiWasAutoCorrected`, `task.metadata.aiClarificationNeeded`, `task.metadata.aiConfidence`.
        *   **Visual Cues:**
            *   If `aiWasAutoCorrected == true`, add a small `Icon(LucideIcons.edit3, size: 12, color: Colors.blueAccent)` next to the title or as a badge.
            *   If `aiClarificationNeeded` is not null, add `Icon(LucideIcons.helpCircle, size: 12, color: Colors.orangeAccent)`.
            *   If `aiConfidence == 'Low'`, perhaps a slightly different background shade for the item or a small icon.
    *   **`lib/src/features/tasks/screens/task_detail_screen.dart`**:
        *   Similar to `TaskListItemWidget`, display the AI metadata.
        *   If `aiWasAutoCorrected == true`:
            *   Add a section:
                ```dart
                Text('AI Auto-correction:', style: theme.textTheme.titleSmall),
                Text('Original: "${task.metadata.aiOriginalTextSegment}"', style: theme.textTheme.bodySmall),
                Text('Corrected to: "${task.metadata.aiCorrectedTextSegment}"', style: theme.textTheme.bodySmall),
                TextButton(onPressed: () { /* Call provider to revert */ }, child: Text('Revert to Original'))
                ```
        *   If `aiClarificationNeeded != null`:
            *   Add a section: `Text('AI needs clarification: "${task.metadata.aiClarificationNeeded}"', style: TextStyle(color: Colors.orangeAccent))`.

2.  **Insight Card Widget:**
    *   **`lib/src/features/insights/widgets/insight_card_widget.dart`**:
        *   **New Prop:** Add `final Function(String taskId)? onEditTask;`
        *   **`_getInsightIcon`, `_getTitle`, `_getBackgroundColor`, `_getBorderColor`**:
            *   Add cases for `InsightType.aiAutocorrectionReview` and `InsightType.confirmAiInterpretation`. Use icons like `LucideIcons.edit3` (Autocorrection) and `LucideIcons.brainCircuit` (Confirm AI).
        *   **`_buildRelatedContext`**:
            *   For `InsightType.aiAutocorrectionReview`:
                ```dart
                return Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (insight.taskTitle != null) Text('For task: "${insight.taskTitle}"', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text('Original: "${insight.originalText ?? 'N/A'}"', style: theme.textTheme.bodySmall),
                      Text('Corrected to: "${insight.correctedText ?? 'N/A'}"', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.primary)),
                    ],
                  ),
                );
                ```
            *   For `InsightType.confirmAiInterpretation`:
                ```dart
                return Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (insight.taskTitle != null) Text('Task created: "${insight.taskTitle}"', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
                      if (insight.originalInput != null) Text('Based on: "${insight.originalInput}"', style: theme.textTheme.bodySmall),
                      if (insight.aiConfidence != null) Text('AI Confidence: ${insight.aiConfidence}', style: theme.textTheme.bodySmall),
                      if (insight.aiClarificationNeeded != null) Text('AI needs clarification: "${insight.aiClarificationNeeded}"', style: theme.textTheme.bodySmall?.copyWith(color: Colors.orangeAccent)),
                    ],
                  ),
                );
                ```
        *   **`_buildSpecificActionButtons`**:
            *   **For `InsightType.aiAutocorrectionReview`**:
                *   `TextButton(child: Text('Keep Correction'), onPressed: () => _handleDismiss(ref))`
                *   `TextButton(child: Text('Revert'), onPressed: () { ref.read(insightProvider.notifier).revertAutocorrection(insight.id, insight.targetTaskId!, insight.originalText!, insight.correctedText!, insight.taskTitle!, task.content); if (onActionComplete != null) onActionComplete!(); })` (Assuming `task.content` is available or fetched for comparison). *Correction: The `revertAutocorrection` in provider needs the current task content to decide which field to revert. This might be tricky if the card doesn't have full task details. A simpler approach might be to revert the title by default or pass a flag from the backend about which field was corrected.*
                *   `TextButton(child: Text('Edit Task'), onPressed: () { if (widget.onEditTask != null && insight.targetTaskId != null) widget.onEditTask!(insight.targetTaskId!); _handleDismiss(ref); })`
            *   **For `InsightType.confirmAiInterpretation`**:
                *   `ElevatedButton(child: Text('Looks Good'), onPressed: () => _handleAccept(ref))` (Accept here means dismiss, task is okay).
                *   `TextButton(child: Text('Edit Task'), onPressed: () { if (widget.onEditTask != null && insight.targetTaskId != null) widget.onEditTask!(insight.targetTaskId!); _handleDismiss(ref); })`
                *   `TextButton(child: Text('Delete Task', style: TextStyle(color: theme.colorScheme.error)), onPressed: () => _handleDeleteTask(context, ref, insight.targetTaskId!))`

3.  **Pass `onEditTask` to `AiInsightsListWidget`:**
    *   In `lib/src/features/insights/screens/insights_screen.dart` (and `dashboard_screen.dart` if it shows insights), pass the navigation function to `AiInsightsListWidget`.

    ```dart
    // lib/src/features/insights/screens/insights_screen.dart
    // ...
    @override
    Widget build(BuildContext context) {
      // ...
      return Scaffold(
        // ...
        body: AiInsightsListWidget(
          onActionComplete: _handleInsightActionComplete,
          refreshKey: _refreshKey,
          onEditTask: (taskId) { // NEW
            context.pushNamed('editTask', pathParameters: {'taskId': taskId});
          },
        ),
      );
    }
    ```
    *   `AiInsightsListWidget` needs to accept and forward this `onEditTask` prop to each `InsightCardWidget`.

**Potential Issues & Refinements:**

*   **Reverting Autocorrections:**
    *   The AI prompt should ideally specify *which field* (`title` or `content`) it corrected. If not, the frontend/backend has to guess when reverting.
    *   Current plan: The `revertAutocorrection` method in `InsightProvider` needs access to the task's current title *and* content to intelligently decide what to revert to `originalText`. This might mean fetching the task before updating or the `InsightCard` needs to fetch this if it's not already part of the `Insight`'s `relatedData`. A simpler, though less precise, approach is to revert the `title` by default and potentially `content` if `originalText` is long.
*   **Error Handling:** Robust error handling for API calls in providers (e.g., when `updateTask` fails during revert).
*   **Loading States:** Ensure `isLoading` states are managed correctly in the `InsightProvider` and UI during multi-step actions like "Revert."
*   **Linter/Static Analysis:** After these changes, run `flutter analyze` to catch any Dart-specific issues.
*   **Dependencies:** Ensure all necessary imports are correct. The `TaskService` and `InsightService` might need to be accessible within the `InsightProvider` if actions directly modify tasks or other insights. (Corrected this by adding `TaskService` to `InsightNotifier`).

This detailed plan should guide the Flutter implementation. Remember to test each part thoroughly, especially the interaction between insights and task modifications.