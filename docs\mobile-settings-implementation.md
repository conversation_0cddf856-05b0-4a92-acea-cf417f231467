# Mobile Settings Implementation

**Status: ✅ Implementation Complete**

This document outlines the comprehensive implementation of the settings area in the Flutter mobile app, providing feature parity with the web application while optimizing for mobile user experience.

## 1. Design System & UI/UX Principles

### 1.1 Core Design Principles
- **Platform-Native Feel**: Using Flutter's Material Design components with TaskOrganAIzer styling
- **Consistency with Web**: Maintaining functional parity while adapting for mobile interfaces
- **Accessibility**: Ensuring all settings are usable by everyone with proper contrast and touch targets
- **Performance**: Fast loading and responsive interactions

### 1.2 Color Scheme
- **Primary**: `#4F46E5` (Indigo 600)
- **Background**: `#F9FAFB` (Gray 50)
- **Surface**: `#FFFFFF` (White)
- **Text Primary**: `#111827` (Gray 900)
- **Text Secondary**: `#6B7280` (Gray 500)
- **Border**: `#E5E7EB` (Gray 200)
- **Success**: `#10B981` (Emerald 500)
- **Warning**: `#F59E0B` (Amber 500)
- **Error**: `#EF4444` (Red 500)

### 1.3 Typography
- **Headline**: 20sp, SemiBold
- **Title**: 16sp, SemiBold
- **Body**: 14sp, Regular
- **Caption**: 12sp, Regular
- **Button**: 14sp, Medium

## 2. Navigation Structure

```
Settings (Main Screen)
├── Profile
│   ├── Edit Profile
│   └── Change Avatar
├── Account
│   ├── Change Password
│   └── Security
├── Tasks
│   ├── Default View
│   ├── Sort & Filter
│   └── Task Actions
├── Notifications
│   ├── Push Notifications
│   ├── Email Notifications
│   └── Reminder Settings
├── Date & Time
│   ├── Date Format
│   ├── Time Format
│   └── Timezone
├── AI & Automation
│   ├── Keytag Mappings
│   ├── Category-Location Mappings
│   └── AI Suggestions
└── Developer
    └── API Settings
```

## 3. Backend Implementation

The backend implementation provides a comprehensive API for managing user settings:

### 3.1 API Endpoints

- **GET/PUT `/api/users/settings/all`**: Fetch or update all settings at once
- **GET/PUT `/api/users/settings/preferences`**: General preferences
- **GET/PUT `/api/users/settings/task-settings`**: Task-specific settings
- **GET/PUT `/api/users/settings/notification-settings`**: Notification preferences
- **GET/PUT `/api/users/settings/datetime-settings`**: Date and time formats
- **GET/PUT `/api/users/settings/keytag-mappings`**: Keyword to category mappings
- **GET/PUT `/api/users/settings/category-location-mappings`**: Category to location mappings
- **GET/PUT `/api/users/settings/location-suggestion-settings`**: Location suggestion preferences

### 3.2 Data Models

The backend uses a comprehensive `UserSettings` model with nested objects for specific setting types:

```typescript
interface UserSettings {
  userId: string;
  preferences: {
    theme: string;
    defaultView: string;
    taskSettings: {
      defaultSortField: string;
      defaultSortOrder: string;
      defaultDeadlineTime: string;
      autoArchiveCompletedTasks: boolean;
      autoArchiveDays: number;
      autoHideCompletedTasks: boolean;
      autoHideDays: number;
      defaultReminderOffset: number;
    };
    notificationSettings: {
      enableEmailNotifications: boolean;
      enablePushNotifications: boolean;
      enableInAppNotifications: boolean;
      reminderEscalation: boolean;
      reminderIntervals: number[];
    };
    dateTimeSettings: {
      dateFormat: string;
      timeFormat: string;
      timezone: string;
    };
  };
  keytagMapping: {
    exactMatches: Record<string, string>;
    enableFuzzyMatching: boolean;
    fuzzyMatchThreshold: number;
    excludedKeywords: string[];
    applyToManualTasks: boolean;
    updatedAt: number;
  };
  categoryLocationMapping: {
    exactMatches: Record<string, string>;
    categoryAliases: Record<string, string[]>;
    enableFuzzyMatching: boolean;
    fuzzyMatchThreshold: number;
    debugMode: boolean;
    excludedCategories: string[];
    categoryPriority: Record<string, number>;
    autoMapWithoutConfirmation: boolean;
    updatedAt: number;
  };
  locationSuggestionSettings: {
    weights: {
      proximity: number;
      keyword: number;
      categoryMapping: number;
      aiHint: number;
    };
    nonLinearProximityDecay: boolean;
    maxDistanceThreshold: number;
  };
}
```

## 4. Mobile App Implementation

### 4.1 Project Structure

```
lib/
  features/
    settings/
      screens/
        settings_screen.dart
        profile_settings_screen.dart
        account_settings_screen.dart
        task_settings_screen.dart
        notification_settings_screen.dart
        datetime_settings_screen.dart
        keytag_mapping_screen.dart
        category_location_mapping_screen.dart
      widgets/
        section_header.dart
        settings_tile.dart
        toggle_setting.dart
        dropdown_setting.dart
        text_setting.dart
      models/
        user_settings.dart
        keytag_mapping.dart
        category_location_mapping.dart
      providers/
        settings_provider.dart
        keytag_mapping_provider.dart
        category_location_provider.dart
      services/
        settings_service.dart
```

### 4.2 Data Models

The Flutter app uses Dart classes that mirror the backend models:

```dart
class UserAllSettings {
  final UserPreferences preferences;
  final KeytagMappingSettings keytagMapping;
  final CategoryLocationMappingSettings categoryLocationMapping;
  final LocationSuggestionSettings locationSuggestionSettings;

  UserAllSettings({
    required this.preferences,
    required this.keytagMapping,
    required this.categoryLocationMapping,
    required this.locationSuggestionSettings,
  });

  factory UserAllSettings.fromJson(Map<String, dynamic> json) {
    return UserAllSettings(
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
      keytagMapping: KeytagMappingSettings.fromJson(json['keytagMapping'] ?? {}),
      categoryLocationMapping: CategoryLocationMappingSettings.fromJson(
          json['categoryLocationMapping'] ?? {}),
      locationSuggestionSettings: LocationSuggestionSettings.fromJson(
          json['locationSuggestionSettings'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'preferences': preferences.toJson(),
      'keytagMapping': keytagMapping.toJson(),
      'categoryLocationMapping': categoryLocationMapping.toJson(),
      'locationSuggestionSettings': locationSuggestionSettings.toJson(),
    };
  }
}
```

### 4.3 State Management

The app uses Riverpod for state management:

```dart
@riverpod
class UserSettingsNotifier extends _$UserSettingsNotifier {
  late final SettingsService _settingsService;

  @override
  FutureOr<UserAllSettings?> build() {
    _settingsService = ref.read(settingsServiceProvider);
    return _fetchSettings();
  }

  Future<UserAllSettings?> _fetchSettings() async {
    try {
      final settings = await _settingsService.getAllUserSettings();
      return settings;
    } catch (e) {
      ref.read(loggerProvider).e('Error fetching settings: $e');
      return null;
    }
  }

  Future<void> updateAllSettings(UserAllSettings settings) async {
    state = const AsyncValue.loading();
    try {
      await _settingsService.updateAllUserSettings(settings);
      state = AsyncValue.data(settings);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}
```

## 5. UI Implementation

### 5.1 Main Settings Screen

The main settings screen provides navigation to all settings categories:

```dart
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        elevation: 0,
      ),
      body: ListView(
        children: [
          _buildSectionHeader(context, 'Account'),
          _buildSettingsTile(
            context,
            icon: LucideIcons.user,
            title: 'Profile',
            subtitle: 'Manage your personal information',
            onTap: () => context.push('/settings/profile'),
          ),
          _buildSettingsTile(
            context,
            icon: LucideIcons.lock,
            title: 'Account & Security',
            subtitle: 'Password and account security',
            onTap: () => context.push('/settings/account'),
          ),
          
          _buildSectionHeader(context, 'Preferences'),
          _buildSettingsTile(
            context,
            icon: LucideIcons.checkSquare,
            title: 'Tasks',
            subtitle: 'Default views and behaviors',
            onTap: () => context.push('/settings/tasks'),
          ),
          _buildSettingsTile(
            context,
            icon: LucideIcons.bell,
            title: 'Notifications',
            subtitle: 'Configure alerts and reminders',
            onTap: () => context.push('/settings/notifications'),
          ),
          _buildSettingsTile(
            context,
            icon: LucideIcons.clock,
            title: 'Date & Time',
            subtitle: 'Format preferences',
            onTap: () => context.push('/settings/datetime'),
          ),
          
          _buildSectionHeader(context, 'AI & Automation'),
          _buildSettingsTile(
            context,
            icon: LucideIcons.tag,
            title: 'Keytag Mappings',
            subtitle: 'Keyword to category mappings',
            onTap: () => context.push('/settings/keytag-mappings'),
          ),
          _buildSettingsTile(
            context,
            icon: LucideIcons.mapPin,
            title: 'Category-Location Mappings',
            subtitle: 'Link categories to locations',
            onTap: () => context.push('/settings/category-locations'),
          ),
          
          if (kDebugMode) ...[
            _buildSectionHeader(context, 'Developer'),
            _buildSettingsTile(
              context,
              icon: LucideIcons.terminal,
              title: 'Developer Settings',
              subtitle: 'Advanced configuration',
              onTap: () => context.push('/settings/developer'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    String? subtitle,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: const Icon(LucideIcons.chevronRight),
      onTap: onTap,
    );
  }
}
```

### 5.2 Settings Detail Screens

Each settings category has its own detail screen. For example, the Keytag Mappings screen:

```dart
class KeytagMappingScreen extends ConsumerStatefulWidget {
  const KeytagMappingScreen({Key? key}) : super(key: key);

  @override
  _KeytagMappingScreenState createState() => _KeytagMappingScreenState();
}

class _KeytagMappingScreenState extends ConsumerState<KeytagMappingScreen> {
  final _formKey = GlobalKey<FormState>();
  late KeytagMappingSettings _settings;
  final _newKeywordController = TextEditingController();
  String? _selectedCategoryId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final settingsState = ref.read(userSettingsProvider);
    if (settingsState.value != null) {
      _settings = settingsState.value!.keytagMapping;
    } else {
      _settings = KeytagMappingSettings(
        exactMatches: {},
        enableFuzzyMatching: true,
        fuzzyMatchThreshold: 0.7,
        excludedKeywords: [],
        applyToManualTasks: false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(flatCategoryListProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Keytag Mappings'),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // General settings
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'General Settings',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 16),
                          SwitchListTile(
                            title: const Text('Enable Fuzzy Matching'),
                            subtitle: const Text(
                                'Match keywords that are similar but not exact'),
                            value: _settings.enableFuzzyMatching,
                            onChanged: (value) {
                              setState(() {
                                _settings = _settings.copyWith(
                                    enableFuzzyMatching: value);
                              });
                            },
                          ),
                          if (_settings.enableFuzzyMatching) ...[
                            const SizedBox(height: 8),
                            Text('Fuzzy Match Threshold: ${(_settings.fuzzyMatchThreshold * 100).toInt()}%'),
                            Slider(
                              value: _settings.fuzzyMatchThreshold,
                              min: 0.5,
                              max: 1.0,
                              divisions: 10,
                              label: '${(_settings.fuzzyMatchThreshold * 100).toInt()}%',
                              onChanged: (value) {
                                setState(() {
                                  _settings = _settings.copyWith(
                                      fuzzyMatchThreshold: value);
                                });
                              },
                            ),
                          ],
                          const SizedBox(height: 8),
                          SwitchListTile(
                            title: const Text('Apply to Manual Tasks'),
                            subtitle: const Text(
                                'Apply mappings to manually created tasks'),
                            value: _settings.applyToManualTasks,
                            onChanged: (value) {
                              setState(() {
                                _settings = _settings.copyWith(
                                    applyToManualTasks: value);
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Mappings
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Keyword Mappings',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              TextButton.icon(
                                icon: const Icon(LucideIcons.plus),
                                label: const Text('Add'),
                                onPressed: () => _showAddMappingDialog(context, categoriesAsync),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_settings.exactMatches.isEmpty)
                            const Center(
                              child: Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Text('No mappings yet. Add your first one!'),
                              ),
                            )
                          else
                            ..._buildMappingsList(context, categoriesAsync),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Excluded Keywords
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Excluded Keywords',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              TextButton.icon(
                                icon: const Icon(LucideIcons.plus),
                                label: const Text('Add'),
                                onPressed: () => _showAddExcludedKeywordDialog(context),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: _settings.excludedKeywords.map((keyword) {
                              return Chip(
                                label: Text(keyword),
                                deleteIcon: const Icon(LucideIcons.x, size: 16),
                                onDeleted: () {
                                  setState(() {
                                    final newList = List<String>.from(
                                        _settings.excludedKeywords);
                                    newList.remove(keyword);
                                    _settings = _settings.copyWith(
                                        excludedKeywords: newList);
                                  });
                                },
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  List<Widget> _buildMappingsList(BuildContext context, AsyncValue<List<Category>> categoriesAsync) {
    return categoriesAsync.when(
      data: (categories) {
        final mappings = _settings.exactMatches.entries.toList();
        return mappings.map((entry) {
          final keyword = entry.key;
          final categoryId = entry.value;
          final category = categories.firstWhere(
            (c) => c.id == categoryId,
            orElse: () => Category(
              id: categoryId,
              name: 'Unknown Category',
              color: '#808080',
              userId: '',
            ),
          );
          
          return ListTile(
            title: Text(keyword),
            subtitle: Text('Maps to: ${category.name}'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(LucideIcons.edit2),
                  onPressed: () => _showEditMappingDialog(
                    context,
                    keyword,
                    categoryId,
                    categoriesAsync,
                  ),
                ),
                IconButton(
                  icon: const Icon(LucideIcons.trash2),
                  onPressed: () => _showDeleteMappingDialog(context, keyword),
                ),
              ],
            ),
          );
        }).toList();
      },
      loading: () => [const Center(child: CircularProgressIndicator())],
      error: (_, __) => [const Text('Error loading categories')],
    );
  }

  // Dialog methods for adding/editing mappings and excluded keywords...

  Future<void> _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        final settingsState = ref.read(userSettingsProvider);
        if (settingsState.value != null) {
          final allSettings = settingsState.value!;
          final updatedSettings = allSettings.copyWith(
            keytagMapping: _settings,
          );
          
          await ref.read(userSettingsProvider.notifier).updateAllSettings(updatedSettings);
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Settings saved successfully')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving settings: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
```

## 6. Visual Design

### 6.1 Main Settings Screen

```
+----------------------------------+
|  ⬅️ Settings                    |
+----------------------------------+
|  Account                         |
|  ┌──────────────────────────┐    |
|  │ 👤 Profile              ›│    |
|  └──────────────────────────┘    |
|  ┌──────────────────────────┐    |
|  │ 🔒 Account & Security   ›│    |
|  └──────────────────────────┘    |
|                                  |
|  Preferences                     |
|  ┌──────────────────────────┐    |
|  │ ✅ Tasks                 ›│    |
|  ├──────────────────────────┤    |
|  │ 🔔 Notifications        ›│    |
|  ├──────────────────────────┤    |
|  │ 🕒 Date & Time         ›│    |
|  └──────────────────────────┘    |
|                                  |
|  AI & Automation                 |
|  ┌──────────────────────────┐    |
|  │ ⚡ Keytag Mappings      ›│    |
|  ├──────────────────────────┤    |
|  │ 🏷️ Category-Locations  ›│    |
|  └──────────────────────────┘    |
|                                  |
|  Developer (debug only)           |
|  ┌──────────────────────────┐    |
|  │ 💻 Developer Settings   ›│    |
|  └──────────────────────────┘    |
+----------------------------------+
```

### 6.2 Settings Detail Screen Example (Keytag Mappings)

```
+----------------------------------+
|  ⬅️ Keytag Mappings     💾      |
+----------------------------------+
|  ┌──────────────────────────┐    |
|  │ General Settings         │    |
|  │                          │    |
|  │ Enable Fuzzy Matching    │    |
|  │ Match similar keywords   │    |
|  │ ┌─────────────────┐ ON  │    |
|  │ └─────────────────┘      │    |
|  │                          │    |
|  │ Fuzzy Match Threshold: 70%   │    |
|  │ ┌───────●─────────┐     │    |
|  │ └───────────────────┘    │    |
|  │                          │    |
|  │ Apply to Manual Tasks    │    |
|  │ Use for manual creation  │    |
|  │ ┌─────────────────┐ OFF │    |
|  │ └─────────────────┘      │    |
|  └──────────────────────────┘    |
|                                  |
|  ┌──────────────────────────┐    |
|  │ Keyword Mappings    + Add│    |
|  │                          │    |
|  │ grocery                  │    |
|  │ Maps to: Shopping        │    |
|  │                    ✏️ 🗑️  │    |
|  │                          │    |
|  │ workout                  │    |
|  │ Maps to: Health          │    |
|  │                    ✏️ 🗑️  │    |
|  └──────────────────────────┘    |
|                                  |
|  ┌──────────────────────────┐    |
|  │ Excluded Keywords   + Add│    |
|  │                          │    |
|  │ [the] [and] [for] [to]  │    |
|  └──────────────────────────┘    |
+----------------------------------+
```

## 7. Implementation Status

### 7.1 Completed
- ✅ Backend API endpoints
- ✅ Core data models
- ✅ Settings service implementation
- ✅ Main settings screen
- ✅ Profile settings screen
- ✅ Account settings screen
- ✅ Task settings screen
- ✅ Notification settings screen
- ✅ Date & time settings screen
- ✅ Keytag mappings screen
- ✅ Category-location mappings screen

### 7.2 Next Steps
- Enhance error handling and offline support
- Add comprehensive unit and integration tests
- Implement analytics to track settings usage
- Review accessibility compliance
- Optimize performance for low-end devices

## 8. Conclusion

The mobile settings implementation provides a comprehensive, user-friendly interface for managing all aspects of the Task OrganAIzer application. By maintaining consistency with the web interface while optimizing for mobile, we ensure users have a seamless experience across platforms.
