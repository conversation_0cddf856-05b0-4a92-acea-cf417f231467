# Setting up Firebase App Distribution with a Service Account

This guide will walk you through setting up a service account for Firebase App Distribution.

## Prerequisites

1. You must have a Firebase project with App Distribution enabled
2. You must have the Google Cloud SDK installed
3. You must have the Firebase CLI installed

## Step 1: Create a Service Account

1. Go to your Firebase project in the console: https://console.firebase.google.com/
2. Click on the gear icon (⚙️) in the top left and select "Project settings"
3. Select the "Service accounts" tab
4. Click on "Generate new private key" button at the bottom
5. Save the JSON file to a secure location on your computer

## Step 2: Assign the Firebase App Distribution Admin Role

1. Go to the Google Cloud Console: https://console.cloud.google.com/
2. Make sure you're in the same project as your Firebase project
3. Go to "IAM & Admin" > "IAM"
4. Find the service account you just created (it will have an email ending with `@[PROJECT-ID].iam.gserviceaccount.com`)
5. Click the pencil icon to edit its permissions
6. Click "Add another role"
7. Search for "Firebase App Distribution Admin" and select it
8. Click "Save"

## Step 3: Set Up Environment Variables

In PowerShell:

```powershell
# Set the path to your service account key file
$env:GOOGLE_APPLICATION_CREDENTIALS = "C:\path\to\your\serviceAccountKey.json"

# Set your Firebase App ID (found in Firebase Console > Project Settings > Your Apps)
$env:FIREBASE_APP_ID = "1:************:android:abcdef1234567890"
```

## Step 4: Test the Distribution

Run the distribute script:

```powershell
.\distribute.ps1
```

If everything is set up correctly, your app should be uploaded to Firebase App Distribution.

## Troubleshooting

### Permission Denied Errors

If you get a 403 Permission Denied error, check the following:

1. Verify that the service account has the "Firebase App Distribution Admin" role
2. Make sure the GOOGLE_APPLICATION_CREDENTIALS environment variable points to the correct JSON file
3. Ensure the Firebase project ID in the service account JSON matches the project where your app is registered
4. Check that the app ID is correct and belongs to the same Firebase project

### Invalid Service Account

If you get an error about an invalid service account:

1. Make sure the JSON file is not corrupted
2. Regenerate a new service account key if necessary
3. Verify that the service account still exists in the Google Cloud Console

### Other Issues

If you encounter other issues:

1. Check the Firebase CLI logs for more details
2. Run `firebase --debug appdistribution:distribute` for verbose logging
3. Verify that your Firebase project has App Distribution enabled
4. Ensure you're logged in with the correct Google account that has access to the Firebase project 