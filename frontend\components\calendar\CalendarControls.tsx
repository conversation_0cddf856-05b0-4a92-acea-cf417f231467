'use client';

import { useState, useEffect } from 'react';
import { format, startOfToday, isToday, isSameDay, addDays, isWithinInterval } from 'date-fns';
import { Check, CalendarDays } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TaskModel } from '@/types/task';
import { TaskPriorityType } from '@/types/task-priority';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { safeFormatDate } from '@/lib/utils';

const PRIORITIES: TaskPriorityType[] = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];

interface CalendarControlsProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  tasks: TaskModel[];
  categories: { id: string; name: string; color: string }[];
  selectedCategories: string[];
  onCategoryToggle: (categoryId: string) => void;
  onTaskClick: (task: TaskModel) => void;
  onFilterChange?: (filters: TaskFilters) => void;
}

interface TaskFilters {
  priorities?: string[];
  status?: 'all' | 'active' | 'completed';
}

export function CalendarControls({
  selectedDate,
  onDateSelect,
  tasks,
  categories,
  selectedCategories,
  onCategoryToggle,
  onTaskClick,
  onFilterChange,
}: CalendarControlsProps) {
  const [date, setDate] = useState<Date>(selectedDate || startOfToday());
  const [upcomingTasks, setUpcomingTasks] = useState<TaskModel[]>([]);
  const [showCompleted, setShowCompleted] = useState(false);
  const [selectedPriorities, setSelectedPriorities] = useState<TaskPriorityType[]>([]);
  const [isFiltersApplied, setIsFiltersApplied] = useState(false);
  const [calendarSyncStatus] = useState<'connected' | 'disconnected'>('disconnected');
  const [connectedCalendars] = useState<{ provider: string; email: string }[]>([]);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);

  // Update upcoming tasks when tasks or filters change
  useEffect(() => {
    const filtered = tasks.filter(task => {
      if (!task.dueDate) return false;

      // Filter by completion status
      if (!showCompleted && task.completed) return false;

      // Filter by priority if priorities are selected
      if (selectedPriorities.length > 0 && task.priority && !selectedPriorities.includes(task.priority)) {
        return false;
      }

      // Show tasks due within the next 7 days
      const dueDate = new Date(task.dueDate);
      return isWithinInterval(dueDate, {
        start: startOfToday(),
        end: addDays(startOfToday(), 7)
      });
    });

    setUpcomingTasks(filtered);
  }, [tasks, showCompleted, selectedPriorities]);

  // Handle priority toggle
  const handlePriorityToggle = (priority: TaskPriorityType) => {
    setSelectedPriorities(prev => {
      const newPriorities = prev.includes(priority)
        ? prev.filter(p => p !== priority)
        : [...prev, priority];

      // Apply filters if onFilterChange is provided
      if (onFilterChange) {
        onFilterChange({
          priorities: newPriorities.length > 0 ? newPriorities as string[] : undefined,
          status: showCompleted ? 'all' : 'active'
        });
      }

      setIsFiltersApplied(newPriorities.length > 0 || showCompleted);
      return newPriorities;
    });
  };

  // Handle show completed toggle
  const handleShowCompletedToggle = (checked: boolean) => {
    setShowCompleted(checked);

    // Apply filters if onFilterChange is provided
    if (onFilterChange) {
      onFilterChange({
        priorities: selectedPriorities.length > 0 ? selectedPriorities as string[] : undefined,
        status: checked ? 'all' : 'active'
      });
    }

    setIsFiltersApplied(selectedPriorities.length > 0 || checked);
  };

  // Clear all filters
  const clearFilters = () => {
    setSelectedPriorities([]);
    setShowCompleted(false);

    if (onFilterChange) {
      onFilterChange({
        status: 'active'
      });
    }

    setIsFiltersApplied(false);
  };

  return (
    <div className="space-y-6">
      {/* Calendar Sync */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">Calendar Sync</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsSyncDialogOpen(true)}
            className="h-8 px-2"
          >
            <CalendarDays className="h-4 w-4 mr-1" />
            {calendarSyncStatus === 'connected' ? 'Manage' : 'Connect'}
          </Button>
        </div>

        {calendarSyncStatus === 'connected' && connectedCalendars.length > 0 && (
          <div className="text-sm text-gray-600 mb-2">
            {connectedCalendars.map((calendar, index) => (
              <div key={index} className="flex items-center mb-1">
                <Check className="h-3 w-3 text-green-500 mr-1" />
                <span>Connected to {calendar.provider} ({calendar.email})</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Filters */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">Filters</h3>
          {isFiltersApplied && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-6 px-2 text-xs"
            >
              Clear
            </Button>
          )}
        </div>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-completed"
              checked={showCompleted}
              onCheckedChange={handleShowCompletedToggle}
            />
            <label
              htmlFor="show-completed"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Show Completed
            </label>
          </div>
          <div className="space-y-1.5">
            {PRIORITIES.map((priority) => (
              <div key={priority} className="flex items-center space-x-2">
                <Checkbox
                  id={`priority-${priority}`}
                  checked={selectedPriorities.includes(priority)}
                  onCheckedChange={() => handlePriorityToggle(priority)}
                />
                <label
                  htmlFor={`priority-${priority}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {priority.charAt(0) + priority.slice(1).toLowerCase()}
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Categories */}
      <div>
        <h3 className="font-medium mb-2">Categories</h3>
        <div className="space-y-2">
          {categories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.id}`}
                checked={selectedCategories.includes(category.id)}
                onCheckedChange={() => onCategoryToggle(category.id)}
              />
              <label
                htmlFor={`category-${category.id}`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {category.name}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Upcoming Tasks */}
      {upcomingTasks.length > 0 && (
        <div>
          <h3 className="font-medium mb-2">Upcoming Tasks</h3>
          <div className="space-y-2">
            {upcomingTasks.map((task) => (
              <Button
                key={task.id}
                variant="ghost"
                className="w-full justify-start text-left p-2 h-auto"
                onClick={() => onTaskClick(task)}
              >
                <div className="flex flex-col gap-1">
                  <span className="text-sm font-medium">{task.title}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {safeFormatDate(task.dueDate, 'MMM d')}
                    </Badge>
                    {task.priority && (
                      <Badge variant="secondary" className="text-xs">
                        {task.priority.toLowerCase()}
                      </Badge>
                    )}
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
