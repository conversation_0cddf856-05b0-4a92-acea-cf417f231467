Okay, Smart End-of-Speech (EOS) detection is a crucial UX improvement for voice input. The goal is to make the recording feel natural, stopping automatically when the user is done speaking, without them needing to explicitly hit a "stop" button, yet not cutting them off prematurely.

Here's a plan for implementing this in your frontend (web) and mobile (Flutter) apps:

**I. Core Concepts for Smart EOS Detection**

1.  **Silence Detection:** The primary mechanism. After speech is detected, monitor for a subsequent period of silence.
2.  **API Events:** Leverage events provided by the underlying Speech Recognition APIs:
    *   **Web Speech API:** `onspeechend` (browser thinks speech has stopped), `onresult` (especially `result.isFinal`), `onnomatch`, `onerror`.
    *   **Flutter `speech_to_text`:** `onStatusChanged` (statuses like `listening`, `notListening`, `done`), `onSoundLevelChanged` (can indicate silence if levels are consistently low, though less reliable than direct speech events). The `pauseFor` parameter in `listen()` is also key.
3.  **Timers:** Use timers to manage silence thresholds.
    *   `inactivityTimeout`: Duration of silence after speech *ends* before stopping the recording.
    *   `maxRecordingDuration`: A safety net to prevent infinitely long recordings.
    *   (Optional) `initialSilenceTimeout`: Duration to wait for initial speech before potentially giving feedback or stopping.
4.  **User Feedback:** Provide visual cues to the user about the recording state (e.g., "Listening...", "Silence detected, finishing up...", "Processing...").
5.  **Manual Override:** Always keep a manual "Stop" button available.

**II. Implementation Plan - Web Frontend (`frontend/components/recording-modal.tsx`)**

Your current `RecordingModal.tsx` uses `recognitionRef.current.continuous = true;` and `recognitionRef.current.interimResults = true;`. This is good for getting live updates.

```typescript
// frontend/components/recording-modal.tsx
// ... (existing imports)

const INACTIVITY_TIMEOUT_MS = 2000; // 2 seconds of silence after speech ends
const MAX_RECORDING_DURATION_MS = 30000; // 30 seconds max overall

export function RecordingModal({ isOpen, onClose, onSave }: RecordingModalProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [finalTranscript, setFinalTranscript] = useState("");
  const [interimTranscript, setInterimTranscript] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(true);
  const [statusMessage, setStatusMessage] = useState("Initializing..."); // For user feedback

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null); // Timer for silence detection
  const maxDurationTimerRef = useRef<NodeJS.Timeout | null>(null); // Timer for max recording duration

  const SpeechRecognitionAPI = (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) as SpeechRecognitionStatic | undefined;

  const stopRecordingAndSave = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
    if (maxDurationTimerRef.current) clearTimeout(maxDurationTimerRef.current);

    setIsRecording(false);
    setRecordingTime(0);
    const transcriptToSave = (finalTranscript + interimTranscript).trim();
    if (transcriptToSave) {
      onSave(transcriptToSave);
    }
    // onClose(); // Let the caller decide if modal should close immediately after save
  }, [finalTranscript, interimTranscript, onSave, onClose]);

  const cleanupRecognition = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.onresult = null;
      recognitionRef.current.onerror = null;
      recognitionRef.current.onend = null;
      recognitionRef.current.onspeechend = null; // Important for EOS
      recognitionRef.current.stop(); // Ensure it's stopped
      recognitionRef.current = null;
    }
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
    if (maxDurationTimerRef.current) clearTimeout(maxDurationTimerRef.current);
    timerIntervalRef.current = null;
    inactivityTimerRef.current = null;
    maxDurationTimerRef.current = null;
  }, []);


  useEffect(() => {
    if (!isOpen) {
      cleanupRecognition();
      setIsRecording(false); // Ensure recording state is reset
      setStatusMessage("Initializing...");
      return;
    }

    if (!SpeechRecognitionAPI) {
      setError("Speech recognition is not supported by your browser. Try Chrome or Edge.");
      setIsSupported(false);
      setIsRecording(false);
      setStatusMessage("Unsupported");
      return;
    }

    setIsSupported(true);
    setError(null);
    setFinalTranscript("");
    setInterimTranscript("");
    setStatusMessage("Initializing microphone...");

    try {
      if (!recognitionRef.current) {
        recognitionRef.current = new SpeechRecognitionAPI();
      }
      const recognition = recognitionRef.current; // Shadow variable for non-null access

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = "en-US";

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        // Clear inactivity timer on new speech results
        if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);

        let final = "";
        let interim = "";
        for (let i = event.resultIndex; i < event.results.length; ++i) {
          if (event.results[i].isFinal) {
            final += event.results[i][0].transcript;
          } else {
            interim += event.results[i][0].transcript;
          }
        }
        setFinalTranscript(prev => prev + final);
        setInterimTranscript(interim);
        setStatusMessage("Listening..."); // Update status

        // Restart inactivity timer if results are not final yet OR if they are final but short
        // This allows for brief pauses between phrases.
        if (!event.results[event.results.length - 1].isFinal || final.length < 10) {
             if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current); // Clear previous
             inactivityTimerRef.current = setTimeout(() => {
                 console.log("Inactivity timeout after speech result, stopping.");
                 stopRecordingAndSave();
             }, INACTIVITY_TIMEOUT_MS);
        }
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        // ... (existing error handling)
        cleanupRecognition();
        setIsRecording(false);
        setStatusMessage("Error");
      };

      recognition.onend = () => {
        // This event fires when recognition stops, either manually or automatically.
        // If it wasn't a manual stop (via stopRecordingAndSave), and there's transcript, save it.
        if (isRecording) { // Check isRecording to prevent double-save if stopRecordingAndSave was already called
            console.log("Recognition.onend called while still 'isRecording=true'. This might be an auto-stop by browser or after a final result.");
            // If stopRecordingAndSave hasn't been called by a timer, this is likely a natural end or API timeout.
            // We can check if there's substantial transcript and save.
             const currentFullTranscript = (finalTranscript + interimTranscript).trim();
             if(currentFullTranscript.length > 0){
                // It's often better to rely on onspeechend + timer, but this is a fallback.
                // stopRecordingAndSave(); // This will call onSave if there's transcript
             } else {
                // if onend is called with no transcript and wasn't a manual stop, might be an error or premature end
                // console.log("Recognition ended with no transcript.");
             }
        }
        // No need to call cleanupRecognition() here if stopRecordingAndSave always does it.
        //setIsRecording(false); // This will be set by stopRecordingAndSave or manual stop
      };

      // Key for EOS: onspeechend
      recognition.onspeechend = () => {
        console.log("Speech recognition: onspeechend triggered.");
        setStatusMessage("Silence detected, finishing...");
        if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current); // Clear previous
        inactivityTimerRef.current = setTimeout(() => {
          console.log("Inactivity timeout after onspeechend, stopping.");
          stopRecordingAndSave();
        }, INACTIVITY_TIMEOUT_MS / 2); // Shorter timeout after onspeechend indicates speech has likely finished
      };

      recognition.onaudiostart = () => {
          console.log("Audio capture started");
          setStatusMessage("Listening...");
      };
      
      recognition.onaudioend = () => {
          console.log("Audio capture ended");
          // onspeechend is more reliable for speech, but audioend can be a fallback
          // if (!isRecording) return; // if already stopping
          // setStatusMessage("Processing audio...");
      };


      recognition.start();
      setIsRecording(true);
      setStatusMessage("Listening...");
      setRecordingTime(0);
      if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = setInterval(() => setRecordingTime(prev => prev + 1), 1000);

      // Max duration timer
      if (maxDurationTimerRef.current) clearTimeout(maxDurationTimerRef.current);
      maxDurationTimerRef.current = setTimeout(() => {
        console.log("Max recording duration reached, stopping.");
        toast({ title: "Recording Limit", description: `Max recording time of ${MAX_RECORDING_DURATION_MS / 1000}s reached.`, variant: "default" });
        stopRecordingAndSave();
      }, MAX_RECORDING_DURATION_MS);

    } catch (err) {
      // ... (existing catch block)
      cleanupRecognition();
      setIsRecording(false);
      setStatusMessage("Error initializing");
    }

    return () => {
      cleanupRecognition();
    };
  }, [isOpen, SpeechRecognitionAPI, stopRecordingAndSave, cleanupRecognition, onSave, toast]); // Added toast to dependencies

  // ... (formatTime, handleSave)

  // Manual Stop button
  const handleManualStop = () => {
    console.log("Manual stop clicked.");
    // Set status to processing, then stop and save
    setStatusMessage("Processing...");
    stopRecordingAndSave();
  };

  // Cancel/Delete button
  const handleCancelOrDelete = () => {
    cleanupRecognition();
    setFinalTranscript(""); // Clear any captured transcript
    setInterimTranscript("");
    onClose(); // Close modal
  };


  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) { handleCancelOrDelete(); } }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {error ? "Error" : isRecording ? "Recording Voice Note" : (finalTranscript + interimTranscript).trim() ? "Review Voice Note" : isSupported ? statusMessage : "Unsupported"}
          </DialogTitle>
          {/* ... (existing error and unsupported messages) ... */}
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center justify-center gap-2 text-sm font-medium">
            {isRecording && <span className="h-2 w-2 rounded-full bg-red-500 animate-pulse"></span>}
            <span>{statusMessage} {isRecording && `(${formatTime(recordingTime)})`}</span>
          </div>

          <AudioWaveform isRecording={isRecording} />

          {((finalTranscript + interimTranscript).trim() || (isRecording && !error)) && (
            <div className="mt-4">
              <h3 className="text-sm font-medium mb-2">Transcript:</h3>
              <div className="bg-secondary/50 p-3 rounded-md text-sm min-h-[60px] max-h-[150px] overflow-y-auto">
                <span>{finalTranscript}</span>
                <span className="text-muted-foreground">{interimTranscript}</span>
                {!finalTranscript && !interimTranscript && isRecording && <span className="text-muted-foreground italic">Speak now...</span>}
              </div>
            </div>
          )}

          <div className="flex justify-center gap-4 mt-6">
            {isRecording ? (
              <Button variant="destructive" size="lg" className="rounded-full" onClick={handleManualStop} disabled={!isSupported}>
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            ) : (finalTranscript + interimTranscript).trim() && !error ? (
              <>
                <Button variant="outline" size="lg" className="rounded-full" onClick={handleCancelOrDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
                <Button variant="default" size="lg" className="rounded-full" onClick={() => onSave((finalTranscript + interimTranscript).trim())}>
                  Save Note
                </Button>
              </>
            ) : (
                 <Button variant="outline" size="lg" className="rounded-full" onClick={handleCancelOrDelete}>
                   <X className="h-4 w-4 mr-2" />
                   {error || !isSupported ? "Close" : "Cancel"}
                 </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
```

**III. Implementation Plan - Mobile (Flutter - `mobile/.../recording_modal.dart`)**

The `speech_to_text` package has parameters like `listenFor` (max duration) and `pauseFor` (duration of silence before considering speech ended). We'll leverage `pauseFor`.

```dart
// mobile/flashtasks_mobile/lib/src/features/dashboard/widgets/recording_modal.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import '../../../core/services/speech_service.dart'; // Your existing speech service
// Assuming AudioWaveform is a separate widget for mobile too, or implement here
// import 'package:flashtasks_mobile/src/shared/widgets/audio_waveform_widget.dart';

class RecordingModal extends StatefulWidget {
  const RecordingModal({super.key});

  @override
  State<RecordingModal> createState() => _RecordingModalState();
}

class _RecordingModalState extends State<RecordingModal> {
  final SpeechService _speechService = SpeechService(); // Use your speech service
  bool _isActuallyRecording = false; // More precise state
  bool _isInitialized = false;
  String _finalTranscript = '';
  String _currentInterimTranscript = ''; // For displaying live results
  String _error = '';
  int _recordingSeconds = 0;
  Timer? _recordingTimer;
  String _statusMessage = "Initializing...";

  // Configurable EOS parameters
  final Duration _eosPauseDuration = const Duration(seconds: 2); // How long to wait after speech ends
  final Duration _maxRecordingDuration = const Duration(seconds: 30);

  @override
  void initState() {
    super.initState();
    _initializeSpeech();
  }

  @override
  void dispose() {
    _manualStopRecording(); // Ensure everything is stopped
    _recordingTimer?.cancel();
    _speechService.dispose();
    super.dispose();
  }

  Future<void> _initializeSpeech() async {
    try {
      final available = await _speechService.initialize();
      if (!mounted) return;
      if (available) {
        setState(() {
          _isInitialized = true;
          _error = '';
          _statusMessage = "Tap mic to start";
        });
        // Optionally auto-start recording if desired
        // _startRecording(); 
      } else {
        setState(() {
          _error = 'Speech recognition not available.';
          _isInitialized = false;
          _statusMessage = "Unsupported";
        });
      }
    } catch (e) {
      if (kDebugMode) print('Error initializing speech: $e');
      if (!mounted) return;
      setState(() {
        _error = 'Error initializing speech recognition.';
        _isInitialized = false;
        _statusMessage = "Initialization Error";
      });
    }
  }

  void _startRecording() async {
    if (!_isInitialized || _isActuallyRecording) return;

    // Clear previous transcripts
    setState(() {
      _finalTranscript = '';
      _currentInterimTranscript = '';
      _error = '';
      _recordingSeconds = 0;
      _statusMessage = "Listening...";
      _isActuallyRecording = true; // Set recording true *before* listen
    });

    _recordingTimer?.cancel();
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      setState(() { _recordingSeconds++; });
       // Check for max duration
      if (_recordingSeconds >= _maxRecordingDuration.inSeconds) {
        print("Max recording duration reached in mobile.");
        _autoStopAndSave();
      }
    });

    try {
      await _speechService.startListening(
        onResult: _onSpeechResult,
        onStatus: _onSpeechStatus,
        // onError: _onSpeechError, // speech_to_text handles errors via initialize or status
      );
      // isListening is true now from the package
    } catch (e) {
        if (mounted) {
            setState(() {
                _error = "Failed to start listening: ${e.toString()}";
                _isActuallyRecording = false;
                _statusMessage = "Error starting";
            });
            _recordingTimer?.cancel();
        }
    }
  }

  void _onSpeechResult(String text) {
    if (!mounted) return;
    // speech_to_text's onResult with partialResults=true often gives the full current phrase.
    // If result.finalResult is true, it's a more confirmed segment.
    // For simplicity, we'll just update the current transcript.
    // The package handles distinguishing final and interim internally for its `recognizedWords`.
    setState(() {
      _currentInterimTranscript = text; // Keep this for live feedback
      _statusMessage = "Listening..."; // Reset status if we got a result
    });
  }

  void _onSpeechStatus(String status) {
    if (kDebugMode) print('Speech status: $status');
    if (!mounted) return;

    if (status == 'listening') {
      setState(() {
        _statusMessage = "Listening...";
        if (!_isActuallyRecording) _isActuallyRecording = true; // Sync state
      });
    } else if (status == 'notListening' || status == 'done') {
      // This is a key event. speech_to_text has stopped listening,
      // likely due to its internal `pauseFor` timeout.
      if (_isActuallyRecording) { // If we were actively recording
        print("Speech status indicates end of speech ('$status'). Finalizing.");
        _autoStopAndSave();
      }
    } else if (status == 'error_no_match' || status == 'error_speech_timeout') {
        setState(() {
            _error = "No speech detected or it timed out.";
            _isActuallyRecording = false;
            _statusMessage = "No speech";
        });
        _recordingTimer?.cancel();
    }
  }
  
  // This method is for manual stop by user or max duration
  void _manualStopRecording() {
    if (!_isActuallyRecording) return; // Already stopped or stopping

    _speechService.stopListening(); // Tell the package to stop
    _recordingTimer?.cancel();
    setState(() {
      _isActuallyRecording = false;
      // Use the last known interim transcript as the final one if _speechService.finalText isn't updated yet
      _finalTranscript = _speechService.finalText.isNotEmpty ? _speechService.finalText : _currentInterimTranscript;
      _statusMessage = _finalTranscript.trim().isNotEmpty ? "Review transcript" : "Recording stopped";
    });
  }

  // This method is called when speech_to_text indicates end of speech (e.g. via status 'done' or 'notListening' after 'listening')
  void _autoStopAndSave() {
    if (!_isActuallyRecording) return; // Already stopped or processing

    _speechService.stopListening(); // Ensure the service is stopped
    _recordingTimer?.cancel();
    final transcriptToSave = (_speechService.finalText.isNotEmpty ? _speechService.finalText : _currentInterimTranscript).trim();
    
    setState(() {
        _isActuallyRecording = false;
        _finalTranscript = transcriptToSave;
        _statusMessage = transcriptToSave.isNotEmpty ? "Processing..." : "No speech captured";
    });

    if (transcriptToSave.isNotEmpty) {
        Navigator.of(context).pop(transcriptToSave);
    } else {
        // Optionally give feedback if nothing was captured before closing
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No speech was captured.')),
        );
        // Don't pop immediately, let user see "No speech captured" then they can cancel
    }
  }


  String _formatRecordingTime() {
    final minutes = (_recordingSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_recordingSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  void _saveTranscriptAndClose() {
    _manualStopRecording(); // Ensure recording is fully stopped
    final transcriptToSave = _finalTranscript.trim().isNotEmpty ? _finalTranscript.trim() : _currentInterimTranscript.trim();
    if (transcriptToSave.isNotEmpty) {
      Navigator.of(context).pop(transcriptToSave);
    } else {
      // If transcript is empty after manual stop, just close
      Navigator.of(context).pop();
    }
  }

  void _cancelAndClose() {
    _manualStopRecording();
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayTranscript = _finalTranscript.isNotEmpty ? _finalTranscript : _currentInterimTranscript;

    return AlertDialog(
      title: Row(
        children: [
          Icon(_isInitialized ? LucideIcons.mic : LucideIcons.micOff, color: _isInitialized ? theme.colorScheme.primary : theme.disabledColor),
          const SizedBox(width: 8),
          Text(_isInitialized ? 'Voice Input' : 'Voice Unavailable', style: theme.textTheme.titleLarge),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status message and timer
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (_isActuallyRecording)
                    Container(
                      width: 8, height: 8,
                      decoration: BoxDecoration(color: Colors.redAccent, shape: BoxShape.circle),
                      margin: const EdgeInsets.only(right: 8),
                    ),
                  Text(
                    _isActuallyRecording ? '${_statusMessage} (${_formatRecordingTime()})' : _statusMessage,
                    style: theme.textTheme.bodyMedium?.copyWith(
                        color: _isActuallyRecording ? Colors.redAccent : theme.textTheme.bodySmall?.color
                    ),
                  ),
                ],
              ),
            ),
            
            // Placeholder for a mobile audio waveform - you'd use a package or custom painter
            Container(
              height: 80,
              width: double.infinity,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(child: Icon(LucideIcons.barChartHorizontal, size: 30, color: theme.disabledColor)),
            ),

            // Transcript Display
            if (displayTranscript.isNotEmpty || (_isActuallyRecording && _error.isEmpty))
              Container(
                margin: const EdgeInsets.only(top: 16.0),
                padding: const EdgeInsets.all(12.0),
                width: double.infinity,
                constraints: const BoxConstraints(minHeight: 60, maxHeight: 120),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SingleChildScrollView(
                    child: Text(
                        displayTranscript.isEmpty && _isActuallyRecording ? "Listening..." : displayTranscript,
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                    ),
                ),
              ),
            
            // Error message
            if (_error.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16.0),
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Row(
                  children: [
                    Icon(LucideIcons.alertCircle, color: theme.colorScheme.onErrorContainer, size: 18),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _error,
                        style: TextStyle(color: theme.colorScheme.onErrorContainer, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
      actionsAlignment: MainAxisAlignment.spaceAround,
      actionsPadding: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
      actions: [
        if (_isActuallyRecording)
          OutlinedButton.icon(
            icon: const Icon(LucideIcons.square),
            label: const Text('Stop'),
            onPressed: _manualStopRecording,
            style: OutlinedButton.styleFrom(foregroundColor: Colors.redAccent, side: BorderSide(color: Colors.redAccent.withOpacity(0.5))),
          )
        else if (displayTranscript.isNotEmpty && _error.isEmpty) ...[
          TextButton(
            onPressed: _cancelAndClose, // Changed to cancelAndClose
            child: const Text('Delete'),
            style: TextButton.styleFrom(foregroundColor: theme.colorScheme.error),
          ),
          ElevatedButton(
            onPressed: _saveTranscriptAndClose, // Changed to saveTranscriptAndClose
            child: const Text('Save Note'),
          ),
        ] else // Initial state or error state
          TextButton(
            onPressed: _cancelAndClose,
            child: Text(_isInitialized ? 'Cancel' : 'Close'),
          ),
        
        // Always show a Start/Retry button if not currently recording and initialized
        if (!_isActuallyRecording && _isInitialized && !(displayTranscript.isNotEmpty && _error.isEmpty) )
          ElevatedButton.icon(
             icon: const Icon(LucideIcons.mic),
             label: Text(_finalTranscript.isNotEmpty || _currentInterimTranscript.isNotEmpty ? 'Record Again' : 'Start Recording'),
             onPressed: _startRecording,
             style: ElevatedButton.styleFrom(backgroundColor: theme.colorScheme.primary, foregroundColor: theme.colorScheme.onPrimary),
          ),
      ],
    );
  }
}
```

**IV. Shared Considerations & UI/UX**

1.  **UI Feedback during EOS Detection:**
    *   When `onspeechend` (web) or `notListening` (mobile, after `listening`) is triggered, change the status message in the modal: "Silence detected, finishing up..." or show a small countdown visual (e.g., a shrinking circle or a 2-second progress bar).
    *   This manages user expectation and explains why the recording might stop.

2.  **Configurability (Internal):**
    *   Make `INACTIVITY_TIMEOUT_MS` (web), `_eosPauseDuration` (mobile), and `MAX_RECORDING_DURATION_MS` easily configurable constants at the top of their respective files. This allows for easier tuning during testing.

3.  **Error Handling:**
    *   **`no-speech` / `error_speech_timeout`:** If the speech recognizer stops because no speech was detected for its own internal timeout, ensure the modal reflects this ("No speech detected. Try again?"). Your current error handling seems to cover this.
    *   **Permissions:** Double-check that microphone permission errors are clearly communicated.

4.  **Minimum Speech Length (Optional Refinement):**
    *   You could add a client-side check: if the final transcript after EOS is extremely short (e.g., < 0.5 seconds or < 3 characters) and not a common short command word, you *could* prompt "Was that all?" or discard it automatically. This is more advanced and might be annoying if not tuned well. For now, relying on the `pauseFor` and inactivity timeouts is likely sufficient.

5.  **Testing:**
    *   Test with short phrases ("Okay").
    *   Test with longer sentences with natural pauses.
    *   Test with background noise (though the APIs handle some of this).
    *   Test what happens if the user stays completely silent after starting.
    *   Test the max recording duration.

**Workflow Summary for Smart EOS:**

1.  User opens modal, recording starts (or user taps start). `statusMessage` = "Listening...".
2.  User speaks. Interim results update `_currentInterimTranscript`.
3.  User pauses.
    *   **Web:** `onspeechend` fires. Start `inactivityTimerRef` (e.g., 1 second). `statusMessage` = "Silence detected...".
    *   **Mobile:** `speech_to_text` `onStatus` might change to `notListening` after its internal `pauseFor` duration.
4.  **Scenario A: User resumes speaking quickly.**
    *   **Web:** New `onresult` fires before `inactivityTimerRef` completes. Clear/reset `inactivityTimerRef`. `statusMessage` back to "Listening...".
    *   **Mobile:** `onStatus` changes back to `listening`.
5.  **Scenario B: User is done speaking.**
    *   **Web:** `inactivityTimerRef` (started after `onspeechend`) completes. `stopRecordingAndSave()` is called.
    *   **Mobile:** The `pauseFor` duration in `_speechService.listen()` completes, and the status likely changes to `done` or `notListening`. `_autoStopAndSave()` is called.
6.  `onSave` callback is triggered with the final transcript.

This approach makes the recording process feel more fluid and less reliant on precise user interaction to stop. The key is tuning the `INACTIVITY_TIMEOUT_MS` / `_eosPauseDuration` and `pauseFor` (mobile) values to find a good balance.