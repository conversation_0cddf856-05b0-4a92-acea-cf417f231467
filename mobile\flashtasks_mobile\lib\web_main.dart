import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'src/core/config/api_config.dart';
import 'src/core/services/messaging_service.dart';
import 'src/app.dart'; // Import the main app widget
import 'package:flutter/foundation.dart' show kDebugMode, kReleaseMode;
import 'src/core/platform/platform_helper.dart';

/// Web-specific entry point - Does not import firebase_messaging
void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize the messaging service (web placeholder implementation)
  final messagingService = MessagingService();
  await messagingService.initialize();
  
  // Set up messaging handlers (will use placeholders on web)
  messagingService.setupForegroundNotificationHandling();
  messagingService.setupNotificationTapHandling();

  // Log platform and build mode info
  _logPlatformInfo();

  // Initialize API config to load any saved custom server URL
  await ApiConfig.initialize();

  // Create a ProviderContainer
  final container = ProviderContainer();

  // Set the provider container in ApiConfig
  ApiConfig.setProviderContainer(container);

  runApp(
    // Wrap the app with ProviderScope for Riverpod state management
    ProviderScope(
      parent: container,
      child: const FlashTasksApp(), // Use the imported FlashTasksApp
    ),
  );
}

/// Log detailed platform and environment information for debugging
void _logPlatformInfo() {
  const isRelease = kReleaseMode;
  const isDebug = kDebugMode;

  // Use our platform helper to get platform info
  final platformInfo = PlatformHelper.getPlatformDescription();

  print('🚀 FLASHTASKS STARTING:');
  print('📱 Platform: $platformInfo (WEB)');
  print('🔧 Build mode: ${isRelease ? "RELEASE" : isDebug ? "DEBUG" : "PROFILE"}');
  print('🔗 API URL will be: ${ApiConfig.getBaseUrl()}');
  print('🔒 Production mode: ${ApiConfig.isProduction}');
} 