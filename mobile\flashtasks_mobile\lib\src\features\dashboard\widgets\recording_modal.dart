import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import '../../../core/services/speech_service.dart';

/// Modal dialog for recording voice input
class RecordingModal extends StatefulWidget {
  const RecordingModal({super.key});

  @override
  State<RecordingModal> createState() => _RecordingModalState();
}

class _RecordingModalState extends State<RecordingModal> with TickerProviderStateMixin {
  final SpeechService _speechService = SpeechService();
  bool _isRecording = false;
  bool _isInitialized = false;
  bool _isStartingRecording = false;
  String _finalTranscript = '';
  String _currentTranscript = '';
  String _error = '';
  int _recordingSeconds = 0;
  Timer? _recordingTimer;

  // Animation controllers
  late AnimationController _micPulseController;
  late Animation<double> _micPulseAnimation;
  late AnimationController _startRecordingController;
  late Animation<double> _startRecordingAnimation;

  // Waveform visualization
  List<double> _waveformHeights = List.filled(24, 8.0);
  double _currentSoundLevel = 0.0;

  // UI state
  bool _showBlink = true;
  Timer? _blinkTimer;
  Color _recordingColor = Colors.red.shade700;

  @override
  void initState() {
    super.initState();

    // Set up microphone pulse animation when recording
    _micPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);
    _micPulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _micPulseController, curve: Curves.easeInOut),
    );

    // Create a separate animation for the start recording button
    _startRecordingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _startRecordingAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _startRecordingController, curve: Curves.easeInOut),
    );

    // Set up blinking animation for recording indicator
    _blinkTimer = Timer.periodic(const Duration(milliseconds: 600), (timer) {
      setState(() {
        _showBlink = !_showBlink;
      });
    });

    // Initialize speech and start recording automatically
    _initializeSpeechAndRecord();
  }

  @override
  void dispose() {
    _stopRecording();
    _recordingTimer?.cancel();
    _micPulseController.dispose();
    _startRecordingController.dispose();
    _blinkTimer?.cancel();
    _speechService.dispose();
    super.dispose();
  }

  /// Initialize the speech recognition and start recording automatically
  Future<void> _initializeSpeechAndRecord() async {
    // Show initializing state immediately
    setState(() {
      _isStartingRecording = true;
      _error = '';
    });

    try {
      final available = await _speechService.initialize();
      if (available) {
        setState(() {
          _isInitialized = true;
          _error = '';
        });

        // Automatically start recording after initialization
        _startRecording();
      } else {
        setState(() {
          _isStartingRecording = false;
          _error = 'Speech recognition not available on this device';
          _isInitialized = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing speech: $e');
      }
      setState(() {
        _isStartingRecording = false;
        _error = 'Error initializing speech recognition';
        _isInitialized = false;
      });
    }
  }

  /// Start recording with visual feedback
  void _startRecording() async {
    if (!_isInitialized) {
      setState(() {
        _error = 'Speech recognition not initialized';
      });
      return;
    }

    // Show button press animation
    _startRecordingController.forward().then((_) => _startRecordingController.reverse());

    // Show starting state
    setState(() {
      _isStartingRecording = true;
      _currentTranscript = '';
      _error = '';
    });

    // Start haptic feedback if available
    // HapticFeedback.mediumImpact();

    final success = await _speechService.startListening(
      onResult: _onSpeechResult,
      onStatus: _onSpeechStatus,
      onError: _onSpeechError,
      onSoundLevel: _onSoundLevelChange,
    );

    if (success) {
      setState(() {
        _isStartingRecording = false;
        _isRecording = true;
        _currentTranscript = '';
        _error = '';
        _recordingSeconds = 0;
      });
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingSeconds++;

          // Change color every 15 seconds for better visual feedback
          if (_recordingSeconds % 15 == 0) {
            _recordingColor = _recordingSeconds % 30 == 0 ?
                Colors.red.shade700 : Colors.orange.shade700;
          }
        });
      });
    } else {
      setState(() {
        _isStartingRecording = false;
        _error = 'Failed to start speech recognition';
      });
    }
  }

  /// Stop recording
  void _stopRecording() {
    if (_isRecording) {
      _speechService.stopListening();
      _recordingTimer?.cancel();
      setState(() {
        _isRecording = false;
        _finalTranscript = _speechService.finalText;
        _waveformHeights = List.filled(24, 8.0);
        _currentSoundLevel = 0.0;
      });
    }
  }

  /// Handle speech recognition results
  void _onSpeechResult(String text) {
    setState(() {
      _currentTranscript = text;
    });
  }

  /// Handle speech recognition status changes
  void _onSpeechStatus(String status) {
    if (kDebugMode) {
      print('Speech status: $status');
    }

    if (status == 'listening') {
      // Speech recognition is actively listening
      if (!_isRecording) {
        setState(() {
          _isRecording = true;
        });
      }
    } else if (status == 'notListening' || status == 'done') {
      // This is a key event. speech_to_text has stopped listening,
      // likely due to its internal `pauseFor` timeout (3 seconds of silence).
      if (_isRecording) { // If we were actively recording
        if (kDebugMode) {
          print("Speech status indicates end of speech ('$status'). Auto-stopping and saving.");
        }
        _autoStopAndSave();
      }
    } else if (status == 'error_no_match' || status == 'error_speech_timeout') {
      setState(() {
        _error = "No speech detected or it timed out.";
        _isRecording = false;
      });
      _recordingTimer?.cancel();
    }
  }

  /// Handle speech recognition errors
  void _onSpeechError(String error) {
    if (kDebugMode) {
      print('Speech error: $error');
    }
    setState(() {
      _error = error;
      _isRecording = false;
    });
    _recordingTimer?.cancel();
  }

  /// Format the recording time as MM:SS
  String _formatRecordingTime() {
    final minutes = (_recordingSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_recordingSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  /// Handle sound level changes
  void _onSoundLevelChange(double level) {
    // Update the current sound level
    _currentSoundLevel = level;

    // Update waveform visualization based on sound level
    _updateWaveform();
  }

  /// This method is called when speech_to_text indicates end of speech (e.g. via status 'done' or 'notListening' after 'listening')
  void _autoStopAndSave() {
    if (!_isRecording) return; // Already stopped or processing

    _speechService.stopListening(); // Ensure the service is stopped
    _recordingTimer?.cancel();
    final transcriptToSave = (_speechService.finalText.isNotEmpty ? _speechService.finalText : _currentTranscript).trim();

    setState(() {
        _isRecording = false;
        _finalTranscript = transcriptToSave;
    });

    if (transcriptToSave.isNotEmpty) {
        Navigator.of(context).pop(transcriptToSave);
    } else {
        // Optionally give feedback if nothing was captured before closing
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No speech was captured.')),
        );
        // Don't pop immediately, let user see "No speech captured" then they can cancel
    }
  }

  /// Save the transcript and close the dialog
  void _saveTranscript() {
    _stopRecording();
    Navigator.of(context).pop(_finalTranscript.isEmpty ? _currentTranscript : _finalTranscript);
  }

  /// Cancel recording and close the dialog
  void _cancelRecording() {
    _stopRecording();
    Navigator.of(context).pop();
  }

  void _updateWaveform() {
    if (!mounted) return;

    setState(() {
      // Create a more natural-looking waveform based on sound level
      // Center bars are taller, and amplitude is determined by current sound level
      const int numBars = 24;
      const double baseHeight = 6.0;
      final double maxAmplitude = 30.0 * _currentSoundLevel;

      _waveformHeights = List.generate(numBars, (i) {
        // Create a bell curve pattern with random variation
        double position = i / (numBars - 1); // 0.0 to 1.0
        double bellCurve = 1.0 - (2.0 * position - 1.0) * (2.0 * position - 1.0); // 0.0 to 1.0 to 0.0

        // Add some randomness for a more natural look
        double randomFactor = 0.7 + (0.3 * (DateTime.now().millisecondsSinceEpoch % (i+5)) / 5);

        // Calculate final height
        return baseHeight + (maxAmplitude * bellCurve * randomFactor);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(_isRecording ? LucideIcons.mic : LucideIcons.mic,
              color: _isRecording ? _recordingColor : null),
          const SizedBox(width: 8),
          Text(_isRecording ? 'Recording...' :
               _isStartingRecording ? 'Starting Recording...' :
               _isInitialized ? 'Voice Input' : 'Voice Input Unavailable',
               style: TextStyle(
                 color: _isRecording ? _recordingColor : null,
                 fontWeight: _isRecording ? FontWeight.bold : null,
               )),
        ],
      ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
            // Recording status with enhanced visual feedback
            // Now shows a large, prominent recording indicator instead of a button
            Stack(
              alignment: Alignment.center,
              children: [
                // Outer ripple effect for recording
                if (_isRecording)
                  AnimatedBuilder(
                    animation: _micPulseController,
                    builder: (context, child) {
                      return Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _recordingColor.withOpacity(0.1 * _micPulseAnimation.value),
                        ),
                      );
                    },
                  ),

                // Main recording indicator with animation
                AnimatedBuilder(
                  animation: _isRecording ? _micPulseController : _startRecordingAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isRecording ?
                          _micPulseAnimation.value :
                          (_isStartingRecording ? 0.95 : _startRecordingAnimation.value),
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _isRecording
                              ? _recordingColor.withOpacity(0.2)
                              : _isStartingRecording
                                  ? Colors.orange.withOpacity(0.2)
                                  : Theme.of(context).colorScheme.primaryContainer,
                          border: Border.all(
                            color: _isRecording
                                ? _recordingColor
                                : _isStartingRecording
                                    ? Colors.orange
                                    : Theme.of(context).colorScheme.primary,
                            width: 2.0,
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            _isRecording
                                ? LucideIcons.mic
                                : _isStartingRecording
                                    ? LucideIcons.loader
                                    : (_isInitialized ? LucideIcons.mic : LucideIcons.micOff),
                            color: _isRecording
                                ? _recordingColor
                                : _isStartingRecording
                                    ? Colors.orange
                                    : Theme.of(context).colorScheme.onPrimaryContainer,
                            size: 40.0,
                          ),
                        ),
                      ),
                    );
                  },
                ),

                // Extra visual indicator when starting
                if (_isStartingRecording)
                  const CircularProgressIndicator(),
              ],
            ),
            // Recording indicator with improved visual feedback
            if (_isRecording)
              Padding(
                padding: const EdgeInsets.only(top: 10.0, bottom: 2.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Blinking recording indicator
                    AnimatedOpacity(
                      opacity: _showBlink ? 1.0 : 0.2,
                      duration: const Duration(milliseconds: 300),
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: _recordingColor,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: _recordingColor.withOpacity(0.5),
                              blurRadius: 4,
                              spreadRadius: 1
                            )
                          ]
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Recording...',
                      style: TextStyle(
                        color: _recordingColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

            // Starting indicator
            if (_isStartingRecording)
              Padding(
                padding: const EdgeInsets.only(top: 10.0, bottom: 2.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Getting ready...',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            // Recording time
            if (_isRecording)
              Padding(
                padding: const EdgeInsets.only(top: 2.0, bottom: 2.0),
                child: Text(
                  _formatRecordingTime(),
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
            // Enhanced audio waveform visualization
            if (_isRecording)
              Container(
                margin: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                height: 40,
                width: 240,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _waveformHeights.map((h) => Container(
                    width: 6,
                    height: h,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      color: _recordingColor.withOpacity(0.7 + (0.3 * h / 40)),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  )).toList(),
                ),
              ),
            // Transcript
            if (_currentTranscript.isNotEmpty || _finalTranscript.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16.0),
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Text(
                  _finalTranscript.isEmpty ? _currentTranscript : _finalTranscript,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            // Error message
            if (_error.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16.0),
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Text(
                  _error,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                  ),
                ),
              ),
          ],
        ),
      ),
      actions: [
        // Cancel button - always visible
        TextButton(
          onPressed: _cancelRecording,
          child: Text(_isRecording ? 'Stop' : 'Cancel'),
        ),
        // Save button - only visible when there's text to save
        if (_isInitialized && (_currentTranscript.isNotEmpty || _finalTranscript.isNotEmpty))
          TextButton(
            onPressed: _saveTranscript,
            style: TextButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            ),
            child: const Text('Save'),
          ),
      ],
    );
  }
}
