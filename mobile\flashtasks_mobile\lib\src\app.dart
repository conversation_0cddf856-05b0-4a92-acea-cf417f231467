import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/config/app_theme.dart';
import 'core/routing/app_router.dart';
import 'core/services/permissions_service.dart';
import 'core/services/notification_service.dart';
import 'core/platform/platform_helper.dart';

class FlashTasksApp extends ConsumerStatefulWidget {
  const FlashTasksApp({super.key});

  @override
  ConsumerState<FlashTasksApp> createState() => _FlashTasksAppState();
}

class _FlashTasksAppState extends ConsumerState<FlashTasksApp> {
  @override
  void initState() {
    super.initState();
    // Check permissions and initialize services after app is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAppPermissions();
      _initializeNotifications();
    });
  }

  // Initialize notification services
  Future<void> _initializeNotifications() async {
    if (!PlatformHelper.isWeb) {
      try {
        // Initialize notification service
        final notificationService = ref.read(notificationServiceProvider);
        await notificationService.initialize();

        // Get FCM token for debugging
        final token = await notificationService.getDeviceToken();
        debugPrint('FCM Token: $token');

        // Subscribe to topics based on user role or preferences
        // This could be moved to after user authentication
        await notificationService.subscribeToTopic('all_users');
      } catch (e) {
        debugPrint('Error initializing notifications: $e');
      }
    }
  }

  // Check if app has required permissions
  Future<void> _checkAppPermissions() async {
    // This allows for more elegant permission handling when specific features are used,
    // rather than forcing permissions on startup
    // Basic permissions are already requested in main.dart
    final hasLocation = await PermissionsService.hasLocationPermission();
    final hasMicrophone = await PermissionsService.hasMicrophonePermission();

    debugPrint('Permissions status - Location: $hasLocation, Microphone: $hasMicrophone');
  }

  @override
  Widget build(BuildContext context) {
    // Main app with routing
    final router = ref.watch(routerProvider);

    return MaterialApp.router(
      title: 'FlashTasks',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark, // Force dark theme to match web app
      routerConfig: router,
    );
  }
}
