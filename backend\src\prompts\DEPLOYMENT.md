# Prompt System Deployment Guide

## Critical Requirements

The prompt system requires YAML files to be copied to the build directory. The standard TypeScript compiler (`tsc`) **does not** copy non-TS files to the build output. Without this step, the application will fail with errors like:

```
[PromptService] Prompt key "processQuickAdd" not found.
[processQuickAdd] Failed to format prompt: processQuickAdd
Server error: {...}
```

## Required Build Steps

The following build steps must be included in your deployment pipeline:

1. **Install copyfiles**: `yarn add copyfiles --dev` or `npm install copyfiles --save-dev`
2. **Add copy-files script**: In package.json, ensure these scripts are present:
   ```json
   "build": "tsc && yarn copy-files",
   "build:prod": "tsc && yarn copy-files",
   "copy-files": "copyfiles -u 1 \"src/**/*.yaml\" dist"
   ```
3. **For Render deployment**: Use the specialized script that explicitly installs copyfiles before building:
   ```json
   "render-build": "yarn add copyfiles --dev && tsc && yarn copy-files"
   ```

## Render Deployment Configuration

For Render deployment, the `.render-buildpacks.json` file should be configured to:

```json
{
  "packages": {
    "devDependencies": ["@types/jest"]
  },
  "buildCommand": "yarn render-build"
}
```

This ensures that the copyfiles package is installed at build time even if the cache doesn't include it.

## Troubleshooting

If prompt errors occur in production:

1. Verify the YAML file exists at `dist/prompts/aiPrompts.yaml`
2. Check that the build script includes the copy-files step
3. Ensure all necessary prompts are defined in the YAML file
4. Check for YAML syntax errors
5. If on Render, verify the build logs show successful execution of the copyfiles command

## How It Works

The `PromptService` loads prompts from `dist/prompts/aiPrompts.yaml` at runtime. If this file is missing or incomplete, features like quick-add task creation and AI categorization will fail. 