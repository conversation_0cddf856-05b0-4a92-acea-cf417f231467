import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:flutter/foundation.dart' show kDebugMode;
import 'dart:io' show Platform;
import 'dart:convert';

/// Service for scheduling and managing reminder notifications
class ReminderService {
  static final ReminderService _instance = ReminderService._internal();

  factory ReminderService() => _instance;

  ReminderService._internal();

  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  /// Initialize the reminder service
  Future<void> initialize() async {
    // Initialize timezone
    tz_data.initializeTimeZones();

    try {
      // Set local timezone using local
      tz.setLocalLocation(tz.local);
      if (kDebugMode) {
        print('Set timezone to local: ${tz.local.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error setting timezone: $e. Using default timezone.');
      }
    }

    // Configure notification channel for Android with sound and vibration
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'reminders_channel',
      'Task Reminders',
      description: 'Notifications for task reminders',
      importance: Importance.max, // Use max importance for alarms
      playSound: true,
      enableVibration: true,
      showBadge: true,
      enableLights: true,
    );

    // Create the notification channel on Android devices
    final androidPlugin = _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      await androidPlugin.createNotificationChannel(channel);

      // For Android, check and request notification permissions
      if (Platform.isAndroid) {
        try {
          // Check if notifications are enabled
          final bool? notificationsEnabled = await androidPlugin.areNotificationsEnabled();

          if (kDebugMode) {
            print('Notifications enabled: $notificationsEnabled');
          }

          if (notificationsEnabled != true) {
            if (kDebugMode) {
              print('⚠️ Notifications not enabled. Attempting to request permission...');
            }

            // Note: We can't directly request permission in newer plugin versions
            // The user needs to grant permission manually in device settings
            if (kDebugMode) {
              print('Notification permissions may need to be granted manually in device settings');
              print('Go to Settings > Apps > FlashTasks > Notifications and enable notifications');
              print('For exact alarms, go to Settings > Apps > FlashTasks > Permissions > Alarms & Reminders');
            }

            // Check again after request
            final bool? permissionAfterRequest = await androidPlugin.areNotificationsEnabled();

            if (kDebugMode) {
              if (permissionAfterRequest == true) {
                print('✅ Notification permission granted after request');
              } else {
                print('⚠️ Notification permission still not granted after request');
                print('Please enable notifications in Android Settings > Apps > FlashTasks > Notifications');
              }
            }
          } else {
            if (kDebugMode) {
              print('✅ Notification permission already granted');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error checking/requesting notification permission: $e');
          }
        }

        if (kDebugMode) {
          print('Android notification channel created: reminders_channel');
        }
      }
    }

    // Initialize the plugin with settings
    await _localNotifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        ),
      ),
      onDidReceiveNotificationResponse: _handleNotificationTap,
    );

    if (kDebugMode) {
      print('ReminderService initialized successfully');
    }
  }

  /// Schedule a reminder notification
  Future<int> scheduleReminder({
    required String title,
    required String body,
    required DateTime scheduledDate,
    required String taskId,
    int? reminderId,
  }) async {
    // Generate a unique ID for this reminder
    final id = reminderId ?? DateTime.now().millisecondsSinceEpoch.hashCode;

    try {
      // Ensure the scheduled date is in the future
      final now = DateTime.now();
      DateTime effectiveDate = scheduledDate;

      if (scheduledDate.isBefore(now)) {
        if (kDebugMode) {
          print('Warning: Scheduled date is in the past. Using current time + 5 seconds instead.');
        }
        // Use current time + 5 seconds as a fallback
        effectiveDate = now.add(const Duration(seconds: 5));
      }

      // Convert to TZDateTime
      final scheduledTzDateTime = tz.TZDateTime.from(effectiveDate, tz.local);

      if (kDebugMode) {
        print('🔔 Scheduling reminder for: ${scheduledTzDateTime.toString()}');
        print('🕐 Current time: ${tz.TZDateTime.now(tz.local).toString()}');
        print('⏱️ Time difference: ${scheduledTzDateTime.difference(tz.TZDateTime.now(tz.local)).inSeconds} seconds');
        print('🆔 Notification ID: $id');
      }

      // Create a JSON payload for the notification
      final payload = json.encode({
        'type': 'reminder',
        'taskId': taskId,
        'timestamp': effectiveDate.millisecondsSinceEpoch,
      });

      // Determine the best scheduling strategy based on time difference
      final timeDifference = scheduledTzDateTime.difference(tz.TZDateTime.now(tz.local));

      if (kDebugMode) {
        print('⏰ Time until notification: ${timeDifference.inSeconds} seconds');
      }

      // For very short delays (< 10 seconds), use a different approach
      if (timeDifference.inSeconds < 10) {
        if (kDebugMode) {
          print('🚀 Using immediate scheduling for short delay (${timeDifference.inSeconds}s)');
        }

        // Use a simple delayed notification for very short times
        Future.delayed(timeDifference, () async {
          try {
            if (kDebugMode) {
              print('⏰ About to show delayed notification with ID: $id');
              print('📱 Notification details: title="$title", body="$body"');
            }

            await _localNotifications.show(
              id,
              title,
              body,
              NotificationDetails(
                android: AndroidNotificationDetails(
                  'reminders_channel',
                  'Task Reminders',
                  channelDescription: 'Notifications for task reminders',
                  importance: Importance.max,
                  priority: Priority.max,
                  playSound: true,
                  enableVibration: true,
                  visibility: NotificationVisibility.public,
                  showWhen: true,
                  when: DateTime.now().millisecondsSinceEpoch,
                  usesChronometer: false,
                  fullScreenIntent: false,
                  category: AndroidNotificationCategory.reminder,
                  ticker: title,
                  autoCancel: true,
                  ongoing: false,
                  silent: false,
                  channelShowBadge: true,
                ),
                iOS: const DarwinNotificationDetails(
                  presentAlert: true,
                  presentBadge: true,
                  presentSound: true,
                  interruptionLevel: InterruptionLevel.active,
                ),
              ),
              payload: payload,
            );

            if (kDebugMode) {
              print('✅ Delayed notification shown with ID: $id');

              // Verify the notification was actually shown by checking active notifications
              try {
                final activeNotifications = await _localNotifications
                    .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
                    ?.getActiveNotifications();

                if (activeNotifications != null) {
                  final ourNotification = activeNotifications.where((n) => n.id == id).toList();
                  if (ourNotification.isNotEmpty) {
                    print('✅ Notification verified as active: ${ourNotification.first.title}');
                  } else {
                    print('⚠️ Notification not found in active notifications list');
                  }
                } else {
                  print('⚠️ Could not retrieve active notifications');
                }
              } catch (verifyError) {
                print('⚠️ Error verifying notification: $verifyError');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('❌ Error showing delayed notification: $e');
              print('❌ Error type: ${e.runtimeType}');
              print('❌ Error details: ${e.toString()}');
            }
          }
        });

        if (kDebugMode) {
          print('✅ Delayed notification scheduled with ID: $id');
        }
        return id;
      }

      // For longer delays, try different scheduling modes
      bool scheduled = false;

      // Strategy 1: For medium delays (10 seconds to 2 minutes), try exactAllowWhileIdle first
      if (!scheduled && timeDifference.inSeconds >= 10 && timeDifference.inMinutes <= 2) {
        try {
          if (kDebugMode) {
            print('🎯 Trying exactAllowWhileIdle for medium delay (${timeDifference.inSeconds}s)');
          }

          await _localNotifications.zonedSchedule(
            id,
            title,
            body,
            scheduledTzDateTime,
            NotificationDetails(
              android: AndroidNotificationDetails(
                'reminders_channel',
                'Task Reminders',
                channelDescription: 'Notifications for task reminders',
                importance: Importance.max,
                priority: Priority.max,
                playSound: true,
                enableVibration: true,
                visibility: NotificationVisibility.public,
                showWhen: true,
                when: scheduledTzDateTime.millisecondsSinceEpoch,
                category: AndroidNotificationCategory.reminder,
                ticker: title,
                autoCancel: true,
                ongoing: false,
                silent: false,
                channelShowBadge: true,
              ),
              iOS: const DarwinNotificationDetails(
                presentAlert: true,
                presentBadge: true,
                presentSound: true,
                interruptionLevel: InterruptionLevel.active,
              ),
            ),
            uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
            androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
            payload: payload,
          );

          scheduled = true;
          if (kDebugMode) {
            print('✅ Successfully scheduled with exactAllowWhileIdle mode, ID: $id');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ exactAllowWhileIdle failed: $e');
          }
        }
      }

      // Strategy 2: For longer delays or if exactAllowWhileIdle failed, try alarmClock mode
      if (!scheduled) {
        try {
          if (kDebugMode) {
            print('🎯 Trying alarmClock mode for delay (${timeDifference.inSeconds}s)');
          }

          await _localNotifications.zonedSchedule(
            id,
            title,
            body,
            scheduledTzDateTime,
            NotificationDetails(
              android: AndroidNotificationDetails(
                'reminders_channel',
                'Task Reminders',
                channelDescription: 'Notifications for task reminders',
                importance: Importance.max,
                priority: Priority.max,
                playSound: true,
                enableVibration: true,
                visibility: NotificationVisibility.public,
                showWhen: true,
                when: scheduledTzDateTime.millisecondsSinceEpoch,
                category: AndroidNotificationCategory.alarm,
                ticker: title,
                autoCancel: true,
                ongoing: false,
                silent: false,
                channelShowBadge: true,
              ),
              iOS: const DarwinNotificationDetails(
                presentAlert: true,
                presentBadge: true,
                presentSound: true,
                interruptionLevel: InterruptionLevel.timeSensitive,
              ),
            ),
            uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
            androidScheduleMode: AndroidScheduleMode.alarmClock,
            payload: payload,
          );

          scheduled = true;
          if (kDebugMode) {
            print('✅ Successfully scheduled with alarmClock mode, ID: $id');
          }
        } catch (exactAlarmError) {
          if (kDebugMode) {
            print('⚠️ alarmClock mode failed: $exactAlarmError');
            print('⚠️ Error details: ${exactAlarmError.toString()}');
          }
        }
      }

      // Strategy 3: inexactAllowWhileIdle (fallback - less reliable but should work)
      if (!scheduled) {
        try {
          if (kDebugMode) {
            print('🎯 Trying inexactAllowWhileIdle as fallback for delay (${timeDifference.inSeconds}s)');
          }

          await _localNotifications.zonedSchedule(
            id,
            title,
            body,
            scheduledTzDateTime,
            NotificationDetails(
              android: AndroidNotificationDetails(
                'reminders_channel',
                'Task Reminders',
                channelDescription: 'Notifications for task reminders',
                importance: Importance.max,
                priority: Priority.max,
                playSound: true,
                enableVibration: true,
                visibility: NotificationVisibility.public,
                showWhen: true,
                when: scheduledTzDateTime.millisecondsSinceEpoch,
                category: AndroidNotificationCategory.reminder,
                ticker: title,
                autoCancel: true,
                ongoing: false,
                silent: false,
                channelShowBadge: true,
              ),
              iOS: const DarwinNotificationDetails(
                presentAlert: true,
                presentBadge: true,
                presentSound: true,
                interruptionLevel: InterruptionLevel.active,
              ),
            ),
            uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
            androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
            payload: payload,
          );

          scheduled = true;
          if (kDebugMode) {
            print('✅ Successfully scheduled with inexactAllowWhileIdle mode, ID: $id');
            print('⚠️ Note: inexactAllowWhileIdle may be delayed by Android battery optimization');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ inexactAllowWhileIdle failed: $e');
          }
        }
      }

      // Strategy 4: If all scheduling fails, create a hybrid approach
      if (!scheduled) {
        if (kDebugMode) {
          print('🚨 All scheduling methods failed, trying hybrid approach');
        }

        // For delays under 5 minutes, try a combination approach
        if (timeDifference.inMinutes < 5) {
          try {
            // Schedule with the most permissive mode
            await _localNotifications.zonedSchedule(
              id,
              title,
              body,
              scheduledTzDateTime,
              NotificationDetails(
                android: AndroidNotificationDetails(
                  'reminders_channel',
                  'Task Reminders',
                  channelDescription: 'Notifications for task reminders',
                  importance: Importance.max,
                  priority: Priority.max,
                  playSound: true,
                  enableVibration: true,
                  visibility: NotificationVisibility.public,
                  showWhen: true,
                  when: scheduledTzDateTime.millisecondsSinceEpoch,
                  ticker: title,
                  autoCancel: true,
                  ongoing: false,
                  silent: false,
                  channelShowBadge: true,
                ),
                iOS: const DarwinNotificationDetails(
                  presentAlert: true,
                  presentBadge: true,
                  presentSound: true,
                ),
              ),
              uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
              // Don't specify androidScheduleMode, let the plugin decide
              payload: payload,
            );

            scheduled = true;
            if (kDebugMode) {
              print('✅ Successfully scheduled with default mode (plugin decides), ID: $id');
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ Default scheduling mode failed: $e');
            }
          }
        }
      }

      if (!scheduled) {
        throw Exception('All scheduling methods failed');
      }

      return id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error scheduling reminder: $e');
      }

      // Show an immediate notification instead as a last resort
      try {
        if (kDebugMode) {
          print('🚨 Showing immediate notification as fallback');
        }

        await _localNotifications.show(
          id,
          title,
          body,
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'reminders_channel',
              'Task Reminders',
              channelDescription: 'Notifications for task reminders',
              importance: Importance.max,
              priority: Priority.max,
              playSound: true,
              enableVibration: true,
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
            ),
          ),
          payload: json.encode({
            'type': 'reminder',
            'taskId': taskId,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
            'fallback': true,
          }),
        );

        if (kDebugMode) {
          print('✅ Showed immediate notification as fallback with ID: $id');
        }
      } catch (fallbackError) {
        if (kDebugMode) {
          print('❌ Even fallback notification failed: $fallbackError');
        }
      }

      return id; // Return the ID even if scheduling failed
    }
  }

  /// Cancel a specific reminder by ID
  Future<void> cancelReminder(int id) async {
    await _localNotifications.cancel(id);
    if (kDebugMode) {
      print('Cancelled reminder with ID: $id');
    }
  }

  /// Cancel all scheduled reminders
  Future<void> cancelAllReminders() async {
    await _localNotifications.cancelAll();
    if (kDebugMode) {
      print('Cancelled all reminders');
    }
  }

  /// Handle notification tap
  void _handleNotificationTap(NotificationResponse response) {
    try {
      final payload = response.payload;
      if (payload != null) {
        if (kDebugMode) {
          print('Reminder notification tapped with payload: $payload');
        }

        try {
          // Parse the JSON payload
          final Map<String, dynamic> data = json.decode(payload);
          final String? taskId = data['taskId'];
          final String? type = data['type'];

          if (kDebugMode) {
            print('Parsed notification payload: type=$type, taskId=$taskId');
          }

          // Note: In a real implementation, we would navigate to the task details screen
          // This would typically be done through a navigation service or router
          // For now, we just log the information
        } catch (parseError) {
          if (kDebugMode) {
            print('Error parsing notification payload: $parseError');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling notification tap: $e');
      }
    }
  }

  /// Check if we have notification permissions (especially important for iOS)
  Future<bool> checkPermissions() async {
    final settings = await _localNotifications
        .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );

    return settings ?? false;
  }

  /// Check if exact alarm permissions are granted (Android 12+)
  Future<bool> checkExactAlarmPermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final androidPlugin = _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        // Check if exact alarms are allowed
        final bool? canScheduleExactAlarms = await androidPlugin.canScheduleExactNotifications();

        if (kDebugMode) {
          print('📱 Can schedule exact alarms: $canScheduleExactAlarms');
        }

        return canScheduleExactAlarms ?? false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking exact alarm permissions: $e');
      }
    }

    return false;
  }

  /// Request exact alarm permissions (Android 12+)
  Future<bool> requestExactAlarmPermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final androidPlugin = _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        // Request exact alarm permissions
        final bool? result = await androidPlugin.requestExactAlarmsPermission();

        if (kDebugMode) {
          print('📱 Exact alarm permission request result: $result');
        }

        return result ?? false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting exact alarm permissions: $e');
      }
    }

    return false;
  }

  /// Get pending notifications for debugging
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      final pending = await _localNotifications.pendingNotificationRequests();

      if (kDebugMode) {
        print('📋 Pending notifications: ${pending.length}');
        for (final notification in pending) {
          print('  - ID: ${notification.id}, Title: ${notification.title}');
        }
      }

      return pending;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting pending notifications: $e');
      }
      return [];
    }
  }

  /// Get active notifications for debugging
  Future<List<ActiveNotification>> getActiveNotifications() async {
    try {
      final androidPlugin = _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        final active = await androidPlugin.getActiveNotifications();

        if (kDebugMode) {
          print('📱 Active notifications: ${active.length}');
          for (final notification in active) {
            print('  - ID: ${notification.id}, Title: ${notification.title}');
          }
        }

        return active;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting active notifications: $e');
      }
    }

    return [];
  }

  /// Test immediate notification to verify the system is working
  Future<bool> testImmediateNotification() async {
    try {
      final testId = DateTime.now().millisecondsSinceEpoch.hashCode;

      if (kDebugMode) {
        print('🧪 Testing immediate notification with ID: $testId');
      }

      await _localNotifications.show(
        testId,
        'Test Notification',
        'This is a test to verify notifications are working',
        NotificationDetails(
          android: AndroidNotificationDetails(
            'reminders_channel',
            'Task Reminders',
            channelDescription: 'Notifications for task reminders',
            importance: Importance.max,
            priority: Priority.max,
            playSound: true,
            enableVibration: true,
            visibility: NotificationVisibility.public,
            showWhen: true,
            when: DateTime.now().millisecondsSinceEpoch,
            category: AndroidNotificationCategory.reminder,
            ticker: 'Test Notification',
            autoCancel: true,
            ongoing: false,
            silent: false,
            channelShowBadge: true,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
            interruptionLevel: InterruptionLevel.active,
          ),
        ),
        payload: json.encode({
          'type': 'test',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }),
      );

      if (kDebugMode) {
        print('✅ Test notification sent');

        // Wait a moment and check if it's active
        await Future.delayed(const Duration(milliseconds: 500));
        final active = await getActiveNotifications();
        final testNotification = active.where((n) => n.id == testId).toList();

        if (testNotification.isNotEmpty) {
          print('✅ Test notification verified as active');
          return true;
        } else {
          print('⚠️ Test notification not found in active notifications');
          return false;
        }
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error testing immediate notification: $e');
      }
      return false;
    }
  }

  /// Check Android battery optimization status
  Future<Map<String, dynamic>> checkBatteryOptimizationStatus() async {
    final result = <String, dynamic>{
      'canScheduleExactAlarms': false,
      'batteryOptimizationIgnored': false,
      'recommendations': <String>[],
    };

    if (!Platform.isAndroid) {
      result['canScheduleExactAlarms'] = true;
      result['batteryOptimizationIgnored'] = true;
      return result;
    }

    try {
      final androidPlugin = _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        // Check exact alarm permissions
        final canScheduleExact = await androidPlugin.canScheduleExactNotifications();
        result['canScheduleExactAlarms'] = canScheduleExact ?? false;

        if (kDebugMode) {
          print('🔋 Battery optimization check:');
          print('  - Can schedule exact alarms: ${result['canScheduleExactAlarms']}');
        }

        // Add recommendations based on findings
        if (!(result['canScheduleExactAlarms'] as bool)) {
          result['recommendations'].add('Enable "Alarms & reminders" permission in app settings');
          result['recommendations'].add('Go to Settings > Apps > FlashTasks > Battery > Allow background activity');
        }

        // Note: We can't directly check battery optimization status via flutter_local_notifications
        // but we can provide guidance
        result['recommendations'].add('Disable battery optimization for FlashTasks in Android settings');
        result['recommendations'].add('Add FlashTasks to "Never sleeping apps" list');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking battery optimization: $e');
      }
      result['recommendations'].add('Unable to check battery settings - please check manually');
    }

    return result;
  }

  /// Test scheduled notification with detailed monitoring
  Future<int> testScheduledNotification({
    required int delaySeconds,
    String? customTitle,
    String? customBody,
  }) async {
    final title = customTitle ?? 'Test ${delaySeconds}s Notification';
    final body = customBody ?? 'This notification was scheduled for $delaySeconds seconds delay';
    final scheduledTime = DateTime.now().add(Duration(seconds: delaySeconds));

    if (kDebugMode) {
      print('🧪 Testing scheduled notification:');
      print('  - Delay: ${delaySeconds}s');
      print('  - Scheduled for: $scheduledTime');
      print('  - Title: $title');
    }

    // Check battery optimization before scheduling
    final batteryStatus = await checkBatteryOptimizationStatus();
    if (kDebugMode) {
      print('🔋 Battery status: $batteryStatus');
    }

    // Schedule the notification
    final notificationId = await scheduleReminder(
      title: title,
      body: body,
      scheduledDate: scheduledTime,
      taskId: 'test-${DateTime.now().millisecondsSinceEpoch}',
    );

    if (kDebugMode) {
      print('✅ Test notification scheduled with ID: $notificationId');

      // Check if it appears in pending notifications
      await Future.delayed(const Duration(milliseconds: 500));
      final pending = await getPendingNotifications();
      final ourNotification = pending.where((n) => n.id == notificationId).toList();

      if (ourNotification.isNotEmpty) {
        print('✅ Test notification found in pending list');
      } else {
        print('⚠️ Test notification NOT found in pending list - this may indicate a scheduling issue');
      }
    }

    return notificationId;
  }
}