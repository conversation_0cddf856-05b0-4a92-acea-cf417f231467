name: flashtasks_mobile
description: "FlashTasks AI Mobile App"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # Firebase: Try versions suggested by resolver as a starting point for compatibility
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  firebase_auth: ^5.5.3  # For Firebase Authentication

  flutter_local_notifications: ^17.2.0
  timezone: ^0.9.2  # For handling timezone in scheduled notifications

  # Social login
  google_sign_in: ^6.2.1

  # Speech-to-text with conditional dependency (only used on mobile platforms via export trick)
  speech_to_text: ^7.0.0
  flutter_secure_storage: ^9.0.0

  # Logging
  logger: ^2.0.2+1

  # --- All your other dependencies ---
  cupertino_icons: ^1.0.2
  gap: ^3.0.1
  lucide_icons: ^0.257.0
  google_fonts: ^6.1.0
  flutter_colorpicker: ^1.0.3
  flutter_map: ^6.1.0
  latlong2: ^0.9.1
  flutter_map_location_marker: ^8.0.0
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  dio: ^5.8.0+1
  go_router: ^13.2.5
  shared_preferences: ^2.5.3
  intl: ^0.19.0
  permission_handler: ^11.4.0
  geolocator: ^11.0.0
  avatar_glow: ^3.0.1
  flutter_dotenv: ^5.1.0
  flutter_typeahead: ^5.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  riverpod_generator: ^2.6.5
  build_runner: ^2.4.6
  mockito: ^5.4.5
  http_mock_adapter: ^0.6.1

# Override transitive dependencies to resolve conflicts
dependency_overrides:
  js: ^0.6.3
  collection: ^1.17.2
  firebase_messaging_web:
    path: ./dummy_firebase_messaging_web

# Flutter configuration
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
