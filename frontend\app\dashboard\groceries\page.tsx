"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useGrocery } from "@/lib/grocery-context";
import { GroceryItem as GroceryItemType, groceryService } from "@/lib/grocery-service"; // Import type and service
import { GroceryListHeader } from "@/components/grocery/grocery-list-header";
import { GroceryCollaborationPanel } from "@/components/grocery/grocery-collaboration-panel";
import { SharedListsBrowser } from "@/components/grocery/shared-lists-browser";
import QuickInput from "@/components/quick-input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  ShoppingCart,
  Plus,
  Loader2,
  Search,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Trash2,
  <PERSON><PERSON>s,
  Utensils,
  BarChart4,
  CalendarClock,
  Repeat,
  Tag,
  Filter,
  ArrowUpDown,
  Zap,
  Carrot,
  Milk,
  Beef,
  Cookie,
  Coffee,
  Wheat,
  CroissantIcon as Bread,
  Refrigerator,
  Pill,
  Scissors,
  Printer,
  Share2,
  Clock8,
  AlertCircle,
  Info,
  HelpCircle,
  Lightbulb,
  ClipboardCheck,
  LayoutGrid,
  PlusCircle,
  List as ListIcon,
} from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { Menu } from "@/components/ui/menu";
import { formatDistanceToNow } from 'date-fns';
import { useDebounce } from '@/hooks/use-debounce'; // Assuming a debounce hook exists
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"; // For dropdown
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// Mock data for grocery categories (Keep for icons/colors)
const groceryCategories = [
  { id: 1, name: "Produce", icon: <Carrot className="h-4 w-4" />, color: "bg-green-500" },
  { id: 2, name: "Dairy", icon: <Milk className="h-4 w-4" />, color: "bg-blue-500" },
  { id: 3, name: "Meat", icon: <Beef className="h-4 w-4" />, color: "bg-red-500" },
  { id: 4, name: "Bakery", icon: <Bread className="h-4 w-4" />, color: "bg-amber-500" },
  { id: 5, name: "Pantry", icon: <Wheat className="h-4 w-4" />, color: "bg-orange-500" },
  { id: 6, name: "Frozen", icon: <Refrigerator className="h-4 w-4" />, color: "bg-cyan-500" },
  { id: 7, name: "Beverages", icon: <Coffee className="h-4 w-4" />, color: "bg-purple-500" },
  { id: 8, name: "Snacks", icon: <Cookie className="h-4 w-4" />, color: "bg-pink-500" },
  { id: 9, name: "Household", icon: <Scissors className="h-4 w-4" />, color: "bg-gray-500" },
  { id: 10, name: "Health", icon: <Pill className="h-4 w-4" />, color: "bg-emerald-500" },
];

// Mock data for smart lists (Keep for UI structure for now)
const smartLists = [
  {
    id: 1,
    name: "Weekly Staples",
    description: "Items you buy almost every week",
    items: [
      { id: 101, name: "Milk", category: "Dairy", quantity: "1 gallon" },
      { id: 102, name: "Eggs", category: "Dairy", quantity: "1 dozen" },
      { id: 103, name: "Bread", category: "Bakery", quantity: "1 loaf" },
      { id: 104, name: "Bananas", category: "Produce", quantity: "1 bunch" },
      { id: 105, name: "Apples", category: "Produce", quantity: "6" },
    ],
  },
  {
    id: 2,
    name: "Running Low",
    description: "Items you might need to restock soon",
    items: [
      { id: 201, name: "Coffee", category: "Beverages", quantity: "1 bag" },
      { id: 202, name: "Paper Towels", category: "Household", quantity: "1 pack" },
      { id: 203, name: "Toothpaste", category: "Health", quantity: "1" },
    ],
  },
  {
    id: 3,
    name: "Forgotten Items",
    description: "Items you often forget to buy",
    items: [
      { id: 301, name: "Dish Soap", category: "Household", quantity: "1 bottle" },
      { id: 302, name: "Aluminum Foil", category: "Household", quantity: "1 roll" },
      { id: 303, name: "Trash Bags", category: "Household", quantity: "1 box" },
    ],
  },
];

// Mock data for recipe suggestions (Keep for UI structure for now)
const recipeSuggestions = [
  {
    id: 1,
    name: "Pasta Primavera",
    description: "A light and fresh pasta dish with seasonal vegetables",
    image: "/placeholder.svg?height=200&width=300",
    ingredients: ["Pasta", "Tomatoes", "Zucchini", "Bell Peppers", "Parmesan Cheese", "Olive Oil", "Garlic"],
    missingIngredients: ["Zucchini", "Bell Peppers"],
    prepTime: "25 mins",
    difficulty: "Easy",
    matchScore: 85,
  },
  {
    id: 2,
    name: "Chicken Stir Fry",
    description: "Quick and healthy stir fry with chicken and vegetables",
    image: "/placeholder.svg?height=200&width=300",
    ingredients: ["Chicken Breast", "Broccoli", "Carrots", "Soy Sauce", "Ginger", "Garlic", "Rice"],
    missingIngredients: ["Broccoli", "Ginger"],
    prepTime: "30 mins",
    difficulty: "Medium",
    matchScore: 78,
  },
  {
    id: 3,
    name: "Banana Bread",
    description: "Delicious homemade banana bread with walnuts",
    image: "/placeholder.svg?height=200&width=300",
    ingredients: ["Bananas", "Flour", "Sugar", "Eggs", "Butter", "Baking Soda", "Walnuts"],
    missingIngredients: ["Walnuts"],
    prepTime: "60 mins",
    difficulty: "Easy",
    matchScore: 92,
  },
];

// Define props type for GroceryItem
interface GroceryItemProps {
  item: GroceryItemType;
  onToggle: () => void;
  onDelete: () => void;
}

// Standard view item component
function GroceryItem({ item, onToggle, onDelete }: GroceryItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(item.name);
  const [editedQuantity, setEditedQuantity] = useState(item.quantity || "");
  const { updateGroceryItem } = useGrocery();
  const { toast } = useToast();

  const handleEdit = async () => {
    try {
      await updateGroceryItem(item._id, {
        name: editedName,
        quantity: editedQuantity || undefined
      });
      setIsEditing(false);
      toast({
        title: "Item updated",
        description: "Item details updated successfully",
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Update failed",
        description: error.message || "Failed to update item",
      });
    }
  };

  const capitalizedName = useMemo(() => {
    return item.name.charAt(0).toUpperCase() + item.name.slice(1);
  }, [item.name]);

  if (isEditing) {
    return (
      <div className="flex flex-col gap-2 p-2 rounded-md bg-secondary/30 border border-white/5">
        <div className="flex gap-2">
          <Input
            value={editedName}
            onChange={(e) => setEditedName(e.target.value)}
            className="h-8 flex-grow"
            placeholder="Item name"
          />
          <Input
            value={editedQuantity}
            onChange={(e) => setEditedQuantity(e.target.value)}
            className="h-8 w-20"
            placeholder="Qty"
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(false)}
          >
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleEdit}
          >
            Save
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-2 rounded-md bg-secondary/30 border border-white/5">
      <div className="flex items-center gap-3">
        <Checkbox
          checked={item.isChecked}
          onCheckedChange={onToggle}
          className={item.isChecked ? "data-[state=checked]:bg-green-600" : ""}
        />
        <div className="flex flex-col">
          <span className={`text-sm font-medium ${item.isChecked ? "line-through text-muted-foreground" : ""}`}>
            {capitalizedName}
            {item.quantity && (
              <span className="ml-1 text-xs text-muted-foreground">
                (Qty: {item.quantity})
              </span>
            )}
          </span>
          {item.notes && (
            <span className="text-xs text-muted-foreground">
              {item.notes}
            </span>
          )}
        </div>
      </div>
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7 text-muted-foreground hover:text-white"
          onClick={() => setIsEditing(true)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7 text-muted-foreground hover:text-white"
          onClick={onDelete}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

// Compact view item component
function GroceryItemCompact({ item, onToggle, onDelete }: GroceryItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(item.name);
  const [editedQuantity, setEditedQuantity] = useState(item.quantity || "");
  const { updateGroceryItem } = useGrocery();
  const { toast } = useToast();

  const handleEdit = async () => {
    try {
      await updateGroceryItem(item._id, {
        name: editedName,
        quantity: editedQuantity || undefined
      });
      setIsEditing(false);
      toast({
        title: "Item updated",
        description: "Item details updated successfully",
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Update failed",
        description: error.message || "Failed to update item",
      });
    }
  };

  const capitalizedName = useMemo(() => {
    return item.name.charAt(0).toUpperCase() + item.name.slice(1);
  }, [item.name]);

  if (isEditing) {
    return (
      <div className="flex flex-col gap-2 p-2 rounded-md bg-secondary/30 border border-white/5">
        <div className="flex gap-2">
          <Input
            value={editedName}
            onChange={(e) => setEditedName(e.target.value)}
            className="h-7 flex-grow text-sm"
            placeholder="Item name"
          />
          <Input
            value={editedQuantity}
            onChange={(e) => setEditedQuantity(e.target.value)}
            className="h-7 w-16 text-sm"
            placeholder="Qty"
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(false)}
            className="h-6 text-xs"
          >
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleEdit}
            className="h-6 text-xs"
          >
            Save
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-2 rounded-md bg-secondary/30 border border-white/5">
      <div className="flex items-center gap-2">
        <Checkbox
          checked={item.isChecked}
          onCheckedChange={onToggle}
          className={item.isChecked ? "data-[state=checked]:bg-green-600" : ""}
        />
        <span className={`text-sm ${item.isChecked ? "line-through text-muted-foreground" : ""}`}>
          {capitalizedName}
          {item.quantity && (
            <span className="ml-1 text-xs text-muted-foreground">
              (Qty: {item.quantity})
            </span>
          )}
        </span>
      </div>
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-white"
          onClick={() => setIsEditing(true)}
        >
          <Edit className="h-3.5 w-3.5" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-white"
          onClick={onDelete}
        >
          <Trash2 className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
}

// New component for Inline Add with Typeahead
interface InlineAddItemProps {
  category: string;
  onAdd: (name: string, category: string) => void;
  onClose: () => void;
}

// Memoized to prevent remounts and focus loss
const InlineAddItem = React.memo(function InlineAddItem({ category, onAdd, onClose }: InlineAddItemProps) {
  const [itemName, setItemName] = useState("");
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const debouncedItemName = useDebounce(itemName, 400); // Use debounce

  // Ref for the input field
  // IMPORTANT: Parent must not remount this component or change its key on every render, or input will lose focus!
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Fetch suggestions
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (debouncedItemName.trim().length < 2) {
        setSuggestions([]);
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        const results = await groceryService.searchGroceryLibrary(debouncedItemName);
        // Filter out suggestions already on the main list?
        setSuggestions(results);
      } catch (err: any) {
        setError(err.message || "Failed to load suggestions");
      } finally {
        setIsLoading(false);
      }
    };
    fetchSuggestions();
  }, [debouncedItemName]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleAdd = () => {
    if (itemName.trim()) {
      onAdd(itemName.trim(), category);
      setItemName(""); // Clear after adding
    }
  };

  const handleSelectSuggestion = (suggestion: string) => {
    onAdd(suggestion, category);
    setItemName("");
    setSuggestions([]);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleAdd();
    }
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="p-2 mt-1 mb-2 border border-green-600/50 rounded-md bg-secondary/50 shadow-sm relative">
      <Input
        ref={inputRef}
        type="text"
        placeholder={`Add to ${category}...`}
        value={itemName}
        onChange={(e) => setItemName(e.target.value)}
        onKeyDown={handleKeyDown}
        className="h-8 text-sm border-green-600/30 focus-visible:ring-green-500/50 pr-8"
        autoComplete="off"
      />
      {/* Optional: Add button inside input? */}
       <Button
         size="icon"
         variant="ghost"
         className="absolute right-2.5 top-2.5 h-5 w-5 text-green-600 hover:text-green-500"
         onClick={handleAdd}
         disabled={!itemName.trim()}
         title="Add item"
       >
         <Plus className="h-4 w-4" />
       </Button>

      {/* Suggestions List */}
      {(isLoading || error || suggestions.length > 0) && (
        <div className="mt-1.5 max-h-32 overflow-y-auto border-t border-white/10 pt-1.5">
          {isLoading && <p className="text-xs text-muted-foreground px-1">Loading suggestions...</p>}
          {error && <p className="text-xs text-red-500 px-1">Error: {error}</p>}
          {!isLoading && suggestions.map((sugg) => (
            <button
              key={sugg}
              onClick={() => handleSelectSuggestion(sugg)}
              className="block w-full text-left text-xs px-1 py-0.5 rounded-sm hover:bg-accent"
            >
              {sugg}
            </button>
          ))}
        </div>
      )}
    </div>
  );
});

export default function GroceriesPage() {
  const { toast } = useToast();
  const {
    items,
    isLoading,
    error,
    suggestions,
    isLoadingSuggestions,
    errorSuggestions,
    insights,
    isLoadingInsights,
    errorInsights,
    addGroceryItems,
    updateGroceryItem,
    deleteGroceryItem,
    fetchSuggestions,
    fetchInsights,
    updateCategories,
    currentListOwnerId, // Get current list context for shared lists
    refreshGroceries, // Get refresh function for QuickInput
  } = useGrocery();

  // State for new item name input (header)
  const [newItemName, setNewItemName] = useState("");
  const [newItemQuantity, setNewItemQuantity] = useState("");
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Create refs to maintain focus
  const newItemInputRef = React.useRef<HTMLInputElement>(null);
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  // State for header typeahead suggestions
  const [typeaheadSuggestions, setTypeaheadSuggestions] = useState<string[]>([]);
  const [isLoadingTypeahead, setIsLoadingTypeahead] = useState(false);
  const [typeaheadError, setTypeaheadError] = useState<string | null>(null);
  const [showTypeahead, setShowTypeahead] = useState(false);

  // Debounced value for header typeahead search
  const debouncedNewItemName = useDebounce(newItemName, 500); // 500ms debounce

  // State for inline add
  const [inlineAddCategory, setInlineAddCategory] = useState<string | null>(null);

  // View control states
  const [activeTab, setActiveTab] = useState<"current" | "completed" | "all">("current");
  const [viewMode, setViewMode] = useState<"standard" | "compact">("standard");
  const [sortMode, setSortMode] = useState<"category" | "alphabetical" | "frequency">("category");

  // State for updating categories
  const [isUpdatingCategories, setIsUpdatingCategories] = useState(false);

  // For SSR compatibility
  const [mounted, setMounted] = useState(false);
  useEffect(() => setMounted(true), []);

  // Effect to preserve focus when suggestions update
  useEffect(() => {
    // Save active element before any state updates
    const activeElement = document.activeElement;

    return () => {
      // If the input was focused before update and is still in the DOM, restore focus
      if (activeElement === newItemInputRef.current && document.contains(newItemInputRef.current)) {
        setTimeout(() => {
          newItemInputRef.current?.focus();
        }, 0);
      }
    };
  }, [typeaheadSuggestions, isLoadingTypeahead, typeaheadError]);

  // Fetch header typeahead suggestions when debounced input changes
  useEffect(() => {
    // Store whether input is focused before API call
    const inputIsFocused = document.activeElement === newItemInputRef.current;

    const fetchTypeahead = async () => {
      if (debouncedNewItemName.trim().length < 2) {
        setTypeaheadSuggestions([]);
        setShowTypeahead(false);
        return;
      }
      setIsLoadingTypeahead(true);
      setTypeaheadError(null);
      try {
        const results = await groceryService.searchGroceryLibrary(debouncedNewItemName);
        setTypeaheadSuggestions(results);
        setShowTypeahead(results.length > 0);
      } catch (err: any) {
        console.error("Error fetching typeahead suggestions:", err);
        setTypeaheadError(err.message || "Failed to load suggestions.");
        setShowTypeahead(false);
      } finally {
        setIsLoadingTypeahead(false);

        // If input was focused before API call, restore focus after response
        if (inputIsFocused && document.contains(newItemInputRef.current)) {
          setTimeout(() => {
            newItemInputRef.current?.focus();
          }, 0);
        }
      }
    };

    fetchTypeahead();
  }, [debouncedNewItemName]);

  // Add a new item (from header)
  const handleAddItem = async () => {
    if (!newItemName.trim()) return;

    setIsAddingItem(true);
    setShowTypeahead(false);
    try {
      await addGroceryItems([{
        name: newItemName.trim(),
        quantity: newItemQuantity.trim() || undefined,
      }]);
      setNewItemName("");
      setNewItemQuantity("");
      toast({
        title: "Item added",
        description: "Item added to your grocery list",
      });
    } catch (err: any) {
      console.error("Failed to add grocery item:", err);
      toast({
        variant: "destructive",
        title: "Failed to add item",
        description: err.message || "Could not add item. Please try again.",
      });
    } finally {
      setIsAddingItem(false);
    }
  };

  // Handle selecting a header typeahead suggestion
  const handleSelectTypeahead = (suggestion: string) => {
    setNewItemName(suggestion);
    setTypeaheadSuggestions([]);
    setShowTypeahead(false);
    // Focus quantity or add button maybe?
  };

  // Add a suggested item (AI)
  const handleAddSuggestedItem = async (suggestionName: string) => {
    // Add item using context
    try {
      await addGroceryItems([{ name: suggestionName }]);
      toast({ title: "Suggestion added", description: `${suggestionName} added.` });
    } catch (err: any) {
      toast({ variant: "destructive", title: "Failed to add suggestion", description: err.message });
    }
  };

  // Add item from inline input
  const handleAddInlineItem = async (name: string, category: string) => {
    const categoryDetails = groceryCategories.find(c => c.name === category);
    setInlineAddCategory(null); // Close inline add on submit
    try {
      await addGroceryItems([{
        name: name,
        quantity: undefined
      }]);

      // After adding the item, update its category separately
      // This assumes the item was just added and we can find it by name
      const addedItem = items.find(item =>
        item.name.toLowerCase() === name.toLowerCase() && !item.isChecked
      );

      if (addedItem) {
        await updateGroceryItem(addedItem._id, {
          category: categoryDetails ?
            { name: categoryDetails.name, color: categoryDetails.color } :
            { name: category, color: 'bg-gray-500' }
        });
      }

      toast({ title: "Item Added", description: `${name} added to ${category}.` });
    } catch (err: any) {
      toast({ variant: "destructive", title: "Add Failed", description: err.message });
    }
  };

  // Toggle item check state
  const handleToggleItem = async (id: string, currentChecked: boolean) => {
    try {
      await updateGroceryItem(id, { isChecked: !currentChecked });
    } catch (err: any) {
      console.error("Failed to update grocery item:", err);
      toast({
        variant: "destructive",
        title: "Failed to update item",
        description: err.message || "Could not update item. Please try again.",
      });
    }
  };

  // Delete an item
  const handleDeleteItem = async (id: string) => {
    try {
      await deleteGroceryItem(id);
      toast({
        title: "Item deleted",
        description: "Item removed from your grocery list",
      });
    } catch (err: any) {
      console.error("Failed to delete grocery item:", err);
      toast({
        variant: "destructive",
        title: "Failed to delete item",
        description: err.message || "Could not delete item. Please try again.",
      });
    }
  };

  // Update the pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20); // Increased default page size to 20
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  // Handler for page changes
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      // Fetching will be triggered by useEffect watching currentPage
    }
  };

  // Add this function to handle page size changes
  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Filter items by search query and active tab
  const filteredItems = useMemo(() => {
    const allItems = items || [];
    let tabFiltered;
    if (activeTab === "current") {
      tabFiltered = allItems.filter((item) => !item.isChecked);
    } else if (activeTab === "completed") {
      tabFiltered = allItems.filter((item) => item.isChecked);
    } else {
      tabFiltered = allItems;
    }
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      return tabFiltered.filter((item) =>
        item.name.toLowerCase().includes(query)
      );
    }
    return tabFiltered;
  }, [items, activeTab, searchQuery]);

  // Calculate paginated items
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    // Update total pages based on filtered items
    const calculatedTotalPages = Math.ceil(filteredItems.length / pageSize);

    // Update pagination state if it changed
    if (calculatedTotalPages !== totalPages) {
      setTotalPages(calculatedTotalPages);
      setTotalItems(filteredItems.length);

      // If current page is now invalid, reset to page 1
      if (currentPage > calculatedTotalPages && calculatedTotalPages > 0) {
        setCurrentPage(1);
      }
    }

    return filteredItems.slice(startIndex, endIndex);
  }, [filteredItems, currentPage, pageSize, totalPages]);

  // Use paginatedItems instead of filteredItems for display
  // Sort the filtered items
  const sortedItems = useMemo(() => {
    if (sortMode === "alphabetical") {
      return [...paginatedItems].sort((a, b) => a.name.localeCompare(b.name));
    } else if (sortMode === "frequency") {
      return paginatedItems;
    }
    return paginatedItems;
  }, [paginatedItems, sortMode]);

  // Group items by category
  const groupedItems = useMemo(() => {
    return sortedItems.reduce((acc, item) => {
      const category = item.category?.name ?? "Uncategorized";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(item);
      return acc;
    }, {} as Record<string, GroceryItemType[]>);
  }, [sortedItems]);

  // Get category details (icon, color)
  const getCategoryDetails = useCallback((categoryName: string) => {
    const category = groceryCategories.find((cat) => cat.name === categoryName);
    return category || { icon: <Tag className="h-4 w-4" />, color: "bg-gray-500" };
  }, []);

  // Helper to capitalize first letter
  const capitalizeFirstLetter = (str: string): string => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  // Helper to format the last shopping trip date
  const formatLastShoppingTrip = (date: Date | null): string => {
    if (!date) return "N/A";
    return formatDistanceToNow(date, { addSuffix: true });
  };

  // Function to update categories
  const handleUpdateCategories = async () => {
    setIsUpdatingCategories(true);
    try {
      await updateCategories();
    } catch (error) {
      console.error("Error updating categories:", error);
    } finally {
      setIsUpdatingCategories(false);
    }
  };

  // Function to toggle the inline add input for a specific category
  const toggleInlineAddForCategory = (categoryName: string) => {
    setInlineAddCategory(prev => prev === categoryName ? null : categoryName);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="flex-1 flex flex-col"> {/* Main container */}
      {/* Grocery List Header - Shows which list is being viewed */}
      <GroceryListHeader />

      {/* Header */}
      <header className="border-b border-white/5 sticky top-16 z-10 bg-background/80 backdrop-blur-md">
        <div className="flex items-center justify-between h-16 px-4">
          <div className="flex items-center gap-2">
            <div className="bg-green-500/20 p-1.5 rounded-md">
              <ShoppingCart className="h-5 w-5 text-green-500" />
            </div>
            <h1 className="text-2xl font-bold text-white">Groceries</h1>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search groceries..."
                className="pl-8 h-9 w-full rounded-md border border-white/5 bg-secondary/50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            {/* Add Item Input and Button with Typeahead */}
            <div className="flex items-center gap-2">
              <Popover open={showTypeahead} onOpenChange={setShowTypeahead}>
                <PopoverTrigger asChild>
                   <div className="relative flex gap-2">
                     <Input
                       ref={newItemInputRef}
                       type="text"
                       placeholder="Add an item..."
                       className="h-9 w-32 md:w-auto rounded-md border border-white/5 bg-secondary/50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                       value={newItemName}
                       onChange={(e) => {
                         setNewItemName(e.target.value);
                         // Re-open popover if user types again after selecting
                         if (e.target.value.length >= 2 && !showTypeahead) {
                           setShowTypeahead(true);
                         }
                       }}
                       onKeyDown={(e) => {
                         if (e.key === 'Enter') {
                           handleAddItem();
                           e.preventDefault(); // Prevent form submission if inside a form
                           // Keep focus on the input after adding
                           setTimeout(() => newItemInputRef.current?.focus(), 0);
                         }
                         if (e.key === 'Escape') {
                           setShowTypeahead(false);
                         }
                       }}
                       disabled={isAddingItem}
                       autoComplete="off" // Disable browser autocomplete
                       aria-autocomplete="list" // Accessibility hint
                       aria-controls="typeahead-suggestions"
                     />
                     <Input
                       type="text"
                       placeholder="Qty"
                       className="h-9 w-16 rounded-md border border-white/5 bg-secondary/50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
// ...
                       value={newItemQuantity}
                       onChange={(e) => setNewItemQuantity(e.target.value)}
                       onKeyDown={(e) => {
                         if (e.key === 'Enter') {
                           handleAddItem();
                           e.preventDefault();
                         }
                       }}
                       disabled={isAddingItem}
                     />
                   </div>
                </PopoverTrigger>
                <PopoverContent id="typeahead-suggestions" className="w-[--radix-popover-trigger-width] p-1 mt-1" align="start" onOpenAutoFocus={(e) => e.preventDefault()}>
                  {isLoadingTypeahead && (
                    <div className="p-2 text-sm text-muted-foreground flex items-center justify-center">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Loading...
                    </div>
                  )}
                  {!isLoadingTypeahead && typeaheadError && (
                    <div className="p-2 text-sm text-red-500">{typeaheadError}</div>
                  )}
                  {!isLoadingTypeahead && !typeaheadError && typeaheadSuggestions.length === 0 && debouncedNewItemName.length >= 2 && (
                    <div className="p-2 text-sm text-muted-foreground">No suggestions found.</div>
                  )}
                  {!isLoadingTypeahead && !typeaheadError && typeaheadSuggestions.length > 0 && (
                    <div className="max-h-48 overflow-y-auto">
                      {typeaheadSuggestions.map((suggestion) => (
                        <button
                          key={suggestion}
                          onClick={() => handleSelectTypeahead(suggestion)}
                          className="w-full text-left px-2 py-1.5 text-sm rounded-sm hover:bg-accent focus:outline-none focus:bg-accent"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  )}
                </PopoverContent>
              </Popover>
              <Button
                onClick={handleAddItem}
                disabled={!newItemName.trim() || isAddingItem}
                className="bg-green-600 hover:bg-green-700"
              >
                {isAddingItem ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4 mr-1" />
                )}
                Add Item
              </Button>

              {/* Settings Button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="border-white/5 bg-secondary/50 hover:bg-secondary/70"
                      asChild
                    >
                      <Link href="/settings/groceries">
                        <Settings className="h-4 w-4" />
                      </Link>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Grocery Preferences</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </header>

      {/* AI Quick Add Section */}
      <div className="border-b border-white/5 bg-background/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center gap-3">
            <div className="bg-purple-500/20 p-1.5 rounded-md">
              <Sparkles className="h-4 w-4 text-purple-500" />
            </div>
            <div className="flex-1">
              <QuickInput
                onGroceryAdded={refreshGroceries}
                currentGroceryListOwnerId={currentListOwnerId}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6 flex-1 grid grid-cols-3 gap-6">
        {/* Left column (for items) */}
        <div className="col-span-2 space-y-5">
          {/* Tab Navigation */}
          <div className="flex items-center justify-between py-2">
            <Tabs defaultValue="current" value={activeTab} onValueChange={(value) => setActiveTab(value as 'current' | 'completed' | 'all')}>
              <TabsList className="bg-secondary/30">
                <TabsTrigger value="current" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
                  Current List
                </TabsTrigger>
                <TabsTrigger value="completed" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
                  Completed
                </TabsTrigger>
                <TabsTrigger value="all" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
                  All Items
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex items-center gap-4">
              {/* View Mode Toggle */}
              <Tabs defaultValue="standard" value={viewMode} onValueChange={(value) => setViewMode(value as 'standard' | 'compact')}>
                <TabsList className="bg-secondary/30">
                  <TabsTrigger value="standard" className="data-[state=active]:bg-primary/80">
                    <ListIcon className="h-4 w-4 mr-1" />
                    Standard
                  </TabsTrigger>
                  <TabsTrigger value="compact" className="data-[state=active]:bg-primary/80">
                    <LayoutGrid className="h-4 w-4 mr-1" />
                    Compact
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Sort Dropdown */}
              <Select value={sortMode} onValueChange={setSortMode as (value: string) => void}>
                <SelectTrigger className="w-[180px] bg-secondary/30 border-white/5">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="category">
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      <span>By Category</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="alphabetical">
                    <div className="flex items-center gap-2">
                      <ArrowUpDown className="h-4 w-4" />
                      <span>Alphabetical</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="frequency">
                    <div className="flex items-center gap-2">
                      <Repeat className="h-4 w-4" />
                      <span>By Frequency</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {/* TODO: Implement Filter */}
              <Button variant="outline" size="sm" className="border-white/5 bg-secondary/50" disabled>
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          ) : error ? (
            <Card className="border-destructive/50">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Error Loading Groceries</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{error}</p>
              </CardContent>
            </Card>
          ) : filteredItems.length === 0 ? (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">No Items Found</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {searchQuery ?
                    `No items match your search "${searchQuery}".` :
                    activeTab === "completed" ?
                      "You haven't completed any items yet." :
                      "Your grocery list is empty. Add some items to get started."}
                </p>
                <div className="flex items-center gap-2 mt-4">
                   <Input
                     type="text"
                     placeholder="Add first item..."
                     value={newItemName}
                     onChange={(e) => setNewItemName(e.target.value)}
                     className="h-9 bg-secondary/50 border-white/10 focus-visible:ring-green-500/50 w-48"
                     disabled={isAddingItem}
                     onKeyDown={(e) => e.key === 'Enter' && handleAddItem()}
                   />
                   <Button
                     onClick={handleAddItem}
                     disabled={!newItemName.trim() || isAddingItem}
                   >
                     {isAddingItem ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                   </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                <CardTitle className="text-base">
                  {activeTab === "current" ? "Current List" :
                   activeTab === "completed" ? "Completed Items" : "All Items"}
                  <Badge variant="outline" className="ml-2 bg-secondary/50">
                    {filteredItems.length} item{filteredItems.length !== 1 ? 's' : ''}
                  </Badge>
                </CardTitle>

                {/* View controls (For duplicate control clarity) */}
                <div className="flex items-center gap-1">
                  <Select value={sortMode} onValueChange={setSortMode as (value: string) => void}>
                    <SelectTrigger className="w-[130px] h-8 text-xs bg-secondary/50 border-white/5">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="category">By Category</SelectItem>
                      <SelectItem value="alphabetical">Alphabetical</SelectItem>
                      <SelectItem value="frequency">By Frequency</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Show items based on sort mode */}
                {sortMode === 'category' ? (
                  <div className="space-y-4">
                    {Object.entries(groupedItems).map(([category, itemsInCategory]) => (
                      <div key={category} className="space-y-1">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-1.5">
                            <div
                              className={`p-1 rounded-full ${getCategoryDetails(category).color} h-5 w-5 flex items-center justify-center`}
                            >
                              {getCategoryDetails(category).icon}
                            </div>
                            <h3 className="font-medium text-sm">{category}</h3>
                            <Badge variant="outline" className="bg-secondary/30 border-white/5 text-xs ml-1">
                              {itemsInCategory.length}
                            </Badge>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-green-500 hover:text-green-400 hover:bg-green-500/10"
                            onClick={() => toggleInlineAddForCategory(category)} // Toggle inline add
                            title={`Add item to ${category}`}
                          >
                            <PlusCircle className="h-3.5 w-3.5" />
                          </Button>
                        </div>

                        {/* Inline Add Component */}
                        {inlineAddCategory === category && (
                          <InlineAddItem
                            category={category}
                            onAdd={handleAddInlineItem}
                            onClose={() => setInlineAddCategory(null)}
                          />
                        )}

                        {/* Display items in grid or list based on view mode */}
                        {viewMode === "compact" ? (
                          <div className="grid grid-cols-2 gap-1">
                            {itemsInCategory.map((item) => (
                              <GroceryItemCompact
                                key={item._id}
                                item={item}
                                onToggle={() => handleToggleItem(item._id, item.isChecked)}
                                onDelete={() => handleDeleteItem(item._id)}
                              />
                            ))}
                          </div>
                        ) : (
                          <div className="space-y-1">
                            {itemsInCategory.map((item) => (
                              <GroceryItem
                                key={item._id}
                                item={item}
                                onToggle={() => handleToggleItem(item._id, item.isChecked)}
                                onDelete={() => handleDeleteItem(item._id)}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  // Non-category sort (alphabetical or frequency)
                  viewMode === "compact" ? (
                    <div className="grid grid-cols-2 gap-1">
                      {sortedItems.map((item) => (
                        <GroceryItemCompact
                          key={item._id}
                          item={item}
                          onToggle={() => handleToggleItem(item._id, item.isChecked)}
                          onDelete={() => handleDeleteItem(item._id)}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {sortedItems.map((item) => (
                        <GroceryItem
                          key={item._id}
                          item={item}
                          onToggle={() => handleToggleItem(item._id, item.isChecked)}
                          onDelete={() => handleDeleteItem(item._id)}
                        />
                      ))}
                    </div>
                  )
                )}

                {/* List stats and actions */}
                <div className="flex justify-between items-center mt-5 pt-2 border-t border-white/5">
                  <div className="text-xs text-muted-foreground">
                    {(items || []).filter((item) => item.isChecked).length} of {(items || []).length} items completed
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="h-7 text-xs border-white/10" disabled>
                      <Printer className="h-3 w-3 mr-1.5" />
                      Print
                    </Button>
                    <Button variant="outline" size="sm" className="h-7 text-xs border-white/10" disabled>
                      <Share2 className="h-3 w-3 mr-1.5" />
                      Share
                    </Button>
                  </div>
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && !isLoading && (
                  <Pagination className="mt-6">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">Items per page:</span>
                        <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(parseInt(value))}>
                          <SelectTrigger className="h-8 w-[70px]">
                            <SelectValue>{pageSize}</SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="20">20</SelectItem>
                            <SelectItem value="30">30</SelectItem>
                            <SelectItem value="50">50</SelectItem>
                            <SelectItem value="100">100</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {totalItems > 0 ? (
                          <>
                            Showing {(currentPage - 1) * pageSize + 1}-
                            {Math.min(currentPage * pageSize, totalItems)} of {totalItems} items
                          </>
                        ) : (
                          "No items"
                        )}
                      </div>
                    </div>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => { e.preventDefault(); handlePageChange(currentPage - 1); }}
                          aria-disabled={currentPage <= 1}
                        />
                      </PaginationItem>

                      {/* Show first page */}
                      {totalPages > 5 && currentPage > 3 && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => { e.preventDefault(); handlePageChange(1); }}
                          >
                            1
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Show ellipsis if needed */}
                      {totalPages > 5 && currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Calculate range of pages to show */}
                      {[...Array(totalPages)].map((_, i) => {
                        const pageNumber = i + 1;
                        // Show current page and 1 page before and after
                        const shouldShowPage =
                          pageNumber === 1 ||
                          pageNumber === totalPages ||
                          Math.abs(pageNumber - currentPage) <= 1;

                        return shouldShowPage ? (
                          <PaginationItem key={i}>
                            <PaginationLink
                              href="#"
                              onClick={(e) => { e.preventDefault(); handlePageChange(pageNumber); }}
                              isActive={currentPage === pageNumber}
                              aria-current={currentPage === pageNumber ? "page" : undefined}
                            >
                              {pageNumber}
                            </PaginationLink>
                          </PaginationItem>
                        ) : null;
                      })}

                      {/* Show ellipsis if needed */}
                      {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Show last page */}
                      {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => { e.preventDefault(); handlePageChange(totalPages); }}
                          >
                            {totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => { e.preventDefault(); handlePageChange(currentPage + 1); }}
                          aria-disabled={currentPage >= totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right column (for smart lists, AI suggestions, etc.) */}
        <div className="space-y-5">
          {/* Shared Lists Browser */}
          <SharedListsBrowser />

          {/* Collaboration Panel */}
          <GroceryCollaborationPanel />

          {/* AI Suggested Items */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <Sparkles className="h-4 w-4 text-green-500" />
                  <CardTitle className="text-base">AI Suggested Items</CardTitle>
                </div>
                <Badge variant="outline" className="bg-secondary/30 border-white/5">
                  {suggestions.length}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingSuggestions ? (
                <div className="py-2 flex items-center justify-center">
                  <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                </div>
              ) : errorSuggestions ? (
                <div className="text-sm text-muted-foreground">
                  Couldn't load suggestions. {errorSuggestions}
                </div>
              ) : suggestions.length === 0 ? (
                <div className="text-sm text-muted-foreground py-2">
                  No suggestions available at the moment.
                </div>
              ) : (
                suggestions.slice(0, 5).map((suggestionName) => ( // Limit display
                  <div key={suggestionName} className="flex items-center justify-between py-1.5 border-b border-white/5 last:border-0">
                    <div className="flex items-center">
                      <div className="mr-2">
                        <Sparkles className="h-3.5 w-3.5 text-green-500" />
                      </div>
                      <span className="text-sm">{capitalizeFirstLetter(suggestionName)}</span>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-7 w-7 p-0 text-green-500 hover:text-green-400 hover:bg-green-500/10"
                      onClick={() => handleAddSuggestedItem(suggestionName)}
                      title={`Add ${suggestionName}`}
                    >
                      <PlusCircle className="h-4 w-4" />
                    </Button>
                  </div>
                ))
              )}
              <div className="pt-1 flex justify-between items-center">
                <div className="text-xs text-muted-foreground">Based on your purchase history</div>
                {/* Refresh Suggestions Button */}
                <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={fetchSuggestions} disabled={isLoadingSuggestions}>
                  {isLoadingSuggestions ? <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin"/> : <Sparkles className="h-3.5 w-3.5 mr-1.5 text-green-500" />}
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Shopping Stats */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <BarChart4 className="h-4 w-4 text-green-500" />
                  <CardTitle className="text-base">Shopping Insights</CardTitle>
                </div>
                <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={fetchInsights} disabled={isLoadingInsights}>
                  {isLoadingInsights ? <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin"/> : <Zap className="h-3.5 w-3.5 mr-1.5 text-green-500" />}
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingInsights ? (
                <div className="space-y-3 py-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-5 w-2/3" />
                  <Skeleton className="h-5 w-1/2" />
                </div>
              ) : errorInsights ? (
                 <div className="text-sm text-muted-foreground py-2">
                   Couldn't load insights. {errorInsights}
                 </div>
              ) : insights ? (
                <div className="space-y-2">
                  <div className="flex justify-between items-center py-1 border-b border-white/5">
                    <div className="flex items-center gap-2">
                      <CalendarClock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Last Shopping Trip</span>
                    </div>
                    <span className="text-sm">{formatLastShoppingTrip(insights.lastShoppingTrip)}</span>
                  </div>
                  <div className="flex justify-between items-center py-1 border-b border-white/5">
                    <div className="flex items-center gap-2">
                      <Repeat className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Most Frequent Item</span>
                    </div>
                    <span className="text-sm">{insights.mostFrequentItem || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between items-center py-1 border-b border-white/5">
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Top Category</span>
                    </div>
                    <span className="text-sm">
                      {insights.topCategory
                        ? `${insights.topCategory.name} (${insights.topCategory.percentage}%)`
                        : 'N/A'}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground py-2">
                  No insights available yet. Add more items!
                </div>
              )}
              <div className="flex justify-center mt-3">
                <Button variant="ghost" size="sm" className="text-xs" disabled>
                  <BarChart4 className="h-3.5 w-3.5 mr-1.5 text-green-500" />
                  View Detailed Analytics
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Tips */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-1.5">
                <Lightbulb className="h-4 w-4 text-amber-500" />
                <CardTitle className="text-base">Quick Tips</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <div className="p-1 rounded-full bg-amber-500/20 text-amber-500">
                    <HelpCircle className="h-3.5 w-3.5" />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Try organizing your shopping by store layout to save time.
                  </p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="p-1 rounded-full bg-amber-500/20 text-amber-500">
                    <Info className="h-3.5 w-3.5" />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Did you know? You can scan barcodes to quickly add items to your list.
                  </p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="p-1 rounded-full bg-amber-500/20 text-amber-500">
                    <Clock className="h-3.5 w-3.5" />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Shopping during off-peak hours can reduce your time in the store by up to 30%.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
