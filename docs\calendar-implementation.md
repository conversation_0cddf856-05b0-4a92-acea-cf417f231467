Okay, integrating with Google Calendar (and potentially others later) for tasks with deadlines and reminders is a powerful feature. It bridges the gap between task management and time management. This is a multi-faceted effort involving both backend and frontend work.

Here's a plan to achieve this, broken down into logical steps.

**Clarifying Questions First:**

1.  **Scope of "Sync":**
    *   **One-way or Two-way?**
        *   **One-way (App -> Calendar):** Tasks created/updated in your app appear in Google Calendar. Changes made directly in Google Calendar (e.g., rescheduling an event created by your app) *do not* sync back. This is simpler to implement initially.
        *   **Two-way (App <-> Calendar):** Changes in either system are reflected in the other. Much more complex, involves handling conflicts, webhooks, and potentially more granular permissions.
    *   **Initial Sync vs. Ongoing Sync:**
        *   When a user first connects their calendar, do we try to import existing relevant calendar events as tasks (if they fit a certain pattern)? Or only sync new tasks going forward?
        *   How often does the sync occur? Real-time, periodic (e.g., every 15 minutes), or on-demand?
2.  **Event Representation in Calendar:**
    *   How should tasks appear in Google Calendar? As "Events" or "Tasks" (Google Calendar has its own simple task list)? Events are generally more flexible for reminders and details.
    *   What information from your app's task should be in the calendar event? (Title, description, link back to your app's task, priority as color-coding perhaps?)
    *   Should reminders in your app create notifications via Google Calendar, or will your app still handle its own push notifications for reminders? (Using Google Calendar notifications is often preferred by users for consistency).
3.  **User Experience for Connecting Calendar:**
    *   Where in the app will the user initiate the Google Calendar connection? (e.g., Settings page).
    *   What permissions will we request? (Read/write to calendar is necessary for creating events).
4.  **Handling Updates and Deletions:**
    *   If a task's deadline is changed in your app, should the Google Calendar event be updated?
    *   If a task is deleted in your app, should the corresponding Google Calendar event be deleted?
    *   What if the user deletes the event directly from Google Calendar? (Relevant for two-way sync).
5.  **Multiple Calendars:** Does the user choose which of their Google Calendars to sync with, or do we sync to their primary calendar by default?
6.  **Backend vs. Frontend Sync Logic:**
    *   Will the sync be primarily managed by the backend (recommended for reliability, especially for ongoing sync and reminder processing)?
    *   Or will the frontend trigger sync operations directly after task creation/update (simpler for one-way, but less robust)?

For now, I'll assume the following for a phased approach:

*   **Phase 1 (MVP):**
    *   **One-way sync (Your App -> Google Calendar).**
    *   Tasks with deadlines/times from your app are created as **Events** in the user's primary Google Calendar.
    *   Event title = Task title.
    *   Event description = Task content + a link back to the task in your app.
    *   Event start/end time derived from task deadline/dueTime. If only a due date, create an all-day event or a default time slot.
    *   Task reminders in your app will also create Google Calendar notifications/reminders for that event.
    *   Updates to task deadline/time in your app update the Google Calendar event.
    *   Deletion of a task in your app deletes the Google Calendar event.
    *   Sync triggered on task creation/update/deletion (likely via backend).
*   **Phase 2 (Enhancements):**
    *   User choice of which calendar to sync to.
    *   Periodic background sync for robustness.
    *   Potential for two-way sync considerations.

**Implementation Plan:**

**I. Backend Setup & Authentication (Node.js/Express)**

1.  **Google Cloud Project & OAuth 2.0 Credentials:**
    *   Go to [Google Cloud Console](https://console.cloud.google.com/).
    *   Ensure your project is selected (or create one if it's different from your Firebase project).
    *   Enable the **Google Calendar API**.
    *   Go to "APIs & Services" -> "Credentials".
    *   Create "OAuth 2.0 Client IDs". Choose "Web application".
    *   **Authorized JavaScript origins:** Your frontend URL(s) (e.g., `http://localhost:3000`, your Vercel URL).
    *   **Authorized redirect URIs:** Your backend endpoint that will handle the OAuth callback (e.g., `https://your-backend-api.onrender.com/api/auth/google/callback/calendar`).
    *   Note down the **Client ID** and **Client Secret**. Store these securely in your backend's `.env` file (e.g., `GOOGLE_CALENDAR_CLIENT_ID`, `GOOGLE_CALENDAR_CLIENT_SECRET`).

2.  **Install Google API Client Library:**
    ```bash
    cd backend
    yarn add googleapis
    # or
    npm install googleapis
    ```

3.  **User Model (`User.ts`):**
    *   Add fields to store Google Calendar OAuth tokens and sync preferences:
        ```typescript
        // backend/src/models/User.ts
        // ...
        export interface IUser extends Document {
          // ... existing fields
          googleCalendarTokens?: {
            accessToken: string;
            refreshToken?: string; // Refresh token is crucial for long-term access
            expiryDate?: number; // Timestamp of when the access token expires
            scope?: string;
          };
          googleCalendarSyncEnabled?: boolean;
          googleCalendarId?: string; // ID of the calendar to sync with (e.g., 'primary')
          lastGoogleCalendarSync?: Date;
          // ...
        }

        const userSchema = new Schema<IUser>({
          // ... existing schema
          googleCalendarTokens: {
            accessToken: String,
            refreshToken: String,
            expiryDate: Number,
            scope: String,
          },
          googleCalendarSyncEnabled: { type: Boolean, default: false },
          googleCalendarId: { type: String, default: 'primary' },
          lastGoogleCalendarSync: Date,
        });
        // ...
        ```

4.  **New Auth Routes for Calendar (`routes/v2/auth.ts` or a new `calendarAuth.ts`):**
    *   **`GET /api/auth/google/calendar/authorize`:**
        *   Redirects the user to Google's OAuth consent screen.
        *   Controller constructs the Google OAuth URL using `googleapis` library, requesting `https://www.googleapis.com/auth/calendar` scope (and `https://www.googleapis.com/auth/calendar.events` for event manipulation). Include `access_type: 'offline'` to get a refresh token and `prompt: 'consent'` if you want to ensure the user re-consents and you get a new refresh token.
    *   **`GET /api/auth/google/callback/calendar`:** (This is your "Authorized redirect URI")
        *   Handles the callback from Google after user authorization.
        *   Receives an authorization `code` from Google.
        *   Exchanges this code for an access token and a refresh token using `oauth2Client.getToken(code)`.
        *   Securely stores the `accessToken`, `refreshToken`, and `expiryDate` in the `User` document for the logged-in user.
        *   Sets `googleCalendarSyncEnabled = true`.
        *   Redirects the user back to a frontend settings page (e.g., `/settings/integrations?success=google-calendar`).

5.  **Google Calendar Service (`services/googleCalendarService.ts`):**
    *   A new service to encapsulate all Google Calendar API interactions.
    *   **Constructor:** Takes an `accessToken` (and potentially `refreshToken` for auto-refresh logic).
    *   **Methods:**
        *   `_getOAuth2Client(user: IUser)`: Helper to create and configure an `OAuth2Client` instance with the user's stored tokens. It should also handle token refresh if `accessToken` is expired using the `refreshToken`.
            ```typescript
            // backend/src/services/googleCalendarService.ts
            import { google, Auth, calendar_v3 } from 'googleapis';
            import { IUser } from '../models/User'; // Assuming User model is in a shared location or imported properly
            import secureLogger from '../utils/secureLogger';

            const GOOGLE_CALENDAR_CLIENT_ID = process.env.GOOGLE_CALENDAR_CLIENT_ID;
            const GOOGLE_CALENDAR_CLIENT_SECRET = process.env.GOOGLE_CALENDAR_CLIENT_SECRET;
            // This should be your backend's callback URL
            const GOOGLE_CALENDAR_REDIRECT_URI = `${process.env.API_BASE_URL}/api/auth/google/callback/calendar`;


            class GoogleCalendarService {
                private getOAuth2Client(userTokens?: IUser['googleCalendarTokens']): Auth.OAuth2Client {
                    const oauth2Client = new google.auth.OAuth2(
                        GOOGLE_CALENDAR_CLIENT_ID,
                        GOOGLE_CALENDAR_CLIENT_SECRET,
                        GOOGLE_CALENDAR_REDIRECT_URI
                    );

                    if (userTokens?.accessToken) {
                        oauth2Client.setCredentials({
                            access_token: userTokens.accessToken,
                            refresh_token: userTokens.refreshToken,
                            expiry_date: userTokens.expiryDate,
                            scope: userTokens.scope,
                        });
                    }
                    return oauth2Client;
                }

                private async getCalendarApi(user: IUser): Promise<calendar_v3.Calendar | null> {
                    if (!user.googleCalendarTokens?.accessToken) {
                        secureLogger.warn(`[GoogleCalendarService] User ${user._id} has no Google Calendar access token.`);
                        return null;
                    }

                    const oauth2Client = this.getOAuth2Client(user.googleCalendarTokens);

                    // Handle token refresh if needed (googleapis library often does this automatically if refresh token is set)
                    // Or manually:
                    if (user.googleCalendarTokens.expiryDate && user.googleCalendarTokens.expiryDate < Date.now() + 60000) { // If expires in next minute
                        if (user.googleCalendarTokens.refreshToken) {
                            try {
                                secureLogger.log(`[GoogleCalendarService] Refreshing Google Calendar token for user ${user._id}`);
                                const { tokens } = await oauth2Client.refreshAccessToken();
                                oauth2Client.setCredentials(tokens);
                                // Update user's tokens in DB
                                user.googleCalendarTokens.accessToken = tokens.access_token!;
                                if (tokens.refresh_token) user.googleCalendarTokens.refreshToken = tokens.refresh_token;
                                user.googleCalendarTokens.expiryDate = tokens.expiry_date!;
                                await user.save();
                                secureLogger.log(`[GoogleCalendarService] Token refreshed and saved for user ${user._id}`);
                            } catch (refreshError: any) {
                                secureLogger.error(`[GoogleCalendarService] Failed to refresh Google Calendar token for user ${user._id}:`, refreshError);
                                // Potentially disable sync for this user or mark for re-auth
                                user.googleCalendarSyncEnabled = false;
                                user.googleCalendarTokens = undefined; // Clear tokens
                                await user.save();
                                return null;
                            }
                        } else {
                            secureLogger.warn(`[GoogleCalendarService] Access token expired for user ${user._id}, but no refresh token available.`);
                            // Mark for re-auth
                            user.googleCalendarSyncEnabled = false;
                            user.googleCalendarTokens = undefined;
                            await user.save();
                            return null;
                        }
                    }
                    return google.calendar({ version: 'v3', auth: oauth2Client });
                }

                // ... (createEvent, updateEvent, deleteEvent methods below)
            // ...
            ```
        *   `createEvent(user: IUser, task: ITask): Promise<string | null>`: Creates an event on Google Calendar.
            *   Takes user object (to get tokens and target calendar ID) and task object.
            *   Constructs event resource:
                *   `summary`: `task.title`
                *   `description`: `task.content` + link back to task in your app (e.g., `View in FlashTasks: ${FRONTEND_URL}/task/${task._id}`)
                *   `start` & `end`: Based on `task.deadline` and `task.dueTime`. If only date, make it an all-day event. If time, set start/end (e.g., 1-hour duration or use task duration if you add that).
                *   `reminders`: Convert your app's reminders to Google Calendar reminder overrides.
                *   `extendedProperties.private.flashTaskId`: Store `task._id` for future updates/deletions.
            *   Uses `calendar.events.insert()`.
            *   Returns the Google Calendar event ID.
        *   `updateEvent(user: IUser, googleEventId: string, task: ITask): Promise<boolean>`: Updates an existing Google Calendar event.
            *   Uses `calendar.events.update()`.
        *   `deleteEvent(user: IUser, googleEventId: string): Promise<boolean>`: Deletes an event.
            *   Uses `calendar.events.delete()`.

**6. Task Service Modifications (`services/taskService.ts` or similar):**
    *   After a task with a deadline is created or updated, if the user has `googleCalendarSyncEnabled`, call the `GoogleCalendarService` to create/update the event.
    *   Store the returned `googleEventId` in `task.metadata.googleCalendarEventId`.
    *   When a task is deleted, if it has a `googleCalendarEventId`, call `GoogleCalendarService.deleteEvent()`.

**7. `UserSettingsController (`controllers/v2/userSettingsController.ts`)**
    *   Endpoints to:
        *   `GET /api/users/settings/integrations/google-calendar`: Get current sync status and primary calendar ID.
        *   `POST /api/users/settings/integrations/google-calendar/enable`: Sets `googleCalendarSyncEnabled = true` and potentially `googleCalendarId`. (Actual token auth happens via OAuth flow).
        *   `POST /api/users/settings/integrations/google-calendar/disable`: Sets `googleCalendarSyncEnabled = false` (doesn't revoke tokens, just stops syncing).
        *   `DELETE /api/users/settings/integrations/google-calendar/disconnect`: Sets `googleCalendarSyncEnabled = false` and clears `googleCalendarTokens` and `googleCalendarId` from the user model. (Does *not* revoke Google's access token from Google's side, user must do that in their Google account settings if they want full revocation).

**II. Frontend Web App Implementation (`frontend/`)**

1.  **Settings Page for Integrations (`app/settings/integrations/page.tsx` or similar):**
    *   **UI:**
        *   "Connect Google Calendar" button.
        *   If connected: Display account email, primary calendar name/ID. "Disconnect" button. Toggle for "Sync tasks with deadlines to calendar". Dropdown to select target calendar (Phase 2).
    *   **Logic:**
        *   "Connect" button redirects to the backend OAuth initiation route (`/api/auth/google/calendar/authorize`).
        *   On successful callback (e.g., `redirect to /settings/integrations?success=google-calendar`), update UI to show "Connected".
        *   "Disconnect" button calls the backend disconnect endpoint.
        *   Toggle calls the enable/disable sync endpoint.

2.  **`lib/auth-service.ts` or a new `integration-service.ts`:**
    *   Functions to call the new backend endpoints for managing calendar sync settings.

3.  **Task Creation/Update Flow:**
    *   No direct changes needed here for Phase 1, as the backend will handle creating/updating calendar events after a task is saved.
    *   **UX Consideration:** Maybe a subtle indicator in the `NewTaskDialog` or `EditTaskDialog` if calendar sync is active for tasks with deadlines: "✓ Will be added to Google Calendar".

**III. Flutter Mobile App Implementation (`mobile/`)**

1.  **Google Sign-In for Flutter:**
    *   You already have `google_sign_in` package. This is for app authentication.
    *   For Google Calendar API access, you'll need to request additional scopes when the user initiates the calendar sync. The `googleapis` and `googleapis_auth` packages are primarily for server-to-server or Dart command-line apps. For Flutter, you'd typically use the `google_sign_in` package to get an OAuth `idToken` or `serverAuthCode` and then send that to your backend. Your backend then exchanges this for `accessToken` and `refreshToken` for the Calendar API.
    *   **Alternative for Mobile (Client-Side Access - More Complex Token Management):** Flutter packages like `googleapis` (client-side version) or directly using `flutter_appauth` with Google's OAuth endpoints for installed apps. This would mean the mobile app gets the tokens and potentially makes calendar API calls directly OR sends the tokens to your backend for storage and server-side sync. **Server-side sync initiated by your backend is generally more robust.**

    **Recommended Mobile Flow (Server-Side Sync):**
    1.  User clicks "Connect Google Calendar" in Flutter settings.
    2.  Flutter app uses `google_sign_in` to request permissions, including the Google Calendar scope (`https://www.googleapis.com/auth/calendar`).
        ```dart
        // In your Flutter settings screen
        final GoogleSignIn googleSignIn = GoogleSignIn(scopes: ['email', 'profile', 'https://www.googleapis.com/auth/calendar']);
        try {
          final GoogleSignInAccount? account = await googleSignIn.signIn();
          if (account != null) {
            final GoogleSignInAuthentication googleAuth = await account.authentication;
            // Send googleAuth.idToken OR googleAuth.serverAuthCode to your backend
            // Your backend will exchange this for calendar API access/refresh tokens
            // Example:
            // await ref.read(yourBackendServiceProvider).sendGoogleAuthCodeForCalendar(googleAuth.serverAuthCode);
            // Then update UI based on backend response.
          }
        } catch (error) {
          // Handle error
        }
        ```
    3.  Flutter app sends the `idToken` or `serverAuthCode` from `googleAuth` to a new backend endpoint (e.g., `POST /api/auth/google/calendar/connect-mobile`).
    4.  Backend exchanges this code/token for Google Calendar API `accessToken` and `refreshToken`, stores them in the `User` model, and enables sync.
    5.  This keeps the long-lived `refreshToken` on your secure backend.

2.  **Models (`mobile/.../models/user.dart`):**
    *   Update `User` model to include `googleCalendarTokens`, `googleCalendarSyncEnabled`, etc., similar to the backend model.

3.  **Services (`mobile/.../core/services/auth_service.dart`, new `settings_service.dart` or `integration_service.dart`):**
    *   Methods to call backend endpoints for enabling/disabling sync and disconnecting.

4.  **UI (`mobile/.../features/settings/screens/`)**:
    *   Create a new "Integrations" or "Connected Services" screen.
    *   "Connect Google Calendar" button.
        *   Initiates the Google Sign-In flow with calendar scopes.
        *   Sends the auth code/token to your backend.
    *   Display sync status and provide "Disconnect" option.

**IV. Task Sync Logic (Backend - `taskController.ts` or `taskService.ts`)**

This is where the actual creation/update/deletion of calendar events happens.

*   **After Task Create/Update (`taskController.ts`):**
    ```typescript
    // backend/src/controllers/taskController.ts
    // ...
    // After successfully saving or updating a task in `createTask` or `updateTask`:
    const user = await User.findById(authenticatedUserId); // Fetch the full user document
    if (user && user.googleCalendarSyncEnabled && user.googleCalendarTokens?.accessToken && savedTask.deadline) {
        const calendarService = new GoogleCalendarService(); // Or get from DI container
        try {
            if (savedTask.metadata?.googleCalendarEventId) { // If event already exists, update it
                await calendarService.updateEvent(user, savedTask.metadata.googleCalendarEventId, savedTask);
                secureLogger.log(`[TaskController] Updated Google Calendar event for task ${savedTask._id}`);
            } else { // Create new event
                const googleEventId = await calendarService.createEvent(user, savedTask);
                if (googleEventId) {
                    savedTask.metadata = { ...savedTask.metadata, googleCalendarEventId };
                    await savedTask.save(); // Save the event ID back to the task
                    secureLogger.log(`[TaskController] Created Google Calendar event ${googleEventId} for task ${savedTask._id}`);
                }
            }
        } catch (calendarError: any) {
            secureLogger.error(`[TaskController] Failed to sync task ${savedTask._id} to Google Calendar:`, calendarError);
            // Decide if this should fail the whole task operation or just log. For Phase 1, just log.
        }
    }
    // ... rest of the response
    ```

*   **After Task Delete (`taskController.ts`):**
    ```typescript
    // backend/src/controllers/taskController.ts
    // ...
    // In `deleteTask`, before sending response, after task is deleted from DB:
    if (deletedTask && deletedTask.metadata?.googleCalendarEventId) {
        const user = await User.findById(authenticatedUserId);
        if (user && user.googleCalendarSyncEnabled && user.googleCalendarTokens?.accessToken) {
            const calendarService = new GoogleCalendarService();
            try {
                await calendarService.deleteEvent(user, deletedTask.metadata.googleCalendarEventId);
                secureLogger.log(`[TaskController] Deleted Google Calendar event for task ${deletedTask._id}`);
            } catch (calendarError: any) {
                secureLogger.error(`[TaskController] Failed to delete Google Calendar event for task ${deletedTask._id}:`, calendarError);
            }
        }
    }
    // ... rest of the response
    ```

**V. Security & Best Practices**

*   **Scopes:** Only request the necessary Google Calendar scopes (e.g., `calendar.events` is usually enough for creating/managing events your app creates). Avoid overly broad scopes.
*   **Token Storage:**
    *   **Backend:** Store Google OAuth `accessToken` and `refreshToken` securely. Encrypt them at rest if possible.
    *   **Mobile:** The mobile app should *not* store these long-term calendar tokens. It should send the one-time `serverAuthCode` or `idToken` to the backend.
*   **User Consent:** Clearly explain to the user why you need calendar access and what your app will do with it.
*   **Error Handling:** Gracefully handle API errors from Google (e.g., quota limits, invalid tokens, permission issues).
*   **Revocation:** Provide a clear way for users to disconnect their Google Calendar from your app (this should clear the tokens from your DB). Inform users that they might also need to revoke access from their Google Account settings for a complete dissociation.

**Phased Rollout:**

1.  **Phase 1 (MVP):**
    *   Implement the backend OAuth flow for Google Calendar.
    *   Implement one-way sync: Task create/update/delete in your app syncs to Google Calendar.
    *   Basic settings UI on web and mobile to connect/disconnect.
    *   Backend handles all Calendar API calls using stored user tokens.
2.  **Phase 2:**
    *   Allow users to select a specific Google Calendar (not just primary).
    *   More robust error handling and UI feedback for sync status.
    *   Potentially a manual "Sync Now" button.
3.  **Phase 3 (Advanced):**
    *   Explore options for two-way sync (much more complex).
    *   Consider background sync jobs on the backend.
    *   Support for other calendar providers (Outlook, Apple Calendar).

This detailed plan should give you a strong starting point for Google Calendar integration. The key is to handle OAuth securely on the backend and make the user experience for connecting and managing the integration smooth on both frontends.