# Build and Deployment Process

## Local Development Build

For local development:

```bash
yarn
yarn build
```

This will:
1. Compile TypeScript files with `tsc`
2. Copy YAML files (including prompts) to the `dist` directory using `npx copyfiles`

## Render Deployment

For Render deployment, we use a special build script `render-build.sh` that:

1. Installs dependencies with `yarn`
2. Installs copyfiles both globally (via npm) and locally (via yarn)
3. Adds the yarn bin directory to PATH 
4. Compiles TypeScript files with `tsc`
5. Explicitly copies YAML files using `npx copyfiles`

The `.render-buildpacks.json` is configured to:

```json
{
  "packages": {
    "devDependencies": []
  },
  "buildCommand": "bash ./render-build.sh"
}
```

## Troubleshooting Common Issues

### "copyfiles not found" Error

If you see errors like:
```
$ copyfiles -u 1 "src/**/*.yaml" dist
/bin/sh: 1: copyfiles: not found
```

This means:
1. The copyfiles package is either not installed, OR
2. The executable is not in the PATH, OR
3. The shell cannot find the binary despite it being installed

Our solution handles all three issues by:
1. Installing copyfiles both globally and locally
2. Explicitly exporting the yarn bin directory to PATH
3. Using `npx` to execute the binary (which finds local node_modules)

### Path Resolution Issues

If other PATH-related issues occur:
1. Check that the `render-build.sh` file has execute permissions
2. Try using `npx` for any other binaries that aren't resolving
3. Make sure both package.json and the build script are consistent in how they reference binaries 