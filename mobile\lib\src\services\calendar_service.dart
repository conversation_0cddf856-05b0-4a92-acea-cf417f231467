import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../config/api_config.dart';
import '../models/task.dart';
import '../services/auth_service.dart';

class CalendarService {
  final Dio _dio;
  final AuthService _authService;

  CalendarService(this._dio, this._authService);

  /// Get Google Calendar authorization URL
  Future<String> getAuthUrl() async {
    try {
      final response = await _dio.get(
        '${ApiConfig.baseUrl}/api/auth/google/calendar/authorize',
        options: Options(headers: await _authService.getAuthHeaders()),
      );

      if (response.data['success'] && response.data['data']['authUrl'] != null) {
        return response.data['data']['authUrl'];
      }
      throw Exception('Failed to get authorization URL');
    } catch (e) {
      throw Exception('Failed to get authorization URL: $e');
    }
  }

  /// Disconnect Google Calendar integration
  Future<void> disconnect() async {
    try {
      await _dio.delete(
        '${ApiConfig.baseUrl}/api/auth/google/calendar',
        options: Options(headers: await _authService.getAuthHeaders()),
      );
    } catch (e) {
      throw Exception('Failed to disconnect calendar: $e');
    }
  }

  /// Check if a task is synced with calendar
  bool isTaskSynced(Task task) {
    return task.metadata?['googleCalendarEventId'] != null &&
           task.metadata?['googleCalendarSyncStatus'] == 'synced';
  }

  /// Get calendar sync status for a task
  String? getTaskSyncStatus(Task task) {
    return task.metadata?['googleCalendarSyncStatus'];
  }

  /// Get last sync time for a task
  DateTime? getTaskLastSync(Task task) {
    final lastSync = task.metadata?['googleCalendarLastSync'];
    if (lastSync != null) {
      return DateTime.parse(lastSync);
    }
    return null;
  }
}

final calendarServiceProvider = Provider<CalendarService>((ref) {
  final dio = ref.watch(dioProvider);
  final authService = ref.watch(authServiceProvider);
  return CalendarService(dio, authService);
});
