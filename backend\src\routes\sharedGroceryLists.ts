import express from 'express';
import { validate } from '../middleware/validation';
import { body, param } from 'express-validator';
import sharedGroceryListController from '../controllers/sharedGroceryListController';
import groceryController from '../controllers/groceryController';

const router = express.Router();

// Validation schemas
const createSharedListValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('List name must be between 1 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('settings.allowMemberInvites')
    .optional()
    .isBoolean()
    .withMessage('allowMemberInvites must be a boolean'),
  body('settings.requireApprovalForEdits')
    .optional()
    .isBoolean()
    .withMessage('requireApprovalForEdits must be a boolean'),
  body('settings.notifyOnChanges')
    .optional()
    .isBoolean()
    .withMessage('notifyOnChanges must be a boolean'),
];

const updateSharedListValidation = [
  param('listId')
    .isMongoId()
    .withMessage('Invalid list ID'),
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('List name must be between 1 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('settings.allowMemberInvites')
    .optional()
    .isBoolean()
    .withMessage('allowMemberInvites must be a boolean'),
  body('settings.requireApprovalForEdits')
    .optional()
    .isBoolean()
    .withMessage('requireApprovalForEdits must be a boolean'),
  body('settings.notifyOnChanges')
    .optional()
    .isBoolean()
    .withMessage('notifyOnChanges must be a boolean'),
];

const addMemberValidation = [
  param('listId')
    .isMongoId()
    .withMessage('Invalid list ID'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('role')
    .optional()
    .isIn(['editor', 'viewer'])
    .withMessage('Role must be either editor or viewer'),
];

const removeMemberValidation = [
  param('listId')
    .isMongoId()
    .withMessage('Invalid list ID'),
  param('memberId')
    .isMongoId()
    .withMessage('Invalid member ID'),
];

const listIdValidation = [
  param('listId')
    .isMongoId()
    .withMessage('Invalid list ID'),
];

const addItemsValidation = [
  param('listId')
    .isMongoId()
    .withMessage('Invalid list ID'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('Items array is required and must not be empty'),
  body('items.*.name')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Each item must have a non-empty name'),
  body('items.*.quantity')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Quantity cannot exceed 50 characters'),
  body('items.*.notes')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Notes cannot exceed 200 characters'),
  body('items.*.isChecked')
    .optional()
    .isBoolean()
    .withMessage('isChecked must be a boolean'),
];

// Routes

// GET /api/shared-grocery-lists - Get all shared lists for the user
router.get('/', sharedGroceryListController.getSharedLists);

// POST /api/shared-grocery-lists - Create a new shared grocery list
router.post('/', validate(createSharedListValidation), sharedGroceryListController.createSharedList);

// GET /api/shared-grocery-lists/:listId - Get a specific shared list with items
router.get('/:listId', validate(listIdValidation), sharedGroceryListController.getSharedList);

// PUT /api/shared-grocery-lists/:listId - Update shared list settings
router.put('/:listId', validate(updateSharedListValidation), sharedGroceryListController.updateSharedList);

// POST /api/shared-grocery-lists/:listId/members - Add member to shared list
router.post('/:listId/members', validate(addMemberValidation), sharedGroceryListController.addMember);

// DELETE /api/shared-grocery-lists/:listId/members/:memberId - Remove member from shared list
router.delete('/:listId/members/:memberId', validate(removeMemberValidation), sharedGroceryListController.removeMember);

// Note: Item management for shared lists is now handled through the main grocery routes
// with listOwnerId query parameter for accessing shared lists

export default router;
