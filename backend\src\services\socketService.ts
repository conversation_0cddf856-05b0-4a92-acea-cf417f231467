import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { Types } from 'mongoose';
import { SharedGroceryList } from '../models/SharedGroceryList';
import secureLogger from '../utils/secureLogger';

interface AuthenticatedSocket extends Socket {
  userId: Types.ObjectId;
  user: {
    id: Types.ObjectId;
    name: string;
    email: string;
  };
}

class SocketService {
  private io: SocketIOServer | null = null;
  private connectedUsers = new Map<string, Types.ObjectId>(); // socketId -> userId

  initialize(server: HTTPServer): void {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Authentication middleware
    this.io.use(async (socket: Socket, next) => {
      try {
        const token = socket.handshake.auth.token;

        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

        if (!decoded.userId) {
          return next(new Error('Invalid token'));
        }

        // Attach user info to socket
        (socket as any).userId = new Types.ObjectId(decoded.userId);
        (socket as any).user = {
          id: new Types.ObjectId(decoded.userId),
          name: decoded.name || 'Unknown',
          email: decoded.email || '<EMAIL>'
        };

        next();
      } catch (error) {
        secureLogger.error('Socket authentication failed:', error);
        next(new Error('Authentication failed'));
      }
    });

    this.io.on('connection', (socket: Socket) => {
      this.handleConnection(socket as AuthenticatedSocket);
    });

    secureLogger.log('Socket.IO service initialized');
  }

  private async handleConnection(socket: AuthenticatedSocket): Promise<void> {
    secureLogger.log(`User ${socket.user.name} connected with socket ${socket.id}`);

    // Store the connection
    this.connectedUsers.set(socket.id, socket.userId);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Join user to all their shared grocery lists
    await this.joinUserSharedLists(socket);

    // Handle grocery list events
    this.setupGroceryListHandlers(socket);

    // Handle disconnection
    socket.on('disconnect', () => {
      secureLogger.log(`User ${socket.user.name} disconnected`);
      this.connectedUsers.delete(socket.id);
    });
  }

  private async joinUserSharedLists(socket: AuthenticatedSocket): Promise<void> {
    try {
      // Find all shared lists where user is a member or owner
      const sharedLists = await SharedGroceryList.find({
        $or: [
          { owner: socket.userId },
          { 'members.userId': socket.userId }
        ],
        isActive: true
      }).select('_id');

      // Join rooms for each shared list
      for (const list of sharedLists) {
        socket.join(`grocery-list:${list._id}`);
      }

      secureLogger.log(`User ${socket.userId} joined ${sharedLists.length} shared grocery list rooms`);
    } catch (error) {
      secureLogger.error('Error joining user to shared lists:', error);
    }
  }

  private setupGroceryListHandlers(socket: AuthenticatedSocket): void {
    // Handle joining a specific grocery list room
    socket.on('join-grocery-list', async (listId: string) => {
      try {
        const list = await SharedGroceryList.findById(listId);

        if (!list || !list.canView(socket.userId)) {
          socket.emit('error', { message: 'Access denied to grocery list' });
          return;
        }

        socket.join(`grocery-list:${listId}`);
        socket.emit('joined-grocery-list', { listId });

        secureLogger.log(`User ${socket.userId} joined grocery list ${listId}`);
      } catch (error) {
        secureLogger.error('Error joining grocery list:', error);
        socket.emit('error', { message: 'Failed to join grocery list' });
      }
    });

    // Handle leaving a grocery list room
    socket.on('leave-grocery-list', (listId: string) => {
      socket.leave(`grocery-list:${listId}`);
      socket.emit('left-grocery-list', { listId });
      secureLogger.log(`User ${socket.userId} left grocery list ${listId}`);
    });

    // Handle real-time typing indicators
    socket.on('typing-grocery-item', (data: { listId: string; isTyping: boolean }) => {
      socket.to(`grocery-list:${data.listId}`).emit('user-typing-grocery-item', {
        userId: socket.userId,
        userName: socket.user.name,
        isTyping: data.isTyping
      });
    });
  }

  // Public methods for emitting events

  /**
   * Emit grocery item added event to all members of a shared list
   */
  emitGroceryItemAdded(listId: string, item: any, addedBy: Types.ObjectId): void {
    if (!this.io) return;

    this.io.to(`grocery-list:${listId}`).emit('grocery-item-added', {
      item,
      addedBy,
      timestamp: new Date()
    });
  }

  /**
   * Emit grocery item updated event to all members of a shared list
   */
  emitGroceryItemUpdated(listId: string, item: any, updatedBy: Types.ObjectId): void {
    if (!this.io) return;

    this.io.to(`grocery-list:${listId}`).emit('grocery-item-updated', {
      item,
      updatedBy,
      timestamp: new Date()
    });
  }

  /**
   * Emit grocery item deleted event to all members of a shared list
   */
  emitGroceryItemDeleted(listId: string, itemId: string, deletedBy: Types.ObjectId): void {
    if (!this.io) return;

    this.io.to(`grocery-list:${listId}`).emit('grocery-item-deleted', {
      itemId,
      deletedBy,
      timestamp: new Date()
    });
  }

  /**
   * Emit shared list updated event to all members
   */
  emitSharedListUpdated(listId: string, list: any, updatedBy: Types.ObjectId): void {
    if (!this.io) return;

    this.io.to(`grocery-list:${listId}`).emit('shared-list-updated', {
      list,
      updatedBy,
      timestamp: new Date()
    });
  }

  /**
   * Emit member added to shared list event
   */
  emitMemberAdded(listId: string, member: any, addedBy: Types.ObjectId): void {
    if (!this.io) return;

    this.io.to(`grocery-list:${listId}`).emit('member-added', {
      member,
      addedBy,
      timestamp: new Date()
    });

    // Also notify the new member directly
    this.io.to(`user:${member.userId}`).emit('added-to-shared-list', {
      listId,
      addedBy,
      timestamp: new Date()
    });
  }

  /**
   * Emit member removed from shared list event
   */
  emitMemberRemoved(listId: string, memberId: Types.ObjectId, removedBy: Types.ObjectId): void {
    if (!this.io) return;

    this.io.to(`grocery-list:${listId}`).emit('member-removed', {
      memberId,
      removedBy,
      timestamp: new Date()
    });

    // Notify the removed member directly
    this.io.to(`user:${memberId}`).emit('removed-from-shared-list', {
      listId,
      removedBy,
      timestamp: new Date()
    });
  }

  /**
   * Get connected users count for a shared list
   */
  getConnectedUsersInList(listId: string): number {
    if (!this.io) return 0;

    const room = this.io.sockets.adapter.rooms.get(`grocery-list:${listId}`);
    return room ? room.size : 0;
  }

  /**
   * Check if a user is currently connected
   */
  isUserConnected(userId: Types.ObjectId): boolean {
    if (!this.io) return false;

    const room = this.io.sockets.adapter.rooms.get(`user:${userId}`);
    return room ? room.size > 0 : false;
  }
}

// Export singleton instance
export const socketService = new SocketService();
