import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/reminder_provider.dart';

class TestReminderPage extends ConsumerStatefulWidget {
  const TestReminderPage({super.key});

  @override
  ConsumerState<TestReminderPage> createState() => _TestReminderPageState();
}

class _TestReminderPageState extends ConsumerState<TestReminderPage> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _bodyController = TextEditingController();
  DateTime _selectedDate = DateTime.now().add(const Duration(minutes: 1));
  
  @override
  void dispose() {
    _titleController.dispose();
    _bodyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final reminderState = ref.watch(remindersProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Reminders'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Reminder Title',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _bodyController,
              decoration: const InputDecoration(
                labelText: 'Reminder Body',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Scheduled Time: '),
                TextButton(
                  onPressed: _selectDateTime,
                  child: Text(
                    _selectedDate.toLocal().toString().split('.')[0],
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _scheduleReminder,
              child: const Text('Schedule Reminder'),
            ),
            const SizedBox(height: 24),
            const Text(
              'Scheduled Reminders:',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: reminderState.reminders.length,
                itemBuilder: (context, index) {
                  final reminder = reminderState.reminders[index];
                  return Card(
                    child: ListTile(
                      title: Text(reminder.title),
                      subtitle: Text(
                        '${reminder.body}\nTime: ${reminder.scheduledDate.toLocal().toString().split('.')[0]}'
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          ref.read(remindersProvider.notifier).cancelReminder(reminder.id);
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDate),
      );
      
      if (time != null) {
        setState(() {
          _selectedDate = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }
  
  void _scheduleReminder() {
    if (_titleController.text.isEmpty || _bodyController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title and body')),
      );
      return;
    }
    
    ref.read(remindersProvider.notifier).scheduleReminder(
      title: _titleController.text,
      body: _bodyController.text,
      scheduledDate: _selectedDate,
      taskId: 'test-${DateTime.now().millisecondsSinceEpoch}',
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reminder scheduled!')),
    );
    
    // Clear fields
    _titleController.clear();
    _bodyController.clear();
  }
} 