# Mobile App Address Search & Location Creation Plan

**Date:** May 22, 2025

**Goal:** Implement address search functionality in the existing mobile app's "Add/Edit Location" dialog to allow users to find locations by typing an address, similar to the web frontend, and ensure correct coordinate data format for backend communication.

**Key Component to Modify:** `mobile/flashtasks_mobile/lib/src/features/locations/widgets/add_edit_location_dialog.dart`

## Plan Details:

1.  **Enhance `AddEditLocationDialog.dart`:**
    *   **Address Search Input:**
        *   Integrate a `TextField` specifically for address search, or repurpose the existing "Address (Optional)" field to have search capabilities.
        *   On text change, use the `dio` package to call the Mapbox Geocoding API (or a similar service) to fetch address suggestions.
        *   Display these suggestions dynamically (e.g., using `ListView.builder` overlaid or appearing below the search field).
    *   **Handle Suggestion Selection:**
        *   On suggestion selection:
            *   Update the address `TextEditingController`.
            *   Update latitude and longitude state variables.
            *   Update the `FlutterMap` view (`MapController.move()`) and marker position.
    *   **Reverse Geocoding (Recommended Enhancement):**
        *   In `FlutterMap`'s `onTap` callback, after updating coordinates, call a reverse geocoding function (Mapbox API via `dio`).
        *   Update the address `TextEditingController` with the result.
    *   **UI/UX:** Ensure a smooth experience with loading indicators for API calls.

2.  **Create `GeocodingService` (e.g., `services/geocoding_service.dart`):**
    *   Encapsulate `dio` calls to the Mapbox Geocoding API:
        *   `Future<List<AddressSuggestion>> searchAddress(String query)`
        *   `Future<String?> getAddressFromCoordinates(double latitude, double longitude)`
    *   Manage the Mapbox API key securely.

3.  **Coordinate Format for Backend:**
    *   **Crucial:** In `AddEditLocationDialog.dart`'s `_submitForm()` method or in `LocationService.dart`, ensure the `coordinates` payload sent to the backend is **GeoJSON**:
        ```dart
        'coordinates': {
          'type': 'Point',
          'coordinates': [_longitude, _latitude], // Longitude first
        }
        ```
    *   Verify the mobile `Location` model correctly parses GeoJSON from backend responses.

4.  **Map Tile Layer:**
    *   Keep using OpenStreetMap tiles for now as per user preference.
        (`urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png'`)

5.  **State Management:**
    *   Use Riverpod for managing address search state (query, suggestions, loading).

6.  **Error Handling:**
    *   Implement robust error handling for geocoding API calls.

## Frontend Reference (for context):

*   The web frontend uses an `AddEditLocationDialog.tsx` which contains a `LocationMap.tsx` component.
*   `LocationMap.tsx` utilizes Mapbox GL JS for map display and the Mapbox Geocoding API for forward and reverse geocoding.
*   A `NEXT_PUBLIC_MAPBOX_TOKEN` environment variable is used for the API key.
*   The frontend `locationService.ts` sends GeoJSON formatted coordinates to the backend.