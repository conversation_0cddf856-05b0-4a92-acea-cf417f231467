import { API_ENDPOINTS } from './api-config';
import { api } from './api';
import { TaskModel } from './types/task.model';

export interface SyncResult {
  success: boolean;
  eventId?: string;
  error?: string;
  status: 'synced' | 'pending' | 'failed' | 'not_synced';
}

class CalendarSyncService {
  // Sync a task with the connected calendar
  async syncTaskWithCalendar(task: TaskModel): Promise<SyncResult> {
    try {
      const response = await api.post(`${API_ENDPOINTS.CALENDAR}/sync/task`, {
        taskId: task._id,
        title: task.title,
        description: task.content,
        startTime: task.deadline,
        endTime: task.deadline ? new Date(new Date(task.deadline).getTime() + 60 * 60 * 1000) : undefined,
        allDay: task.metadata?.isAllDay || false,
        location: task.location,
      });

      return {
        success: true,
        eventId: response.data.eventId,
        status: 'synced',
      };
    } catch (error: any) {
      console.error('Failed to sync task with calendar:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to sync with calendar',
        status: 'failed',
      };
    }
  }

  // Remove a task from the calendar
  async removeTaskFromCalendar(taskId: string, eventId?: string): Promise<SyncResult> {
    if (!eventId) {
      return { success: true, status: 'not_synced' };
    }

    try {
      await api.delete(`${API_ENDPOINTS.CALENDAR}/sync/event/${eventId}`, {
        params: { taskId }
      });

      return { success: true, status: 'not_synced' };
    } catch (error: any) {
      console.error('Failed to remove task from calendar:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to remove from calendar',
        status: 'failed',
      };
    }
  }

  // Get sync status for multiple tasks
  async getTasksSyncStatus(taskIds: string[]): Promise<Record<string, SyncResult>> {
    try {
      const response = await api.post(`${API_ENDPOINTS.CALENDAR}/sync/status/batch`, {
        taskIds
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get batch sync status:', error);
      return {};
    }
  }

  // Update sync settings
  async updateSyncSettings(settings: {
    syncEnabled: boolean;
    defaultCalendarId?: string;
    syncPastEvents?: boolean;
    syncReminders?: boolean;
  }): Promise<{ success: boolean }> {
    try {
      const response = await api.put(`${API_ENDPOINTS.CALENDAR}/sync/settings`, settings);
      return { success: response.data.success };
    } catch (error) {
      console.error('Failed to update sync settings:', error);
      return { success: false };
    }
  }
}

export const calendarSyncService = new CalendarSyncService();
