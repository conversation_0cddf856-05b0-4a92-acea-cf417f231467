import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/reminder_service.dart';

/// Provider for the ReminderService singleton
final reminderServiceProvider = Provider<ReminderService>((ref) {
  return ReminderService();
});

/// Model class for a reminder
class Reminder {
  final int id;
  final String taskId;
  final String title;
  final String body;
  final DateTime scheduledDate;
  
  Reminder({
    required this.id,
    required this.taskId,
    required this.title,
    required this.body,
    required this.scheduledDate,
  });
  
  /// Create a copy of this reminder with the given fields replaced with new values
  Reminder copyWith({
    int? id,
    String? taskId,
    String? title,
    String? body,
    DateTime? scheduledDate,
  }) {
    return Reminder(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      title: title ?? this.title,
      body: body ?? this.body,
      scheduledDate: scheduledDate ?? this.scheduledDate,
    );
  }
  
  /// Convert to a map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'taskId': taskId,
      'title': title,
      'body': body,
      'scheduledDate': scheduledDate.millisecondsSinceEpoch,
    };
  }
  
  /// Create a reminder from a map
  factory Reminder.fromMap(Map<String, dynamic> map) {
    return Reminder(
      id: map['id'],
      taskId: map['taskId'],
      title: map['title'],
      body: map['body'],
      scheduledDate: DateTime.fromMillisecondsSinceEpoch(map['scheduledDate']),
    );
  }
}

/// State class for managing reminders
class RemindersState {
  final List<Reminder> reminders;
  
  RemindersState({required this.reminders});
  
  RemindersState copyWith({List<Reminder>? reminders}) {
    return RemindersState(
      reminders: reminders ?? this.reminders,
    );
  }
}

/// StateNotifier for reminder operations
class RemindersNotifier extends StateNotifier<RemindersState> {
  final ReminderService _reminderService;
  
  RemindersNotifier(this._reminderService) : super(RemindersState(reminders: []));
  
  /// Schedule a new reminder
  Future<void> scheduleReminder({
    required String title,
    required String body,
    required DateTime scheduledDate,
    required String taskId,
  }) async {
    final id = await _reminderService.scheduleReminder(
      title: title,
      body: body,
      scheduledDate: scheduledDate,
      taskId: taskId,
    );
    
    final reminder = Reminder(
      id: id,
      taskId: taskId,
      title: title,
      body: body,
      scheduledDate: scheduledDate,
    );
    
    state = state.copyWith(
      reminders: [...state.reminders, reminder],
    );
    
    // TODO: Persist reminders to a database or local storage
  }
  
  /// Cancel a reminder by ID
  Future<void> cancelReminder(int id) async {
    await _reminderService.cancelReminder(id);
    
    state = state.copyWith(
      reminders: state.reminders.where((r) => r.id != id).toList(),
    );
    
    // TODO: Update persisted reminders
  }
  
  /// Cancel all reminders
  Future<void> cancelAllReminders() async {
    await _reminderService.cancelAllReminders();
    
    state = state.copyWith(reminders: []);
    
    // TODO: Clear persisted reminders
  }
}

/// Provider for the reminders state
final remindersProvider = StateNotifierProvider<RemindersNotifier, RemindersState>((ref) {
  final reminderService = ref.watch(reminderServiceProvider);
  return RemindersNotifier(reminderService);
}); 